package v1

import (
	"meta/app/controller"
	"meta/pkg/middleware"
	"meta/pkg/middleware/generator"

	"github.com/casbin/casbin/v2"
	"github.com/google/wire"
)

var Set = wire.NewSet(wire.Struct(new(Router), "*"))

type Router struct {
	Enf                                *casbin.Enforcer
	Casbinx                            *middleware.Casbinx
	JWT                                *middleware.JWT
	AuthApiKey                         *middleware.Apikey
	OPLog                              *middleware.OPLog
	Gen                                *generator.Gen
	CasbinRuleController               *controller.CasbinRuleController
	CleanDataController                *controller.CleanDataController
	GroupController                    *controller.GroupController
	ProtectGroupController             *controller.ProtectGroupController
	NdsController                      *controller.NdsController
	SpectrumAlertController            *controller.SpectrumAlertController
	SpectrumDataController             *controller.SpectrumDataController
	TenantController                   *controller.TenantController
	UserController                     *controller.UserController
	StrategyController                 *controller.StrategyController
	NotifyController                   *controller.NotifyController
	SocGroupController                 *controller.SocGroupController
	WofangController                   *controller.WofangController
	SocGroupTicketController           *controller.SocGroupTicketController
	SystemApiController                *controller.SystemApiController
	CloudAlertController               *controller.CloudAlertController
	CloudFlowDataController            *controller.CloudFlowDataController
	CloudAttackDataController          *controller.CloudAttackDataController
	AliCloudOriginAttackDataController *controller.AliCloudOriginAttackDataController
	WoFangAlertController              *controller.WofangAlertController
	SkylineDosController               *controller.SkylineDosController
	MatrixStrategyController           *controller.MatrixStrategyController
	MatrixSpectrumDataController       *controller.MatrixSpectrumDataController
	MatrixSpectrumAlertController      *controller.MatrixSpectrumAlertController
	UserOperationLogController         *controller.UserOperationLogController
	SystemConfigController             *controller.SystemConfigController
	DataSyncController                 *controller.DataSyncController

	// Api为直接外部接口的数据
	// Controller是内部接口
	WoFangApi  *controller.WoFangApi
	WoFangApi2 *controller.WoFangApi2
	ChartApi   *controller.ChartController
}
