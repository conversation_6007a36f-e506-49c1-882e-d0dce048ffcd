services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.local
    container_name: ddos-backend-local
    restart: unless-stopped
    networks:
      - meta-network
    ports:
      # 宿主:容器
      - "9001:9001"
    volumes:
      # Mount logs directory for easier debugging
      - ./data/logs:/app/data/logs
      # Mount config for easier configuration changes during development
      - ./resource/config_local.toml:/app/resource/config.toml:ro
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
#    command: /meta

networks:
  meta-network:
    driver: bridge
    name: meta-network
