/**
* <AUTHOR>
* @date 2023-06-07 15:23
* @description
 */

package register

import (
	"meta/app/ent"
	"meta/app/ent/systemapi"
	"meta/app/ent/user"
	"meta/pkg/checker"
	"meta/pkg/common"
	"meta/pkg/common/project"
	"meta/pkg/http"
	"strings"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
)

func (r *Runner) UpdateUserPermission() {
	ctx := r.AuthEnt.GetSAContext()
	_, m, _ := r.AuthV2.GetV2Token()
	// 查询需要根据auth更新项目的用户
	allUsers, _ := r.UserService.Dao.User.Query().Where(user.UpdateAuth(true)).All(ctx)
	for _, u := range allUsers {
		userName := u.Name
		userAuthInfo, _ := http.GetAuthV1UserInfo(userName, m)
		if userAuthInfo == nil {
			continue
		}
		groups := userAuthInfo.Groups
		projects := userAuthInfo.Projects
		go project.UpdateProjectPermission(r.Rdb, r.TenantService, r.CasbinRuleService, ctx, projects)
		go project.UpdateUserPermission(r.Rdb, r.TenantService, r.CasbinRuleService, ctx, groups, userName)

		u.UpdateAuth = false
		r.UserService.UpdateByID(ctx, u, u.ID)
	}
}

func (r *Runner) UpdateAllProjectPermission() {
	r.Logger.Sugar().Infof("更新未下线项目权限及sa用户权限")
	_, m, _ := r.NdsController.AuthV2.GetV2Token()
	projects, _ := http.GetAuthAllProject(m)
	ctx := r.AuthEnt.GetSAContext()
	// 查询，查看，查看详情
	queryViewApis, _ := r.SystemApiService.Dao.SystemApi.Query().Where(systemapi.HTTPMethod("GET"), systemapi.Sa(false), func(s *sql.Selector) {
		s.Where(sqljson.StringContains(systemapi.FieldRoles, "view"))
	}).All(ctx)
	// 编辑
	editApis, _ := r.SystemApiService.Dao.SystemApi.Query().Where(systemapi.HTTPMethod("PUT"), systemapi.Sa(false), func(s *sql.Selector) {
		s.Where(sqljson.StringContains(systemapi.FieldRoles, "edit"))
	}).All(ctx)
	allExcludeSaApis, _ := r.SystemApiService.Dao.SystemApi.Query().Where(systemapi.Sa(false)).All(ctx)
	if projects == nil {
		return
	}

	for _, v := range projects.Items {
		// 项目代号
		authProjectCode := strings.ToLower(v.Code)
		// 项目名称
		authProjectName := v.Name
		cacheKey := common.GenRedisKey("project", "auth", authProjectCode)
		var cacheProject ent.Tenant
		// redis hash => struct
		if err := r.Rdb.HGetAll(ctx, cacheKey).Scan(&cacheProject); err != nil {
			r.Logger.Sugar().Error(err)
		}

		//项目已下线
		if cacheProject.Offline {
			continue
		}

		// 缓存存在
		if cacheProject.Code != "" {
			continue
		}

		if authProjectCode == "_all" {
			continue
		}

		r.Rdb.HSet(ctx, cacheKey, map[string]string{"name": authProjectName, "code": authProjectCode})

		checker.CheckAndAddProject(r.TenantService.Dao, ctx, authProjectCode, authProjectName)

		// 增加项目所有角色
		for _, api := range allExcludeSaApis {
			if api.Roles != nil {
				roles := *api.Roles
				for _, role := range roles {
					checker.CheckAndInitPermission(r.CasbinRuleService, role, authProjectCode, api.Path, api.HTTPMethod, ctx)
				}
			}
		}

		//创建项目sa用户及权限
		saList, err := http.GetAuthProjectSaList(authProjectCode, m)
		if err != nil {
			r.Logger.Sugar().Error(err)
			continue
		}
		for _, v := range saList.Items {
			userName := v.Name
			//type | 类型。1为内部，2为外包，3为URS，4为grp帐号，7 为邮件列表，8为泡泡群，9为系统账号，10为运营账号
			if v.Type == 1 || v.Type == 2 {
				checker.CheckAndInitUser(r.UserService, ctx, userName, "", false)
				project.UpdateUserRole(queryViewApis, r.CasbinRuleService, userName, authProjectCode, ctx)
				project.UpdateUserRole(editApis, r.CasbinRuleService, userName, authProjectCode, ctx)
			}
		}
	}
}

func (r *Runner) CacheOfflineProject() {
	r.Logger.Sugar().Infof("更新auth已下线项目列表")
	_, m, _ := r.NdsController.AuthV2.GetV2Token()
	ctx := r.AuthEnt.GetSAContext()
	offlineProject, err := http.GetAuthOfflineProject(m)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	if offlineProject == nil {
		return
	}
	for _, v := range offlineProject.Items {
		authProjectCode := strings.ToLower(v.Code)
		authProjectName := v.Name
		cacheKey := common.GenRedisKey("project", "auth", authProjectCode)
		r.Rdb.HSet(ctx, cacheKey, map[string]string{"name": authProjectName, "code": authProjectCode, "offline": "true"})
	}
}
