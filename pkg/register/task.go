/**
* <AUTHOR>
* @date 2022-12-27 16:10
* @description
 */

package register

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"meta/app/controller"
	"meta/app/ent"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/protectgroup"
	"meta/app/ent/systemapi"
	"meta/app/ent/tenant"
	"meta/app/entity/config"
	"meta/app/service"
	"meta/pkg/auth"
	"meta/pkg/checker"
	"meta/pkg/common"
	"meta/pkg/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"github.com/sethvargo/go-password/password"
	"go.uber.org/zap"
)

type Runner struct {
	ProtectGroupService             *service.ProtectGroupService
	Logger                          *zap.Logger
	AuthEnt                         *auth.Entx
	AuthV2                          *http.AuthV2X
	Rdb                             *redis.Client
	UserService                     *service.UserService
	TenantService                   *service.TenantService
	CasbinRuleService               *service.CasbinRuleService
	SystemApiService                *service.SystemApiService
	CloudAlertService               *service.CloudAlertService
	CloudAttackDataService          *service.CloudAttackDataService
	AliCloudOriginAttackDataService *service.AliCloudOriginAttackDataService
	CloudFlowDataService            *service.CloudFlowDataService
	WofangService                   *service.WofangService
	SkylineDosService               *service.SkylineDosService
	NdsController                   *controller.NdsController
	MatrixStrategyService           *service.MatrixStrategyService
	MatrixSpectrumDataService       *service.MatrixSpectrumDataService
	MatrixSpectrumAlertService      *service.MatrixSpectrumAlertService
	SystemConfigService             *service.SystemConfigService
	SpectrumAlertService            *service.SpectrumAlertService
	DataSyncService                 *service.DataSyncService
}

func (r *Runner) UpdateProtectGroupTest() {
	r.Logger.Sugar().Info("update protect group data")
	ctx := r.AuthEnt.GetSAContext()
	groupList, err := r.ProtectGroupService.Dao.ProtectGroup.Query().All(ctx)
	if err != nil {
		r.Logger.Error(err.Error())
	}
	fmt.Println("groupList ", groupList)

	for _, k := range groupList {

		var ips []string
		for _, v := range *k.IPList {
			ip, err := common.IpData2SingleIp(v)
			if err != nil {
				r.Logger.Sugar().Errorf("更新防护群组ip出错，%s : %s", v, err)
				continue
			}
			fmt.Println(v)
			ips = append(ips, ip...)
		}
		// 获取防护群组的项目
		checkProject(ips, r, ctx)
		// fmt.Println("galaxyProject ", galaxyProject)
	}
}

func (r *Runner) UpdateProtectGroup() {
	r.Logger.Sugar().Info("update protect group data")
	ctx := r.AuthEnt.GetSAContext()
	groupList, err := http.GetGroupDataAll()
	r.Logger.Sugar().Infof("GKL:  groupList is % +v\n", groupList)

	if err != nil {
		r.Logger.Sugar().Errorf("NDS接口获取防护群组失败：%s", err.Error())
	}
	for _, v := range groupList {
		r.Logger.Sugar().Infof("GKL： 轮到项目 %v。\n ", v.GroupName)
		dbGroup, err := r.ProtectGroupService.Dao.ProtectGroup.Query().Where(protectgroup.GroupName(v.GroupName), protectgroup.GroupID(v.GroupId), protectgroup.Type(v.GroupType)).Only(ctx)
		if err != nil {
			r.Logger.Error(err.Error())
		}
		// 查询某个群组和Pool下ip列表
		dataResponse, err := http.GetProtectGroupIpList(v.GroupId, v.GroupType)
		if err != nil {
			r.Logger.Error(err.Error())
		}

		// 查询防护策略
		// 防护策略
		strategyResponse, err := http.GetProtectGroupStrategy(v.GroupId)
		if err != nil {
			r.Logger.Error(err.Error())
		}

		// 不存在，创建一个
		if dbGroup == nil {
			r.Logger.Sugar().Infof("GKL： 新项目发现 %v。\n ", v.GroupName)
			pGroup := &ent.ProtectGroup{GroupID: v.GroupId, GroupName: v.GroupName, Type: v.GroupType}
			if dataResponse != nil {
				var ips []string
				pGroup.IPList = &dataResponse.Data
				for _, ipOrCidr := range *pGroup.IPList {
					ip, err := common.IpData2SingleIp(ipOrCidr)
					if err != nil {
						r.Logger.Sugar().Errorf("更新防护群组ip出错，%s : %s", ipOrCidr, err)
						continue
					}
					ips = append(ips, ip...)
				}
				pGroup.ExpandIP = strings.Join(ips, "\n")
				// 获取防护群组的项目
				galaxyProject := checkProject(ips, r, ctx)
				if galaxyProject != nil {
					pGroup.TenantID = &galaxyProject.ID
					// 设置项目接入DDoS状态
					r.TenantService.Dao.Tenant.UpdateOneID(galaxyProject.ID).SetIsdefend(true).Save(ctx)
					r.Logger.Sugar().Infof("项目 %v 接入DDoS。\n ", galaxyProject.Code)
				}
			}
			if strategyResponse != nil {
				data := strategyResponse.Data
				pGroup.MonitorInfo = &data.MonitorInfo
				pGroup.DragInfo = &data.DragInfo
				pGroup.Nds4Config = &data.Nds4Config
				pGroup.Nds6Config = &data.Nds6Config
			}

			_, err := r.ProtectGroupService.Create(ctx, pGroup)
			if err != nil {
				r.Logger.Error(err.Error())
			}
			r.Logger.Sugar().Infof("创建群组 %s ip列表:\n%+v ", pGroup.GroupName, dataResponse.Data)

		} else {
			// group already exists
			// 更新防护群组ip列表
			updateOneID := r.ProtectGroupService.Dao.ProtectGroup.UpdateOneID(dbGroup.ID)
			if dataResponse != nil {
				// updateProjectFlag := false
				// if dbGroup.TenantID != nil {
				// 	id, _ := r.TenantService.QueryByID(ctx, *dbGroup.TenantID)
				// 	if id.Code == "lbc" || id.Code == "cld" {
				// 		updateProjectFlag = true
				// 	}
				// } else {
				// 	updateProjectFlag = true
				// }
				// if dbGroup.IPList != nil && !common.StringSliceEqual(*dbGroup.IPList, dataResponse.Data) || updateProjectFlag {
				if dbGroup.IPList != nil && !common.StringSliceEqual(*dbGroup.IPList, dataResponse.Data) {
					var ips []string
					ipList := &dataResponse.Data
					for _, v := range *ipList {
						ip, err := common.IpData2SingleIp(v)
						if err != nil {
							r.Logger.Sugar().Errorf("更新防护群组ip出错，%s : %s", v, err)
							continue
						}
						ips = append(ips, ip...)
					}
					updateFlag := false
					// if updateProjectFlag {
					// 	galaxyProject := checkProject(ips, r, ctx)
					// 	if galaxyProject != nil && galaxyProject.Code != "lbc" && galaxyProject.Code != "cld" {
					// 		updateOneID.SetTenantID(galaxyProject.ID)
					// 		updateFlag = true
					// 	}
					// }
					if dbGroup.IPList != nil && !common.StringSliceEqual(*dbGroup.IPList, dataResponse.Data) {
						joinIps := strings.Join(ips, "\n")
						updateOneID.SetExpandIP(joinIps)
						updateFlag = true
					}
					if updateFlag {
						updateOneID.SetIPList(ipList).Save(ctx)
						// r.Logger.Error(err.Error())
						r.Logger.Sugar().Infof("更新群组 %s ip列表:\n%+v ", dbGroup.GroupName, dataResponse.Data)
					}

				}
			}
			// 更新防护群组策略
			if strategyResponse != nil {
				data := strategyResponse.Data
				flag := false
				if dbGroup.MonitorInfo == nil || *dbGroup.MonitorInfo != data.MonitorInfo {
					updateOneID.SetMonitorInfo(&data.MonitorInfo)
					r.Logger.Sugar().Infof("更新群组 %s MonitorInfo\n%+v", dbGroup.GroupName, data.MonitorInfo)
					flag = true
				}
				if dbGroup.DragInfo == nil || *dbGroup.DragInfo != data.DragInfo {
					updateOneID.SetDragInfo(&data.DragInfo)
					r.Logger.Sugar().Infof("更新群组 %s DragInfo\n%+v", dbGroup.GroupName, data.DragInfo)
					flag = true
				}
				if dbGroup.Nds4Config == nil || *dbGroup.Nds4Config != data.Nds4Config {
					updateOneID.SetNds4Config(&data.Nds4Config)
					r.Logger.Sugar().Infof("更新群组 %s Nds4Config\n%+v", dbGroup.GroupName, data.Nds4Config)
					flag = true
				}
				if dbGroup.Nds6Config == nil || *dbGroup.Nds6Config != data.Nds6Config {
					updateOneID.SetNds6Config(&data.Nds6Config)
					r.Logger.Sugar().Infof("更新群组 %s Nds6Config\n%+v", dbGroup.GroupName, data.Nds6Config)
					flag = true
				}
				if flag {
					_, err := updateOneID.Save(ctx)
					if err != nil {
						r.Logger.Error(err.Error())
					}
				}
			}
		}
	}
}

func checkProject(ips []string, r *Runner, ctx context.Context) *ent.Tenant {
	ipsLen := len(ips)
	if ipsLen != 0 {
		i := 0
		for _, ip := range ips {
			// ip都没有获取到项目，通过网段获取（IPv4）
			if i == 10 {
				if strings.Contains(ip, ".") {
					ip = strings.Join(strings.Split(ip, ".")[:3], ".")
				}
			}
			if i == 11 {
				break
			}
			if strings.Contains(ip, "-") {
				ip = strings.Split(ip, "-")[0]
			}

			realProject := r.NdsController.ProjectCtl.GetRealProject(ctx, ip)
			if realProject != nil {
				// 需要同时新建项目
				checker.CheckAndAddProject(r.NdsController.TenantService.Dao, ctx, realProject.Code, realProject.Name)
				return realProject
			}
			i++
		}
	}
	return nil
}

func (r *Runner) RegisterSAUser() {
	ctx := r.AuthEnt.GetSAContext()
	userName := config.CFG.Stage.User
	pwd := config.CFG.Stage.Password
	if userName == "" {
		userName = "meta"
	}
	if pwd == "" {
		// Generate a pwd that is 16 characters long with 5 digits, 5 symbols,
		// allowing upper and lower case letters, disallowing repeat characters.
		res, err := password.Generate(16, 5, 5, false, false)
		if err != nil {
			pwd = fmt.Sprintf("%d%v", 20, rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(100))
		}
		pwd = res
	}
	_, create := checker.CheckAndInitUser(r.UserService, ctx, userName, pwd, true)
	if create {
		fmt.Printf("Create sa user %s:%s\n", userName, pwd)
	}
	// 设置默认项目
	var project *ent.Tenant
	queryTenant, _ := r.TenantService.Dao.Tenant.Query().Where(tenant.Name("all"), tenant.Code("all")).Only(ctx)
	if queryTenant == nil {
		project = &ent.Tenant{
			Name: "all",
			Code: "all",
		}
		r.TenantService.Create(ctx, project)
	} else {
		project = queryTenant
	}
	// 设置casbin权限
	// 角色，增加all项目的查询权限（为了前端能够显示项目而已
	checker.CheckAndInitRole(r.CasbinRuleService, userName, "query", "all", ctx)
	// 资源，增加 /* 的查询权限（其实也没必要
	checker.CheckAndInitPermission(r.CasbinRuleService, "query", "all", "/*", "GET", ctx)
}

func (r *Runner) RegisterSystemApi(routes []fiber.Route) {
	ctx := r.AuthEnt.GetSAContext()
	for _, v := range routes {
		method := v.Method
		path := v.Path
		if path == "/" || path == "/api" {
			continue
		}
		// fmt.Println(method, path)
		var name string
		split := strings.Split(path, "/")
		if len(split) > 3 {
			name = split[3]
		} else if len(split) > 1 {
			name = split[1]
		} else {
			name = path
		}

		queryApi, _ := r.SystemApiService.Dao.SystemApi.Query().Where(systemapi.Name(name), systemapi.Path(path), systemapi.HTTPMethod(method)).Only(ctx)
		publicApiFlag := false
		var roles []string
		publicGetPath := config.CFG.Stage.Api.PublicGetPath
		if method == "GET" {
			for _, v := range publicGetPath {
				if path == v {
					publicApiFlag = true
				}
			}
		}
		// 查询，查看，query,view
		if method == "GET" && !strings.HasSuffix(path, "/:id") {
			roles = []string{"query", "view"}
		}
		// 查看详情，viewDetail
		if method == "GET" && strings.HasSuffix(path, "/:id") {
			roles = []string{"viewDetail"}
		}
		// 编辑，edit
		if method == "PUT" && strings.HasSuffix(path, "/:id") {
			roles = []string{"edit"}
		}
		//删除，delete
		//&& strings.HasSuffix(path, "/:id")
		if method == "DELETE" {
			roles = []string{"delete"}
		}
		// 批量删除，bulkDelete
		if method == "POST" && strings.HasSuffix(path, "/bulk/delete") {
			roles = []string{"bulkDelete"}
		}
		// 新建，new
		if method == "POST" && !strings.HasSuffix(path, "/bulk") {
			roles = []string{"new"}
		}
		// 批量创建，bulkCreate
		if method == "POST" && strings.HasSuffix(path, "/bulk") {
			roles = []string{"bulkCreate"}
		}

		// 区分sa路由
		saFlag := false
		saPathPrefix := config.CFG.Stage.Api.SaPathPrefix
		for _, v := range saPathPrefix {
			if strings.HasPrefix(path, v) {
				if !publicApiFlag {
					saFlag = true
					break
				}
			}
		}

		if queryApi == nil {
			createApi := &ent.SystemApi{
				Name:       name,
				Path:       path,
				HTTPMethod: method,
				Public:     publicApiFlag,
				Roles:      &roles,
				Sa:         saFlag,
			}
			r.SystemApiService.Create(ctx, createApi)
		}

	}
}

func (r *Runner) LoadMatrixConfig() {
	ctx := r.AuthEnt.GetSAContext()
	matrixStrategies := config.CFG.MatrixStrategies
	for _, stra := range matrixStrategies {
		configMatrix := &ent.MatrixStrategy{
			Name:       stra.Name,
			MonitorBps: stra.MonitorBps * int64(math.Pow10(9)),
			DragBps:    stra.DragBps * int64(math.Pow10(9)),
			Region:     stra.Region,
			Isp:        stra.ISP,
			NetType:    stra.NetType,
			DragType:   stra.DragType,
		}
		ms, _ := r.MatrixStrategyService.Dao.MatrixStrategy.Query().Where(matrixstrategy.Name(configMatrix.Name), matrixstrategy.Region(configMatrix.Region), matrixstrategy.NetType(configMatrix.NetType), matrixstrategy.Isp(configMatrix.Isp)).Only(ctx)
		if ms == nil {
			configMatrix, _ = r.MatrixStrategyService.Create(ctx, configMatrix)
		} else {
			configMatrix = ms
		}
		configKey := common.GenRedisKey("matrix", configMatrix.Region, configMatrix.NetType, configMatrix.Isp)
		configExist, err := r.Rdb.Exists(ctx, configKey).Result()
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		if configExist == 0 {
			dataMap := map[string]any{}
			dataMap["id"] = configMatrix.ID
			dataMap["name"] = configMatrix.Name
			dataMap["monitorBps"] = configMatrix.MonitorBps
			dataMap["dragBps"] = configMatrix.DragBps
			if configMatrix.DragType != -1 {
				dataMap["dragType"] = configMatrix.DragType
			}
			r.Rdb.HSet(ctx, configKey, dataMap)
		}
	}
}

func (r *Runner) FixNDSAlertISPCode() {
	ctx := r.AuthEnt.GetSAContext()
	alerts, err := r.SpectrumAlertService.Dao.SpectrumAlert.Query().All(ctx)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	for _, alert := range alerts {
		ip := alert.IP
		if alert.IspCode == 0 {
			ispID, _ := r.NdsController.GetISPIDAndStrategyType(ip, ctx)
			r.SpectrumAlertService.Dao.SpectrumAlert.UpdateOneID(alert.ID).SetIspCode(ispID).Save(ctx)
		}
		time.Sleep(5000)
	}
}
