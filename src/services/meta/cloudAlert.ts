// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 CloudAlert Query 根据指定字段、时间范围查询或搜索 CloudAlert GET /api/v1/cloudalert */
export async function getCloudalert(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudalertParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudAlert[] }>('/api/v1/cloudalert', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 CloudAlert Create 创建 CloudAlert POST /api/v1/cloudalert */
export async function postCloudalert(body: API.CloudAlert, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.CloudAlert }>('/api/v1/cloudalert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 CloudAlert QueryByID 根据 ID 查询 CloudAlert GET /api/v1/cloudalert/${param0} */
export async function getCloudalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudAlert }>(`/api/v1/cloudalert/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 CloudAlert UpdateByID 根据 ID 修改 CloudAlert PUT /api/v1/cloudalert/${param0} */
export async function putCloudalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putCloudalertIdParams,
  body: API.CloudAlert,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudAlert }>(`/api/v1/cloudalert/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 CloudAlert DeleteByID 根据 ID 删除 CloudAlert DELETE /api/v1/cloudalert/${param0} */
export async function deleteCloudalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCloudalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/cloudalert/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 CloudAlert CreateBulk 批量创建 CloudAlert POST /api/v1/cloudalert/bulk */
export async function postCloudalertBulk(body: API.CloudAlert[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.CloudAlert[] }>('/api/v1/cloudalert/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 CloudAlert DeleteBulk 根据 IDs 批量删除 CloudAlert POST /api/v1/cloudalert/bulk/delete */
export async function postCloudalertBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/cloudalert/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
