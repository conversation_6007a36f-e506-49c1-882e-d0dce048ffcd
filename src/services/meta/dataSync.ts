// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 DataSync Query 根据指定字段、时间范围查询或搜索 DataSync GET /api/v1/datasync */
export async function getDatasync(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDatasyncParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.DataSync[] }>('/api/v1/datasync', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 DataSync Create 创建 DataSync POST /api/v1/datasync */
export async function postDatasync(body: API.DataSync, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.DataSync }>('/api/v1/datasync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 DataSync QueryByID 根据 ID 查询 DataSync GET /api/v1/datasync/${param0} */
export async function getDatasyncId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDatasyncIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.DataSync }>(`/api/v1/datasync/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 DataSync UpdateByID 根据 ID 修改 DataSync PUT /api/v1/datasync/${param0} */
export async function putDatasyncId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putDatasyncIdParams,
  body: API.DataSync,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.DataSync }>(`/api/v1/datasync/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 DataSync DeleteByID 根据 ID 删除 DataSync DELETE /api/v1/datasync/${param0} */
export async function deleteDatasyncId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteDatasyncIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/datasync/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 DataSync CreateBulk 批量创建 DataSync POST /api/v1/datasync/bulk */
export async function postDatasyncBulk(body: API.DataSync[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.DataSync[] }>('/api/v1/datasync/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 DataSync DeleteBulk 根据 IDs 批量删除 DataSync POST /api/v1/datasync/bulk/delete */
export async function postDatasyncBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/datasync/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
