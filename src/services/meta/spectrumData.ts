// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 SpectrumData Query 根据指定字段、时间范围查询或搜索 SpectrumData GET /api/v1/spectrumdata */
export async function getSpectrumdata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpectrumdataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SpectrumData[] }>('/api/v1/spectrumdata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 SpectrumData Create 创建 SpectrumData POST /api/v1/spectrumdata */
export async function postSpectrumdata(body: API.SpectrumData, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.SpectrumData }>('/api/v1/spectrumdata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 SpectrumData QueryByID 根据 ID 查询 SpectrumData GET /api/v1/spectrumdata/${param0} */
export async function getSpectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpectrumdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SpectrumData }>(`/api/v1/spectrumdata/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 SpectrumData UpdateByID 根据 ID 修改 SpectrumData PUT /api/v1/spectrumdata/${param0} */
export async function putSpectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putSpectrumdataIdParams,
  body: API.SpectrumData,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SpectrumData }>(`/api/v1/spectrumdata/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 SpectrumData DeleteByID 根据 ID 删除 SpectrumData DELETE /api/v1/spectrumdata/${param0} */
export async function deleteSpectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSpectrumdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/spectrumdata/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 SpectrumData CreateBulk 批量创建 SpectrumData POST /api/v1/spectrumdata/bulk */
export async function postSpectrumdataBulk(
  body: API.SpectrumData[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SpectrumData[] }>('/api/v1/spectrumdata/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 SpectrumData DeleteBulk 根据 IDs 批量删除 SpectrumData POST /api/v1/spectrumdata/bulk/delete */
export async function postSpectrumdataBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/spectrumdata/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
