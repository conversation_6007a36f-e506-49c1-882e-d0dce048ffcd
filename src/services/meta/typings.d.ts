declare namespace API {
  type Add = {
    /** 类型，牵引: qy；黑洞：hd */
    dragType?: string;
    ip?: string;
    /** 解封时长(秒)，表示多少秒后自动解封 */
    unDragSecond?: number;
  };

  type AddRequestData = {
    /** 牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两 位小数，单位Gbps */
    bandwidth?: number;
    /** 参数配置  仅[清洗上线和清洗调优且configType=1(自定义防护参数)] 有效 */
    configArgs?: string;
    /** 参数配置类型，0表示默认，1表示自定义  仅[清洗上线] 有效 */
    configType?: number;
    /** 部⻔对应的ID，必须是SOC中已有的部⻔信息 */
    departmentId?: number;
    /** 工单描述信息 */
    description?: string;
    /** 清洗方式, 0表示清洗上线，1表示清洗下线 2调优 */
    divertType?: number;
    /** 工单应急接口人 */
    emergencyContacterList?: User[];
    /** 工单跟踪者id列表 */
    followUserIdList?: number[];
    /** 牵引IP列表
如:
*******-10
*******/32
******* 一行一个，前两个代表IP段，最后一个代表单个IP */
    ipList?: string;
    /** 标题 */
    name?: string;
    /** 操作时间，时间戳，毫秒 */
    opTime?: number;
    /** 操作方式，0表示自动，1表示手动
[清洗上线] 可以设置0，1
[清洗下线、调优]只能设置1 */
    opType?: number;
    /** 产品代号，不能包含中文(非必填) */
    productAlias?: string;
    /** 产品中文名 */
    productName?: string;
    /** DOSDIVERT固定值，表示DOS清洗防护工单 */
    workOrderType?: string;
  };

  type AddResponseData = {
    code?: number;
    dataitems?: WorkOrder;
    msg?: string;
  };

  type AlertVO = {
    attackTypes?: string;
    endTime?: string;
    ip?: string;
    maxBps?: number;
    maxPps?: number;
    product?: string;
    startTime?: string;
  };

  type AttackCounter = {
    Average?: number;
    Max?: number;
    N?: number;
    Name?: string;
    Sum?: number;
    Unit?: string;
  };

  type AttackInfo = {
    /** 攻击最大bps */
    maxBps?: number;
    /** 清洗的最大bps */
    maxCleanBps?: number;
    /** 清洗的最大pps */
    maxCleanPps?: number;
    /** 攻击最大pps */
    maxPps?: number;
  };

  type CasbinRule = {
    /** Act holds the value of the "act" field. */
    act?: string;
    /** Dom holds the value of the "dom" field. */
    dom?: string;
    /** ID of the ent. */
    id?: number;
    /** Obj holds the value of the "obj" field. */
    obj?: string;
    /** Sub holds the value of the "sub" field. */
    sub?: string;
    /** Type holds the value of the "type" field. */
    type?: string;
  };

  type CleanData = {
    /** AttackFlags holds the value of the "attack_flags" field. */
    attack_flags?: number;
    /** CFilter holds the value of the "c_filter" field. */
    c_filter?: string;
    /** CFilterID holds the value of the "c_filter_id" field. */
    c_filter_id?: number;
    /** Count holds the value of the "count" field. */
    count?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the CleanDataQuery when eager-loading is set. */
    edges?: CleanDataEdges;
    /** Host holds the value of the "host" field. */
    host?: string;
    /** ID of the ent. */
    id?: number;
    /** InAckBps holds the value of the "in_ack_bps" field. */
    in_ack_bps?: number;
    /** InAckPps holds the value of the "in_ack_pps" field. */
    in_ack_pps?: number;
    /** InBps holds the value of the "in_bps" field. */
    in_bps?: number;
    /** InDNSBps holds the value of the "in_dns_bps" field. */
    in_dns_bps?: number;
    /** InDNSPps holds the value of the "in_dns_pps" field. */
    in_dns_pps?: number;
    /** InIcmpBps holds the value of the "in_icmp_bps" field. */
    in_icmp_bps?: number;
    /** InIcmpPps holds the value of the "in_icmp_pps" field. */
    in_icmp_pps?: number;
    /** InPps holds the value of the "in_pps" field. */
    in_pps?: number;
    /** InSynPps holds the value of the "in_syn_pps" field. */
    in_syn_pps?: number;
    /** InUDPBps holds the value of the "in_udp_bps" field. */
    in_udp_bps?: number;
    /** InUDPPps holds the value of the "in_udp_pps" field. */
    in_udp_pps?: number;
    /** IP holds the value of the "ip" field. */
    ip?: string;
    /** IPType holds the value of the "ip_type" field. */
    ip_type?: number;
    /** OutAckBps holds the value of the "out_ack_bps" field. */
    out_ack_bps?: number;
    /** OutAckPps holds the value of the "out_ack_pps" field. */
    out_ack_pps?: number;
    /** OutBps holds the value of the "out_bps" field. */
    out_bps?: number;
    /** OutDNSBps holds the value of the "out_dns_bps" field. */
    out_dns_bps?: number;
    /** OutDNSPps holds the value of the "out_dns_pps" field. */
    out_dns_pps?: number;
    /** OutIcmpBps holds the value of the "out_icmp_bps" field. */
    out_icmp_bps?: number;
    /** OutIcmpPps holds the value of the "out_icmp_pps" field. */
    out_icmp_pps?: number;
    /** OutPps holds the value of the "out_pps" field. */
    out_pps?: number;
    /** OutSynPps holds the value of the "out_syn_pps" field. */
    out_syn_pps?: number;
    /** OutUDPBps holds the value of the "out_udp_bps" field. */
    out_udp_bps?: number;
    /** OutUDPPps holds the value of the "out_udp_pps" field. */
    out_udp_pps?: number;
    /** SpectrumAlertID holds the value of the "spectrum_alert_id" field. */
    spectrum_alert_id?: number;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** Time holds the value of the "time" field. */
    time?: string;
  };

  type CleanDataEdges = {
    /** SpectrumAlert holds the value of the spectrum_alert edge. */
    spectrum_alert?: SpectrumAlert;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type CloudAlert = {
    /** 创建时间 */
    created_at?: string;
    /** 防护水平 */
    defence_level?: number;
    /** 清洗模式 */
    defence_mode?: number;
    /** 被攻击IP */
    dst_ip?: string;
    /** 被攻击端口 */
    dst_port?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the CloudAlertQuery when eager-loading is set. */
    edges?: CloudAlertEdges;
    /** 攻击结束时间 */
    end_time?: string;
    /** 流匹配模式 */
    flow_mode?: number;
    /** ID of the ent. */
    id?: number;
    /** 从攻击开始到攻击结束，最大的pps */
    max_attack_pps?: number;
    /** 最大攻击PPS */
    max_pps?: number;
    /** 超出阈值的报文总数 */
    overlimit_pkt_count?: number;
    /** 4层协议：6tcp，17udp */
    protocol?: number;
    /** 备注 */
    remark?: string;
    /** 来源IP */
    src_ip?: string;
    /** 来源端口 */
    src_port?: number;
    /** 攻击开始时间 */
    start_time?: string;
    /** TCP报文中的ACK值 */
    tcp_ack_num?: string;
    /** TCP报文中的SEQ值 */
    tcp_seq_num?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type CloudAlertEdges = {
    /** CloudflowDatas holds the value of the cloudflow_datas edge. */
    cloudflow_datas?: CloudFlowData[];
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type CloudAttackData = {
    /** 创建时间 */
    created_at?: string;
    /** 这一秒pps */
    current_attack_pps?: number;
    /** 被攻击IP */
    dst_ip?: string;
    /** 被攻击端口 */
    dst_port?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the CloudAttackDataQuery when eager-loading is set. */
    edges?: CloudAttackDataEdges;
    /** 结束时间 */
    end_time?: string;
    /** ID of the ent. */
    id?: number;
    /** 4层协议：6tcp，17udp */
    protocol?: number;
    /** 备注 */
    remark?: string;
    /** 来源IP */
    src_ip?: string;
    /** 来源端口 */
    src_port?: number;
    /** 攻击开始时间 */
    start_time?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type CloudAttackDataEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type CloudFlowData = {
    /** CloudAlertID holds the value of the "cloud_alert_id" field. */
    cloud_alert_id?: number;
    /** 创建时间 */
    created_at?: string;
    /** 被攻击IP */
    dst_ip?: string;
    /** 被攻击端口 */
    dst_port?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the CloudFlowDataQuery when eager-loading is set. */
    edges?: CloudFlowDataEdges;
    /** 上一个报文到达的时间 */
    end_time?: string;
    /** 总pps>1000的次数 */
    flow_over_max_pps_count?: number;
    /** ID of the ent. */
    id?: number;
    /** 从攻击开始到攻击结束，最大的pps */
    max_attack_pps?: number;
    /** 4层协议：6tcp，17udp */
    protocol?: number;
    /** 备注 */
    remark?: string;
    /** 来源IP */
    src_ip?: string;
    /** 来源端口 */
    src_port?: number;
    /** 攻击开始时间 */
    start_time?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type CloudFlowDataEdges = {
    /** CloudAlert holds the value of the cloud_alert edge. */
    cloud_alert?: CloudAlert;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type DataSync = {
    /** 创建时间 */
    created_at?: string;
    /** DataList holds the value of the "data_list" field. */
    data_list?: string[];
    /** DataType holds the value of the "data_type" field. */
    data_type?: string;
    /** ID of the ent. */
    id?: number;
    /** PreDataList holds the value of the "pre_data_list" field. */
    pre_data_list?: string[];
    /** 备注 */
    remark?: string;
    /** Type holds the value of the "type" field. */
    type?: string;
    /** 更新时间 */
    updated_at?: string;
  };

  type deleteCasbinruleIdParams = {
    /** CasbinRule ID */
    id: number;
  };

  type deleteCleandataIdParams = {
    /** CleanData ID */
    id: number;
  };

  type deleteCloudalertIdParams = {
    /** CloudAlert ID */
    id: number;
  };

  type deleteCloudattackdataIdParams = {
    /** CloudAttackData ID */
    id: number;
  };

  type deleteCloudflowdataIdParams = {
    /** CloudFlowData ID */
    id: number;
  };

  type deleteDatasyncIdParams = {
    /** DataSync ID */
    id: number;
  };

  type deleteGroupIdParams = {
    /** Group ID */
    id: number;
  };

  type deleteHellouserIdParams = {
    /** HelloUser ID */
    id: number;
  };

  type DeleteItem = {
    ids?: number[];
  };

  type deleteMatrixspectrumalertIdParams = {
    /** MatrixSpectrumAlert ID */
    id: number;
  };

  type deleteMatrixspectrumdataIdParams = {
    /** MatrixSpectrumData ID */
    id: number;
  };

  type deleteMatrixstrategyIdParams = {
    /** MatrixStrategy ID */
    id: number;
  };

  type deleteNotifyIdParams = {
    /** Notify ID */
    id: number;
  };

  type deleteProtectgroupIdParams = {
    /** Group ID */
    id: number;
  };

  type deleteSkylinedosIdParams = {
    /** SkylineDos ID */
    id: number;
  };

  type deleteSocgroupticketIdParams = {
    /** SocGroupTicket ID */
    id: number;
  };

  type deleteSpectrumalertIdParams = {
    /** SpectrumAlert ID */
    id: number;
  };

  type deleteSpectrumdataIdParams = {
    /** SpectrumData ID */
    id: number;
  };

  type deleteStrategyIdParams = {
    /** Strategy ID */
    id: number;
  };

  type deleteSystemapiIdParams = {
    /** SystemApi ID */
    id: number;
  };

  type deleteSystemconfigIdParams = {
    /** SystemConfig ID */
    id: number;
  };

  type deleteTenantIdParams = {
    /** Tenant ID */
    id: number;
  };

  type deleteUserIdParams = {
    /** User ID */
    id: number;
  };

  type deleteUseroperationlogIdParams = {
    /** UserOperationLog ID */
    id: number;
  };

  type deleteWofangalertIdParams = {
    /** WofangAlert ID */
    id: number;
  };

  type deleteWofangApi2IpParams = {
    /** 需要删除的ip */
    ip: string;
  };

  type deleteWofangApiDragTypeIpParams = {
    /** 牵引类型 */
    dragType: string;
    /** 需要删除的ip */
    ip: string;
  };

  type deleteWofangIdParams = {
    /** Wofang ID */
    id: number;
  };

  type DragInfo = {
    /** 是否自动牵引 */
    autoDrag?: boolean;
    /** 是否自动回迁 */
    autoUnDrag?: boolean;
  };

  type getCasbinruleIdParams = {
    /** CasbinRule ID */
    id: number;
  };

  type getCasbinruleParams = {
    /** NetType */
    NetType?: string;
    /** Sub */
    Sub?: string;
    /** Dom */
    Dom?: string;
    /** Obj */
    Obj?: string;
    /** Act */
    Act?: string;
    /** V4 */
    V4?: string;
    /** V5 */
    V5?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getChartSpectrumParams = {
    /** ip */
    IP?: string;
    /** start_time */
    startTime?: string;
    /** end_time */
    endTime?: string;
  };

  type getCleandataIdParams = {
    /** CleanData ID */
    id: number;
  };

  type getCleandataParams = {
    /** ip */
    IP?: string;
    /** created_at */
    created_at?: string;
    /** Time */
    Time?: string;
    /** InBps */
    InBps?: number;
    /** OutBps */
    OutBps?: number;
    /** InPps */
    InPps?: number;
    /** OutPps */
    OutPps?: number;
    /** InAckPps */
    InAckPps?: number;
    /** OutAckPps */
    OutAckPps?: number;
    /** InAckBps */
    InAckBps?: number;
    /** OutAckBps */
    OutAckBps?: number;
    /** InSynPps */
    InSynPps?: number;
    /** OutSynPps */
    OutSynPps?: number;
    /** InUdpPps */
    InUdpPps?: number;
    /** OutUdpPps */
    OutUdpPps?: number;
    /** InUdpBps */
    InUdpBps?: number;
    /** OutUdpBps */
    OutUdpBps?: number;
    /** InIcmpPps */
    InIcmpPps?: number;
    /** InIcmpBps */
    InIcmpBps?: number;
    /** OutIcmpBps */
    OutIcmpBps?: number;
    /** OutIcmpPps */
    OutIcmpPps?: number;
    /** InDnsPps */
    InDnsPps?: number;
    /** OutDnsPps */
    OutDnsPps?: number;
    /** InDnsBps */
    InDnsBps?: number;
    /** OutDnsBps */
    OutDnsBps?: number;
    /** AttackFlags */
    AttackFlags?: number;
    /** Count */
    Count?: number;
    /** IpType */
    IpType?: number;
    /** FFilter */
    FFilter?: string;
    /** Host */
    Host?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getCloudalertIdParams = {
    /** CloudAlert ID */
    id: number;
  };

  type getCloudalertParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** src_ip */
    src_ip?: string;
    /** src_port */
    src_port?: number;
    /** dst_ip */
    dst_ip?: string;
    /** dst_port */
    dst_port?: number;
    /** defence_mode */
    defence_mode?: number;
    /** flow_mode */
    flow_mode?: number;
    /** tcp_ack_num */
    tcp_ack_num?: string;
    /** tcp_seq_num */
    tcp_seq_num?: string;
    /** protocol */
    protocol?: number;
    /** defence_level */
    defence_level?: string;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getCloudattackdataIdParams = {
    /** CloudAttackData ID */
    id: number;
  };

  type getCloudattackdataParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** src_ip */
    src_ip?: string;
    /** src_port */
    src_port?: number;
    /** dst_ip */
    dst_ip?: string;
    /** dst_port */
    dst_port?: number;
    /** protocol */
    protocol?: number;
    /** current_attack_pps */
    current_attack_pps?: number;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getCloudflowdataIdParams = {
    /** CloudFlowData ID */
    id: number;
  };

  type getCloudflowdataParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** src_ip */
    src_ip?: string;
    /** src_port */
    src_port?: number;
    /** dst_ip */
    dst_ip?: string;
    /** dst_port */
    dst_port?: number;
    /** protocol */
    protocol?: number;
    /** count */
    count?: number;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getDatasyncIdParams = {
    /** DataSync ID */
    id: number;
  };

  type getDatasyncParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** net_type */
    net_type?: string;
    /** type */
    type?: string;
    /** region */
    region?: string;
    /** source */
    source?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getGroupIdParams = {
    /** Group ID */
    id: number;
  };

  type getGroupParams = {
    /** Name */
    Name?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getHellouserIdParams = {
    /** HelloUser ID */
    id: number;
  };

  type getHellouserParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** age */
    age?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getMatrixspectrumalertAttackingParams = {
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getMatrixspectrumalertIdParams = {
    /** MatrixSpectrumAlert ID */
    id: number;
  };

  type getMatrixspectrumalertParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** ip */
    ip?: string;
    /** device_name */
    device_name?: string;
    /** interface */
    interface?: string;
    /** protect_type */
    protect_type?: string;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** attack_type */
    attack_type?: string;
    /** bps */
    bps?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getMatrixspectrumdataIdParams = {
    /** MatrixSpectrumData ID */
    id: number;
  };

  type getMatrixspectrumdataParams = {
    /** device_ip */
    device_ip?: string;
    /** device_name */
    device_name?: string;
    /** interface */
    interface?: string;
    /** bps */
    bps?: number;
    /** time */
    time?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getMatrixstrategyIdParams = {
    /** MatrixStrategy ID */
    id: number;
  };

  type getMatrixstrategyParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** device */
    device?: string;
    /** monitor_bps */
    monitor_bps?: number;
    /** drag_bps */
    drag_bps?: number;
    /** drag_type */
    drag_type?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getNdsAlertParams = {
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
  };

  type getNdsCleandataParams = {
    /** ip */
    ip: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
  };

  type getNdsSpectrumdataParams = {
    /** ip */
    ip: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
  };

  type getNotifyIdParams = {
    /** Notify ID */
    id: number;
  };

  type getNotifyParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** popo */
    popo?: boolean;
    /** email */
    email?: boolean;
    /** sms */
    sms?: boolean;
    /** phone */
    phone?: boolean;
    /** system */
    system?: boolean;
    /** enabled */
    enabled?: boolean;
    /** sa_notify_popo */
    sa_notify_popo?: boolean;
    /** sa_notify_email */
    sa_notify_email?: boolean;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getProtectgroupIdParams = {
    /** Group ID */
    id: number;
  };

  type getProtectgroupParams = {
    /** Remark */
    Remark?: string;
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** GroupName */
    GroupName?: string;
    /** NetType */
    NetType?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSkylinedosIdParams = {
    /** SkylineDos ID */
    id: number;
  };

  type getSkylinedosParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** region */
    region?: string;
    /** resource */
    resource?: string;
    /** resource_type */
    resource_type?: string;
    /** status */
    status?: string;
    /** project */
    project?: string;
    /** duration_time */
    duration_time?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSocgroupticketIdParams = {
    /** SocGroupTicket ID */
    id: number;
  };

  type getSocgroupticketParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSocgroupTicketParams = {
    /** startTime */
    startTime: string;
    /** 偏移值 */
    offset: number;
  };

  type getSpectrumalertAttackingParams = {
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSpectrumalertIdParams = {
    /** SpectrumAlert ID */
    id: number;
  };

  type getSpectrumalertParams = {
    /** Remark */
    Remark?: string;
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** IP */
    IP?: string;
    /** StartTime */
    StartTime?: string;
    /** EndTime */
    EndTime?: string;
    /** AttackType */
    AttackType?: string;
    /** Source */
    Source?: string;
    /** MaxPps */
    MaxPps?: string;
    /** MaxBps */
    MaxBps?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSpectrumdataIdParams = {
    /** SpectrumData ID */
    id: number;
  };

  type getSpectrumdataParams = {
    /** IP */
    IP?: string;
    /** created_at */
    created_at?: string;
    /** Time */
    Time?: string;
    /** DataType */
    DataType?: number;
    /** MatrixBps */
    MatrixBps?: number;
    /** MatrixPps */
    MatrixPps?: number;
    /** SynBps */
    SynBps?: number;
    /** SynPps */
    SynPps?: number;
    /** AckBps */
    AckBps?: number;
    /** AckPps */
    AckPps?: number;
    /** SynAckBps */
    SynAckBps?: number;
    /** SynAckPps */
    SynAckPps?: number;
    /** IcmpBps */
    IcmpBps?: number;
    /** IcmpPps */
    IcmpPps?: number;
    /** SmallPps */
    SmallPps?: number;
    /** NtpPps */
    NtpPps?: number;
    /** NtpBps */
    NtpBps?: number;
    /** DnsQueryPps */
    DnsQueryPps?: number;
    /** DnsQueryBps */
    DnsQueryBps?: number;
    /** DnsAnswerPps */
    DnsAnswerPps?: number;
    /** DnsAnswerBps */
    DnsAnswerBps?: number;
    /** SsdpBps */
    SsdpBps?: number;
    /** SsdpPps */
    SsdpPps?: number;
    /** UdpPps */
    UdpPps?: number;
    /** UdpBps */
    UdpBps?: number;
    /** QPS */
    QPS?: number;
    /** ReceiveCount */
    ReceiveCount?: number;
    /** IpType */
    IpType?: number;
    /** Monitor */
    Monitor?: string;
    /** Product */
    Product?: string;
    /** Host */
    Host?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getStrategyIdParams = {
    /** Strategy ID */
    id: number;
  };

  type getStrategyParams = {
    /** Remark */
    Remark?: string;
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** Name */
    Name?: string;
    /** NetType */
    NetType?: string;
    /** Base */
    Base?: boolean;
    /** MatrixBps */
    MatrixBps?: number;
    /** MatrixPps */
    MatrixPps?: number;
    /** BpsCount */
    BpsCount?: number;
    /** PpsCount */
    PpsCount?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSystemapiIdParams = {
    /** SystemApi ID */
    id: number;
  };

  type getSystemapiParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** path */
    path?: string;
    /** http_method */
    http_method?: string;
    /** public */
    public?: boolean;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getSystemconfigIdParams = {
    /** SystemConfig ID */
    id: number;
  };

  type getSystemconfigParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** wofang_test_ip */
    wofang_test_ip?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getTenantIdParams = {
    /** Tenant ID */
    id: number;
  };

  type getTenantParams = {
    /** Name */
    Name?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getUserIdParams = {
    /** User ID */
    id: number;
  };

  type getUseroperationlogIdParams = {
    /** UserOperationLog ID */
    id: number;
  };

  type getUseroperationlogParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** username */
    username?: string;
    /** method */
    method?: string;
    /** uri */
    uri?: string;
    /** request_body */
    request_body?: string;
    /** project */
    project?: string;
    /** time */
    time?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getUserParams = {
    /** Name */
    Name?: string;
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** Password */
    Password?: string;
    /** SuperAdmin */
    SuperAdmin?: boolean;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getWofangalertIdParams = {
    /** WofangAlert ID */
    id: number;
  };

  type getWofangalertParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** attack_status */
    attack_status?: string;
    /** attack_type */
    attack_type?: string;
    /** device_ip */
    device_ip?: string;
    /** zone_ip */
    zone_ip?: string;
    /** start_time */
    start_time?: string;
    /** end_time */
    end_time?: string;
    /** max_drop_kbps */
    max_drop_kbps?: number;
    /** max_in_kbps */
    max_in_kbps?: number;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type getWofangApi2Params = {
    /** 查询ip */
    ips: string;
  };

  type getWofangApiParams = {
    /** 牵引类型：hd | qy */
    dragType: string;
    /** 查询ip */
    ips: string;
  };

  type getWofangIdParams = {
    /** Wofang ID */
    id: number;
  };

  type getWofangParams = {
    /** created_at */
    created_at?: string;
    /** updated_at */
    updated_at?: string;
    /** remark */
    remark?: string;
    /** name */
    name?: string;
    /** ip */
    ip?: string;
    /** type */
    type?: string;
    /** un_drag_second */
    un_drag_second?: number;
    /** api_response */
    api_response?: string;
    /** status */
    status?: string;
    /** 需要搜索的值，多个值英文逗号,分隔 */
    search?: string;
    /** 当前页 */
    current?: number;
    /** 分页大小 */
    pageSize?: number;
    /** 排序，默认id逆序(-id) */
    order?: string;
  };

  type Group = {
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the GroupQuery when eager-loading is set. */
    edges?: GroupEdges;
    /** ID of the ent. */
    id?: number;
    /** Name holds the value of the "name" field. */
    name?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
  };

  type GroupCleanData = {
    attackFlags?: number;
    count?: number;
    filter?: string;
    filterId?: number;
    host?: string;
    inAckBps?: number;
    inAckPps?: number;
    inBps?: number;
    inDnsBps?: number;
    inDnsPps?: number;
    inIcmpBps?: number;
    inIcmpPps?: number;
    inPps?: number;
    inSynPps?: number;
    inUdpBps?: number;
    inUdpPps?: number;
    ip?: string;
    ipType?: number;
    outAckBps?: number;
    outAckPps?: number;
    outBps?: number;
    outDnsBps?: number;
    outDnsPps?: number;
    outIcmpBps?: number;
    outIcmpPps?: number;
    outPps?: number;
    outSynPps?: number;
    outUdpBps?: number;
    outUdpPps?: number;
    time?: number;
  };

  type GroupCleanDataResponse = {
    code?: number;
    data?: GroupCleanData[];
    msg?: string;
  };

  type GroupEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
    /** Users holds the value of the users edge. */
    users?: User[];
  };

  type GroupSpectrumData = {
    ackBps?: number;
    ackPps?: number;
    bps?: number;
    dataType?: number;
    dnsAnswerBps?: number;
    dnsAnswerPps?: number;
    dnsQueryBps?: number;
    dnsQueryPps?: number;
    host?: string;
    icmpBps?: number;
    icmpPps?: number;
    ip?: string;
    ipType?: number;
    monitor?: string;
    monitorId?: number;
    ntpBps?: number;
    ntpPps?: number;
    pps?: number;
    product?: string;
    qps?: number;
    receviceCount?: number;
    smallPps?: number;
    ssdpBps?: number;
    ssdpPps?: number;
    synAckBps?: number;
    synAckPps?: number;
    synBps?: number;
    synPps?: number;
    time?: number;
    udpBps?: number;
    udpPps?: number;
  };

  type GroupSpectrumDataResponse = {
    code?: number;
    data?: GroupSpectrumData[];
    msg?: string;
  };

  type GroupUpdate = {
    alertId?: number;
    groupId?: number;
    ips?: string[];
    type?: number;
  };

  type HelloUser = {
    /** Age holds the value of the "age" field. */
    age?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the HelloUserQuery when eager-loading is set. */
    edges?: HelloUserEdges;
    /** ID of the ent. */
    id?: number;
    /** Name holds the value of the "name" field. */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type HelloUserEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type LineData = {
    name?: string;
    time?: string;
    value?: number;
  };

  type MatrixAttackInfo = {
    /** 小于监控bps阈值次数 */
    lowerMonitorBpsCount?: number;
    /** 攻击最大bps */
    maxBps?: number;
    projectID?: number;
    spectrumAlertID?: number;
  };

  type MatrixSpectrumAlert = {
    /** AttackInfo holds the value of the "attack_info" field. */
    attack_info?: MatrixAttackInfo;
    /** 攻击类型 */
    attack_type?: string;
    /** 告警BPS */
    bps?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the MatrixSpectrumAlertQuery when eager-loading is set. */
    edges?: MatrixSpectrumAlertEdges;
    /** 攻击结束时间 */
    end_time?: string;
    /** ID of the ent. */
    id?: number;
    /** IPList holds the value of the "ip_list" field. */
    ip_list?: string[];
    /** Isp holds the value of the "isp" field. */
    isp?: string;
    /** MatrixStrategyID holds the value of the "matrix_strategy_id" field. */
    matrix_strategy_id?: number;
    /** NetType holds the value of the "net_type" field. */
    net_type?: string;
    /** Region holds the value of the "region" field. */
    region?: string;
    /** 备注 */
    remark?: string;
    /** 攻击开始时间 */
    start_time?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
    /** WofangID holds the value of the "wofang_id" field. */
    wofang_id?: number;
  };

  type MatrixSpectrumAlertEdges = {
    /** MatrixSpectrumDatas holds the value of the matrix_spectrum_datas edge. */
    matrix_spectrum_datas?: MatrixSpectrumData[];
    /** MatrixStrategy holds the value of the matrix_strategy edge. */
    matrix_strategy?: MatrixStrategy;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
    /** WofangTicket holds the value of the wofang_ticket edge. */
    wofang_ticket?: Wofang;
  };

  type MatrixSpectrumData = {
    /** Bps holds the value of the "bps" field. */
    bps?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the MatrixSpectrumDataQuery when eager-loading is set. */
    edges?: MatrixSpectrumDataEdges;
    /** ID of the ent. */
    id?: number;
    /** Isp holds the value of the "isp" field. */
    isp?: string;
    /** MatrixSpectrumAlertID holds the value of the "matrix_spectrum_alert_id" field. */
    matrix_spectrum_alert_id?: number;
    /** NetType holds the value of the "net_type" field. */
    net_type?: string;
    /** Region holds the value of the "region" field. */
    region?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** Time holds the value of the "time" field. */
    time?: string;
    /** 更新时间 */
    updated_at?: string;
  };

  type MatrixSpectrumDataEdges = {
    /** MatrixSpectrumAlert holds the value of the matrix_spectrum_alert edge. */
    matrix_spectrum_alert?: MatrixSpectrumAlert;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type MatrixStrategy = {
    /** 创建时间 */
    created_at?: string;
    /** DragBps holds the value of the "drag_bps" field. */
    drag_bps?: number;
    /** DragType holds the value of the "drag_type" field. */
    drag_type?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the MatrixStrategyQuery when eager-loading is set. */
    edges?: MatrixStrategyEdges;
    /** ID of the ent. */
    id?: number;
    /** Isp holds the value of the "isp" field. */
    isp?: string;
    /** MonitorBps holds the value of the "monitor_bps" field. */
    monitor_bps?: number;
    /** Name holds the value of the "name" field. */
    name?: string;
    /** NetType holds the value of the "net_type" field. */
    net_type?: string;
    /** Region holds the value of the "region" field. */
    region?: string;
    /** 备注 */
    remark?: string;
    /** 更新时间 */
    updated_at?: string;
  };

  type MatrixStrategyEdges = {
    /** MatrixStrategyAlerts holds the value of the matrix_strategy_alerts edge. */
    matrix_strategy_alerts?: MatrixSpectrumAlert[];
  };

  type Message = {
    message?: string;
    success?: boolean;
  };

  type MonitorInfo = {
    ackBps?: number;
    ackPps?: number;
    /** 是否开启机器学习 */
    autoML?: boolean;
    icmpBps?: number;
    icmpPps?: number;
    synBps?: number;
    synPps?: number;
    /** 单位bit/s */
    totalBps?: number;
    totalPps?: number;
    udpBps?: number;
    udpPps?: number;
  };

  type Nds4Config = {
    /** ACL开关 */
    sAcl?: boolean;
    /** 防护除SYN外的TCP flood攻击  0-关 1-开 2-自动 */
    sAntiOtherTcp?: number;
    /** 黑名单开关 */
    sBlacklist?: boolean;
    /** 特征匹配开关 */
    sContentMatch?: boolean;
    /** 目的ip限速开关 */
    sDstSpeedLimit?: boolean;
    /** 过滤特殊报文开关 */
    sEliminatePkt?: boolean;
    /** 首包丢弃配置 ：0-关 1-开 2-自动 */
    sFirstPktDrop?: number;
    /** 畸形包报文检测开关 */
    sMalformedPkt?: boolean;
    /** 国外地理位置：0-关 1-开 2-自动 */
    sPositionForeign?: number;
    /** idc国家地理位置：0-关 1-开 2-自动 */
    sPositionIdc?: number;
    /** 源认证配置 ：0-关 1-开 2-自动 */
    sSourceCheck?: number;
    /** 源ip限速开关 */
    sSrcSpeedLimit?: boolean;
    /** TCP反射配置：0-关 1-开 2-自动 */
    sTcpReflection?: number;
    /** UDP反射配置：0-关 1-开 2-自动 */
    sUdpReflection?: number;
    /** 白名单开关 */
    sWhitelist?: boolean;
    /** 不清洗直接转发阈值 */
    tNoCleanLimit?: number;
  };

  type Nds6Config = {
    /** 目的ip限速开关 */
    sDstSpeedLimit?: boolean;
    /** 畸形包报文检测开关 */
    sMalformedPkt?: boolean;
    /** 源ip限速开关 */
    sSrcSpeedLimit?: boolean;
    /** UDP反射配置：0-关 1-开 2-自动 */
    sUdpReflection?: number;
    /** 不清洗直接转发阈值 */
    tNoCleanLimit?: number;
  };

  type Notify = {
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the NotifyQuery when eager-loading is set. */
    edges?: NotifyEdges;
    /** true，通过邮件对emails进行通知 */
    email?: boolean;
    /** 邮件列表 */
    emails?: string[];
    /** 是否启用 */
    enabled?: boolean;
    /** ID of the ent. */
    id?: number;
    /** 通知IP白名单，告警IP在白名单中将不再通知；项目白名单 > 系统设置白名单 */
    ip_whitelists?: string[];
    /** 名称 */
    name?: string;
    /** true，通过电话对phones进行通知 */
    phone?: boolean;
    /** 电话列表 */
    phones?: string[];
    /** true，通过popo对emails进行通知 */
    popo?: boolean;
    /** 通过popo群进行通知 */
    popo_groups?: string[];
    /** 备注 */
    remark?: string;
    /** true，通过auth获取sa列表，并通过邮件进行通知；仅非系统配置生效 */
    sa_notify_email?: boolean;
    /** true，通过auth获取sa列表，并通过popo进行通知；仅非系统配置生效 */
    sa_notify_popo?: boolean;
    /** true，通过短信对phones进行通知 */
    sms?: boolean;
    /** true，系统配置 */
    system?: boolean;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type NotifyEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type ProtectGroup = {
    /** 创建时间 */
    created_at?: string;
    /** DragInfo holds the value of the "drag_info" field. */
    drag_info?: DragInfo;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the ProtectGroupQuery when eager-loading is set. */
    edges?: ProtectGroupEdges;
    /** ExpandIP holds the value of the "expand_ip" field. */
    expand_ip?: string;
    /** GroupID holds the value of the "group_id" field. */
    group_id?: number;
    /** GroupName holds the value of the "group_name" field. */
    group_name?: string;
    /** ID of the ent. */
    id?: number;
    /** IPList holds the value of the "ip_list" field. */
    ip_list?: string[];
    /** MonitorInfo holds the value of the "monitor_info" field. */
    monitor_info?: MonitorInfo;
    /** Nds4Config holds the value of the "nds4_config" field. */
    nds4_config?: Nds4Config;
    /** Nds6Config holds the value of the "nds6_config" field. */
    nds6_config?: Nds6Config;
    /** 备注 */
    remark?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** Type holds the value of the "type" field. */
    type?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type ProtectGroupEdges = {
    /** SpectrumAlerts holds the value of the spectrum_alerts edge. */
    spectrum_alerts?: SpectrumAlert[];
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type PushAlert = {
    /** 1：NDS告警IP；2：机房出口告警IP */
    alert_type?: number;
    bps?: number;
    ip?: string;
    pps?: number;
    product?: string;
    status?: string;
    time?: number;
    type?: string;
  };

  type putCasbinruleIdParams = {
    /** CasbinRule ID */
    id: number;
  };

  type putCleandataIdParams = {
    /** CleanData ID */
    id: number;
  };

  type putCloudalertIdParams = {
    /** CloudAlert ID */
    id: number;
  };

  type putCloudattackdataIdParams = {
    /** CloudAttackData ID */
    id: number;
  };

  type putCloudflowdataIdParams = {
    /** CloudFlowData ID */
    id: number;
  };

  type putDatasyncIdParams = {
    /** DataSync ID */
    id: number;
  };

  type putGroupIdParams = {
    /** Group ID */
    id: number;
  };

  type putHellouserIdParams = {
    /** HelloUser ID */
    id: number;
  };

  type putMatrixspectrumalertIdParams = {
    /** MatrixSpectrumAlert ID */
    id: number;
  };

  type putMatrixspectrumdataIdParams = {
    /** MatrixSpectrumData ID */
    id: number;
  };

  type putMatrixstrategyIdParams = {
    /** MatrixStrategy ID */
    id: number;
  };

  type putNotifyIdParams = {
    /** Notify ID */
    id: number;
  };

  type putProtectgroupIdParams = {
    /** Group ID */
    id: number;
  };

  type putSkylinedosIdParams = {
    /** SkylineDos ID */
    id: number;
  };

  type putSocgroupticketIdParams = {
    /** SocGroupTicket ID */
    id: number;
  };

  type putSpectrumalertIdParams = {
    /** SpectrumAlert ID */
    id: number;
  };

  type putSpectrumdataIdParams = {
    /** SpectrumData ID */
    id: number;
  };

  type putStrategyIdParams = {
    /** Strategy ID */
    id: number;
  };

  type putSystemapiIdParams = {
    /** SystemApi ID */
    id: number;
  };

  type putSystemconfigIdParams = {
    /** SystemConfig ID */
    id: number;
  };

  type putTenantIdParams = {
    /** Tenant ID */
    id: number;
  };

  type putUserIdParams = {
    /** User ID */
    id: number;
  };

  type putUseroperationlogIdParams = {
    /** UserOperationLog ID */
    id: number;
  };

  type putWofangalertIdParams = {
    /** WofangAlert ID */
    id: number;
  };

  type putWofangIdParams = {
    /** Wofang ID */
    id: number;
  };

  type QueryResponseData = {
    /** 0表示成功，非0表示失败 */
    code?: number;
    /** 返回工单的内容 */
    dataitems?: WorkOrder[];
    /** 返回的消息内容 */
    msg?: string;
    /** 满足条件总数 */
    total?: number;
  };

  type Response = {
    data?: any;
    msg?: string;
    status?: number;
  };

  type Result = {
    code?: number;
    data?: any;
    message?: string;
    success?: boolean;
    total?: number;
  };

  type SkylineDos = {
    /** AttackCounters holds the value of the "attack_counters" field. */
    attack_counters?: AttackCounter[];
    /** AttackID holds the value of the "attack_id" field. */
    attack_id?: string;
    /** 创建时间 */
    created_at?: string;
    /** DurationTime holds the value of the "duration_time" field. */
    duration_time?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the SkylineDosQuery when eager-loading is set. */
    edges?: SkylineDosEdges;
    /** 攻击结束时间 */
    end_time?: string;
    /** ID of the ent. */
    id?: number;
    /** Project holds the value of the "project" field. */
    project?: string;
    /** Region holds the value of the "region" field. */
    region?: string;
    /** 备注 */
    remark?: string;
    /** Resource holds the value of the "resource" field. */
    resource?: string;
    /** ResourceType holds the value of the "resource_type" field. */
    resource_type?: string;
    /** 攻击开始时间 */
    start_time?: string;
    /** Status holds the value of the "status" field. */
    status?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
    /** 攻击类型 */
    vector_types?: string[];
  };

  type SkylineDosEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type SocGroupTicket = {
    /** 参数配置  仅[清洗上线和清洗调优且configType=2(自定义防护参数)] 有效 */
    config_args?: string;
    /** 参数配置类型，1表示默认，2表示自定义  仅[清洗上线] 有效 */
    config_type?: number;
    /** 紧急联系人列表 */
    contact_list?: User[];
    /** 创建用户Id，可选 */
    create_user_id?: number;
    /** 创建时间 */
    created_at?: string;
    /** 部门id */
    department_id?: number;
    /** 描述 */
    description?: string;
    /** 清洗方式, 1表示清洗上线，2表示清洗下线 3调优 */
    divert_type?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the SocGroupTicketQuery when eager-loading is set. */
    edges?: SocGroupTicketEdges;
    /** 错误信息 */
    error_info?: string;
    /** 工单跟踪者id列表 */
    follow_list?: number[];
    /** 集团工单编号 */
    group_ticket_id?: number;
    /** ID of the ent. */
    id?: number;
    /** 牵引IP列表 */
    ip_list?: string[];
    /** 牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两位小数，单位Gbps */
    min_bandwidth?: number;
    /** 名称 */
    name?: string;
    /** 操作时间 */
    op_time?: string;
    /** 操作方式，1表示自动，2表示手动 */
    op_type?: number;
    /** 产品代号 */
    product_code?: string;
    /** 产品中文名 */
    product_name?: string;
    /** 备注 */
    remark?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 类型 */
    type?: string;
    /** 更新时间 */
    updated_at?: string;
  };

  type SocGroupTicketEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
    /** User holds the value of the user edge. */
    user?: User;
  };

  type SpectrumAlert = {
    /** AttackInfo holds the value of the "attack_info" field. */
    attack_info?: AttackInfo;
    /** 攻击类型 */
    attack_type?: string;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the SpectrumAlertQuery when eager-loading is set. */
    edges?: SpectrumAlertEdges;
    /** 攻击结束时间 */
    end_time?: string;
    /** ID of the ent. */
    id?: number;
    /** 被攻击IP */
    ip?: string;
    /** IspCode holds the value of the "isp_code" field. */
    isp_code?: number;
    /** 告警BPS */
    max_bps?: number;
    /** 告警PPS */
    max_pps?: number;
    /** ProtectGroupID holds the value of the "protect_group_id" field. */
    protect_group_id?: number;
    /** ProtectStatus holds the value of the "protect_status" field. */
    protect_status?: number[];
    /** 备注 */
    remark?: string;
    /** 攻击开始时间 */
    start_time?: string;
    /** StrategyID holds the value of the "strategy_id" field. */
    strategy_id?: number;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
    /** WofangID holds the value of the "wofang_id" field. */
    wofang_id?: number;
  };

  type SpectrumAlertEdges = {
    /** CleanDatas holds the value of the clean_datas edge. */
    clean_datas?: CleanData[];
    /** ProtectGroup holds the value of the protect_group edge. */
    protect_group?: ProtectGroup;
    /** SpectrumDatas holds the value of the spectrum_datas edge. */
    spectrum_datas?: SpectrumData[];
    /** Strategy holds the value of the strategy edge. */
    strategy?: Strategy;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
    /** WofangTicket holds the value of the wofang_ticket edge. */
    wofang_ticket?: Wofang;
  };

  type SpectrumData = {
    /** AckBps holds the value of the "ack_bps" field. */
    ack_bps?: number;
    /** AckPps holds the value of the "ack_pps" field. */
    ack_pps?: number;
    /** Bps holds the value of the "bps" field. */
    bps?: number;
    /** 创建时间 */
    created_at?: string;
    /** DataType holds the value of the "data_type" field. */
    data_type?: number;
    /** DNSAnswerBps holds the value of the "dns_answer_bps" field. */
    dns_answer_bps?: number;
    /** DNSAnswerPps holds the value of the "dns_answer_pps" field. */
    dns_answer_pps?: number;
    /** DNSQueryBps holds the value of the "dns_query_bps" field. */
    dns_query_bps?: number;
    /** DNSQueryPps holds the value of the "dns_query_pps" field. */
    dns_query_pps?: number;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the SpectrumDataQuery when eager-loading is set. */
    edges?: SpectrumDataEdges;
    /** Host holds the value of the "host" field. */
    host?: string;
    /** IcmpBps holds the value of the "icmp_bps" field. */
    icmp_bps?: number;
    /** IcmpPps holds the value of the "icmp_pps" field. */
    icmp_pps?: number;
    /** ID of the ent. */
    id?: number;
    /** IP holds the value of the "ip" field. */
    ip?: string;
    /** IPType holds the value of the "ip_type" field. */
    ip_type?: number;
    /** Monitor holds the value of the "monitor" field. */
    monitor?: string;
    /** MonitorID holds the value of the "monitor_id" field. */
    monitor_id?: number;
    /** NtpBps holds the value of the "ntp_bps" field. */
    ntp_bps?: number;
    /** NtpPps holds the value of the "ntp_pps" field. */
    ntp_pps?: number;
    /** Pps holds the value of the "pps" field. */
    pps?: number;
    /** Product holds the value of the "product" field. */
    product?: string;
    /** QPS holds the value of the "qps" field. */
    qps?: number;
    /** ReceiveCount holds the value of the "receive_count" field. */
    receive_count?: number;
    /** SmallPps holds the value of the "small_pps" field. */
    small_pps?: number;
    /** SpectrumAlertID holds the value of the "spectrum_alert_id" field. */
    spectrum_alert_id?: number;
    /** SsdpBps holds the value of the "ssdp_bps" field. */
    ssdp_bps?: number;
    /** SsdpPps holds the value of the "ssdp_pps" field. */
    ssdp_pps?: number;
    /** SynAckBps holds the value of the "syn_ack_bps" field. */
    syn_ack_bps?: number;
    /** SynAckPps holds the value of the "syn_ack_pps" field. */
    syn_ack_pps?: number;
    /** SynBps holds the value of the "syn_bps" field. */
    syn_bps?: number;
    /** SynPps holds the value of the "syn_pps" field. */
    syn_pps?: number;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** Time holds the value of the "time" field. */
    time?: string;
    /** UDPBps holds the value of the "udp_bps" field. */
    udp_bps?: number;
    /** UDPPps holds the value of the "udp_pps" field. */
    udp_pps?: number;
  };

  type SpectrumDataEdges = {
    /** SpectrumAlert holds the value of the spectrum_alert edge. */
    spectrum_alert?: SpectrumAlert;
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type Strategy = {
    /** 总bps大小阈值 */
    bps?: number;
    /** bps次数阈值 */
    bps_count?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the StrategyQuery when eager-loading is set. */
    edges?: StrategyEdges;
    /** 是否启用 */
    enabled?: boolean;
    /** ID of the ent. */
    id?: number;
    /** ip运营商类型 */
    isp_code?: number;
    /** 名称 */
    name?: string;
    /** 总pps大小阈值 */
    pps?: number;
    /** pps次数阈值 */
    pps_count?: number;
    /** 备注 */
    remark?: string;
    /** 系统策略，项目未配置自定义策略情况下，使用系统策略 */
    system?: boolean;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 牵引防护类型 */
    type?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type StrategyEdges = {
    /** StrategyAlerts holds the value of the strategy_alerts edge. */
    strategy_alerts?: SpectrumAlert[];
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type SystemApi = {
    /** 创建时间 */
    created_at?: string;
    /** http方法 */
    http_method?: string;
    /** ID of the ent. */
    id?: number;
    /** 名称 */
    name?: string;
    /** 路径 */
    path?: string;
    /** 是否公共接口，所有用户可以访问 */
    public?: boolean;
    /** 备注 */
    remark?: string;
    /** 角色 */
    roles?: string[];
    /** 是否sa接口，sa接口仅sa用户可操作（如果public，则普通用户可以访问(get） */
    sa?: boolean;
    /** 更新时间 */
    updated_at?: string;
  };

  type SystemConfig = {
    /** 创建时间 */
    created_at?: string;
    /** ID of the ent. */
    id?: number;
    /** 通知IP白名单，IP在白名单中将不再通知 */
    ip_whitelists?: string[];
    /** NotifyEmails holds the value of the "notify_emails" field. */
    notify_emails?: string[];
    /** NotifyPhones holds the value of the "notify_phones" field. */
    notify_phones?: string[];
    /** NotifyScenes holds the value of the "notify_scenes" field. */
    notify_scenes?: string[];
    /** 备注 */
    remark?: string;
    /** 更新时间 */
    updated_at?: string;
    /** WofangTestIP holds the value of the "wofang_test_ip" field. */
    wofang_test_ip?: string;
  };

  type Tenant = {
    /** Code holds the value of the "code" field. */
    code?: string;
    /** ID of the ent. */
    id?: number;
    /** Isdefend holds the value of the "isdefend" field. */
    isdefend?: boolean;
    /** Name holds the value of the "name" field. */
    name?: string;
    /** Offline holds the value of the "offline" field. */
    offline?: boolean;
  };

  type User = {
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the UserQuery when eager-loading is set. */
    edges?: UserEdges;
    /** ID of the ent. */
    id?: number;
    /** Name holds the value of the "name" field. */
    name?: string;
    /** Password holds the value of the "password" field. */
    password?: string;
    /** 超级管理员，可操作所有租户数据 */
    super_admin?: boolean;
    /** true，则重新增加用户权限（增量）：项目、角色、api资源 */
    update_auth?: boolean;
    /** 更新时间 */
    updated_at?: string;
    /** 有效 */
    valid?: boolean;
  };

  type User = {
    email?: string;
    name?: string;
    phone?: string;
  };

  type UserEdges = {
    /** Groups holds the value of the groups edge. */
    groups?: Group[];
  };

  type UserOperationLog = {
    /** 创建时间 */
    created_at?: string;
    /** ID of the ent. */
    id?: number;
    /** Method holds the value of the "method" field. */
    method?: string;
    /** Project holds the value of the "project" field. */
    project?: string;
    /** 备注 */
    remark?: string;
    /** RequestBody holds the value of the "request_body" field. */
    request_body?: string;
    /** RequestID holds the value of the "request_id" field. */
    request_id?: string;
    /** 更新时间 */
    updated_at?: string;
    /** URI holds the value of the "uri" field. */
    uri?: string;
    /** Username holds the value of the "username" field. */
    username?: string;
  };

  type Wofang = {
    /** 创建用户Id，可选 */
    create_user_id?: number;
    /** 创建时间 */
    created_at?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the WofangQuery when eager-loading is set. */
    edges?: WofangEdges;
    /** 错误信息 */
    error_info?: string;
    /** ID of the ent. */
    id?: number;
    /** IP */
    ip?: string;
    /** 名称 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 牵引清洗开始时间 */
    start_time?: string;
    /** 状态 */
    status?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 类型 */
    type?: string;
    /** 自动解封时间，单位：秒 */
    un_drag_second?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type WofangAlert = {
    /** AttackID holds the value of the "attack_id" field. */
    attack_id?: number;
    /** 攻击状态，1：攻击结束，2：攻击中 */
    attack_status?: number;
    /** 攻击类型 */
    attack_type?: string[];
    /** 创建时间 */
    created_at?: string;
    /** DeviceIP holds the value of the "device_ip" field. */
    device_ip?: string;
    /** Edges holds the relations/edges for other nodes in the graph.
The values are being populated by the WofangAlertQuery when eager-loading is set. */
    edges?: WofangAlertEdges;
    /** 攻击结束时间 */
    end_time?: string;
    /** ID of the ent. */
    id?: number;
    /** MaxDropBps holds the value of the "max_drop_bps" field. */
    max_drop_bps?: number;
    /** MaxInBps holds the value of the "max_in_bps" field. */
    max_in_bps?: number;
    /** 备注 */
    remark?: string;
    /** 攻击开始时间 */
    start_time?: string;
    /** 租户Id，可选 */
    tenant_id?: number;
    /** 更新时间 */
    updated_at?: string;
    /** 防护ip */
    zone_ip?: string;
  };

  type WofangAlertEdges = {
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
  };

  type WofangEdges = {
    /** MatrixSpectrumAlerts holds the value of the matrix_spectrum_alerts edge. */
    matrix_spectrum_alerts?: MatrixSpectrumAlert[];
    /** SpectrumAlerts holds the value of the spectrum_alerts edge. */
    spectrum_alerts?: SpectrumAlert[];
    /** Tenant holds the value of the tenant edge. */
    tenant?: Tenant;
    /** User holds the value of the user edge. */
    user?: User;
  };

  type WorkOrder = {
    applyUser?: {
      count?: number;
      createTime?: any;
      department1?: any;
      department2?: any;
      department3?: any;
      email?: string;
      fullname?: string;
      grantedAuthorityList?: any[];
      id?: number;
      lastLoginTimeDate?: any;
      phone?: any;
      supperUser?: boolean;
      userAuthorityList?: any;
      userGroupList?: any;
      userRole?: any;
      username?: string;
    };
    assetsContacterGroup?: {
      assetsType?: string;
      contacterGroupList?: {
        baseContacterObject?: {
          cclists?: {
            count?: number;
            createTime?: any;
            department1?: any;
            department2?: any;
            department3?: any;
            email?: string;
            fullname?: string;
            grantedAuthorityList?: any[];
            id?: number;
            lastLoginTimeDate?: any;
            phone?: any;
            supperUser?: boolean;
            userAuthorityList?: any;
            userGroupList?: any;
            userRole?: any;
            username?: string;
          }[];
          contacters?: {
            count?: number;
            createTime?: any;
            department1?: any;
            department2?: any;
            department3?: any;
            email?: string;
            fullname?: string;
            grantedAuthorityList?: any[];
            id?: number;
            lastLoginTimeDate?: any;
            phone?: any;
            supperUser?: boolean;
            userAuthorityList?: any;
            userGroupList?: any;
            userRole?: any;
            username?: string;
          }[];
          dataCategory?: { description?: any; id?: number; name?: string };
          departId?: any;
          departmentName?: any;
          entityEmpty?: boolean;
          leader?: any;
        };
        createTime?: number;
        description?: any;
        entityEmpty?: boolean;
        id?: number;
        mapGroupId?: number;
        name?: string;
        notifyStrategy?: string;
        remark?: any;
        updateTime?: number;
      }[];
      defaultContacterGroup?: {
        baseContacterObject?: {
          cclists?: {
            count?: number;
            createTime?: any;
            department1?: any;
            department2?: any;
            department3?: any;
            email?: string;
            fullname?: string;
            grantedAuthorityList?: any[];
            id?: number;
            lastLoginTimeDate?: any;
            phone?: any;
            supperUser?: boolean;
            userAuthorityList?: any;
            userGroupList?: any;
            userRole?: any;
            username?: string;
          }[];
          contacters?: {
            count?: number;
            createTime?: any;
            department1?: any;
            department2?: any;
            department3?: any;
            email?: string;
            fullname?: string;
            grantedAuthorityList?: any[];
            id?: number;
            lastLoginTimeDate?: any;
            phone?: any;
            supperUser?: boolean;
            userAuthorityList?: any;
            userGroupList?: any;
            userRole?: any;
            username?: string;
          }[];
          dataCategory?: { description?: any; id?: number; name?: string };
          departId?: any;
          departmentName?: any;
          entityEmpty?: boolean;
          leader?: any;
        };
        createTime?: number;
        description?: any;
        entityEmpty?: boolean;
        id?: number;
        mapGroupId?: number;
        name?: string;
        notifyStrategy?: string;
        remark?: any;
        updateTime?: number;
      };
      description?: any;
      id?: number;
      name?: any;
      vulsContacterGroup?: any;
    };
    assetsType?: string;
    attachFileName?: any;
    attachGroupkey?: any;
    autoTestType?: any;
    commentStatus?: number;
    /** 【创建时间】 */
    createTime?: number;
    cycleStatus?: number;
    department?: {
      assetsContacterGroup?: any;
      assetsType?: string;
      createTime?: number;
      description?: string;
      id?: number;
      leader?: any;
      level?: number;
      levelOneId?: number;
      name?: string;
      oaId?: any;
      parentOaId?: any;
      remark?: any;
      state?: any;
      updateTime?: number;
      upperName?: any;
      upperid?: number;
    };
    department1stName?: any;
    department2ndName?: any;
    department3rdName?: any;
    departmentName?: any;
    dept1?: any;
    dept2?: any;
    dept3?: any;
    /** 【描述】 */
    description?: string;
    expectTime?: any;
    extraConfig?: {
      bandwidth?: number;
      configArgs?: string;
      configType?: number;
      departmentId?: number;
      departmentName?: string;
      divertType?: number;
      emergencyContacterList?: User[];
      ipList?: string;
      opTime?: number;
      opType?: number;
      productAlias?: string;
      productName?: string;
    };
    /** 【忽略忽略忽略】 */
    extraConfigString?: string;
    finalDeadlineTime?: any;
    flowId?: any;
    /** 【SOC上工单的ID】 */
    id?: number;
    leader?: any;
    /** 【标题】 */
    name?: string;
    parentId?: number;
    phase?: number;
    product?: any;
    /** 【备注】 */
    remark?: any;
    resultAttachFileName?: string;
    resultAttachGroupkey?: string;
    testCycle?: number;
    /** 【更新时间】 */
    updateTime?: number;
    url?: any;
    workOrderStatus?: string;
    workOrderType?: string;
    workingDays?: number;
  };
}
