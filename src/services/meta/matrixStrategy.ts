// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 MatrixStrategy Query 根据指定字段、时间范围查询或搜索 MatrixStrategy GET /api/v1/matrixstrategy */
export async function getMatrixstrategy(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixstrategyParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixStrategy[] }>('/api/v1/matrixstrategy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 MatrixStrategy Create 创建 MatrixStrategy POST /api/v1/matrixstrategy */
export async function postMatrixstrategy(
  body: API.MatrixStrategy,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixStrategy }>('/api/v1/matrixstrategy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 MatrixStrategy QueryByID 根据 ID 查询 MatrixStrategy GET /api/v1/matrixstrategy/${param0} */
export async function getMatrixstrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixstrategyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixStrategy }>(`/api/v1/matrixstrategy/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 MatrixStrategy UpdateByID 根据 ID 修改 MatrixStrategy PUT /api/v1/matrixstrategy/${param0} */
export async function putMatrixstrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putMatrixstrategyIdParams,
  body: API.MatrixStrategy,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixStrategy }>(`/api/v1/matrixstrategy/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 MatrixStrategy DeleteByID 根据 ID 删除 MatrixStrategy DELETE /api/v1/matrixstrategy/${param0} */
export async function deleteMatrixstrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteMatrixstrategyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/matrixstrategy/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 MatrixStrategy CreateBulk 批量创建 MatrixStrategy POST /api/v1/matrixstrategy/bulk */
export async function postMatrixstrategyBulk(
  body: API.MatrixStrategy[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixStrategy[] }>('/api/v1/matrixstrategy/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 MatrixStrategy DeleteBulk 根据 IDs 批量删除 MatrixStrategy POST /api/v1/matrixstrategy/bulk/delete */
export async function postMatrixstrategyBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/matrixstrategy/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
