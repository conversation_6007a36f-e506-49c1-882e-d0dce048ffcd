// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据 起始时间和偏移值 查询 DDoS Query 根据 起始时间和偏移值 查询 DDoS GET /api/v1/socgroup/ticket */
export async function getSocgroupTicket(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSocgroupTicketParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.QueryResponseData & { dataitems?: API.WorkOrder[] } }>(
    '/api/v1/socgroup/ticket',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** Add 提交 四层DDOS清洗防护 工单 Add 提交 四层DDOS清洗防护 工单 POST /api/v1/socgroup/ticket */
export async function postSocgroupTicket(
  body: API.AddRequestData,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.AddResponseData & { dataitems?: API.WorkOrder } }>(
    '/api/v1/socgroup/ticket',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
