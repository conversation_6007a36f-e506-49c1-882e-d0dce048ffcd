// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 ProtectGroup Query 根据指定字段、时间范围查询或搜索 ProtectGroup GET /api/v1/protectgroup */
export async function getProtectgroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getProtectgroupParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.ProtectGroup[] }>('/api/v1/protectgroup', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 ProtectGroup Create 创建 ProtectGroup POST /api/v1/protectgroup */
export async function postProtectgroup(body: API.ProtectGroup, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.ProtectGroup }>('/api/v1/protectgroup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 ProtectGroup QueryByID 根据 ID 查询 ProtectGroup GET /api/v1/protectgroup/${param0} */
export async function getProtectgroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getProtectgroupIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.ProtectGroup }>(`/api/v1/protectgroup/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 ProtectGroup UpdateByID 根据 ID 修改 ProtectGroup PUT /api/v1/protectgroup/${param0} */
export async function putProtectgroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putProtectgroupIdParams,
  body: API.ProtectGroup,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.ProtectGroup }>(`/api/v1/protectgroup/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 ProtectGroup DeleteByID 根据 ID 删除 ProtectGroup DELETE /api/v1/protectgroup/${param0} */
export async function deleteProtectgroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteProtectgroupIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/protectgroup/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 ProtectGroup CreateBulk 批量创建 ProtectGroup POST /api/v1/protectgroup/bulk */
export async function postProtectgroupBulk(
  body: API.ProtectGroup[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.ProtectGroup[] }>('/api/v1/protectgroup/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 ProtectGroup DeleteBulk 根据 IDs 批量删除 ProtectGroup POST /api/v1/protectgroup/bulk/delete */
export async function postProtectgroupBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/protectgroup/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
