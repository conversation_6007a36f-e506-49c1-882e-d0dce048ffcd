// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取NDS时间范围内的告警数据 获取NDS时间范围内的告警数据 GET /api/v1/nds/alert */
export async function getNdsAlert(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNdsAlertParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.AlertVO[] }>('/api/v1/nds/alert', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取NDS告警时间范围内的清洗数据 获取NDS告警时间范围内的清洗数据 GET /api/v1/nds/cleandata */
export async function getNdsCleandata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNdsCleandataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.GroupCleanDataResponse[] }>('/api/v1/nds/cleandata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加ip到NDS防护群组 添加ip到NDS防护群组 POST /api/v1/nds/ip */
export async function postNdsIp(body: API.GroupUpdate, options?: { [key: string]: any }) {
  return request<API.Message>('/api/v1/nds/ip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 集团NDS推送告警 PushAlert 集团NDS推送告警 PushAlert POST /api/v1/nds/push */
export async function postNdsPush(body: API.PushAlert, options?: { [key: string]: any }) {
  return request<API.Message>('/api/v1/nds/push', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取NDS告警时间范围内的分光数据 获取NDS告警时间范围内的分光数据 GET /api/v1/nds/spectrumdata */
export async function getNdsSpectrumdata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNdsSpectrumdataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.GroupSpectrumDataResponse[] }>(
    '/api/v1/nds/spectrumdata',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
