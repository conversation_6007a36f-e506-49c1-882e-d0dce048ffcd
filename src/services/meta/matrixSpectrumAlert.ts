// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert GET /api/v1/matrixspectrumalert */
export async function getMatrixspectrumalert(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixspectrumalertParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumAlert[] }>('/api/v1/matrixspectrumalert', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 MatrixSpectrumAlert Create 创建 MatrixSpectrumAlert POST /api/v1/matrixspectrumalert */
export async function postMatrixspectrumalert(
  body: API.MatrixSpectrumAlert,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumAlert }>('/api/v1/matrixspectrumalert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 MatrixSpectrumAlert QueryByID 根据 ID 查询 MatrixSpectrumAlert GET /api/v1/matrixspectrumalert/${param0} */
export async function getMatrixspectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixspectrumalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixSpectrumAlert }>(
    `/api/v1/matrixspectrumalert/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** UpdateByID 根据 ID 修改 MatrixSpectrumAlert UpdateByID 根据 ID 修改 MatrixSpectrumAlert PUT /api/v1/matrixspectrumalert/${param0} */
export async function putMatrixspectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putMatrixspectrumalertIdParams,
  body: API.MatrixSpectrumAlert,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixSpectrumAlert }>(
    `/api/v1/matrixspectrumalert/${param0}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** DeleteByID 根据 ID 删除 MatrixSpectrumAlert DeleteByID 根据 ID 删除 MatrixSpectrumAlert DELETE /api/v1/matrixspectrumalert/${param0} */
export async function deleteMatrixspectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteMatrixspectrumalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/matrixspectrumalert/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Query 获取攻击数据 MatrixSpectrumAlert Query 获取攻击数据 MatrixSpectrumAlert GET /api/v1/matrixspectrumalert/attacking */
export async function getMatrixspectrumalertAttacking(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixspectrumalertAttackingParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumAlert[] }>(
    '/api/v1/matrixspectrumalert/attacking',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** CreateBulk 批量创建 MatrixSpectrumAlert CreateBulk 批量创建 MatrixSpectrumAlert POST /api/v1/matrixspectrumalert/bulk */
export async function postMatrixspectrumalertBulk(
  body: API.MatrixSpectrumAlert[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumAlert[] }>(
    '/api/v1/matrixspectrumalert/bulk',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert POST /api/v1/matrixspectrumalert/bulk/delete */
export async function postMatrixspectrumalertBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/matrixspectrumalert/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
