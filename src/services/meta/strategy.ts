// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 Strategy Query 根据指定字段、时间范围查询或搜索 Strategy GET /api/v1/strategy */
export async function getStrategy(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getStrategyParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.Strategy[] }>('/api/v1/strategy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 Strategy Create 创建 Strategy POST /api/v1/strategy */
export async function postStrategy(body: API.Strategy, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Strategy }>('/api/v1/strategy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 Strategy QueryByID 根据 ID 查询 Strategy GET /api/v1/strategy/${param0} */
export async function getStrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getStrategyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Strategy }>(`/api/v1/strategy/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 Strategy UpdateByID 根据 ID 修改 Strategy PUT /api/v1/strategy/${param0} */
export async function putStrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putStrategyIdParams,
  body: API.Strategy,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Strategy }>(`/api/v1/strategy/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 Strategy DeleteByID 根据 ID 删除 Strategy DELETE /api/v1/strategy/${param0} */
export async function deleteStrategyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteStrategyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/strategy/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 Strategy CreateBulk 批量创建 Strategy POST /api/v1/strategy/bulk */
export async function postStrategyBulk(body: API.Strategy[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Strategy[] }>('/api/v1/strategy/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 Strategy DeleteBulk 根据 IDs 批量删除 Strategy POST /api/v1/strategy/bulk/delete */
export async function postStrategyBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/strategy/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
