// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 CloudAttackData Query 根据指定字段、时间范围查询或搜索 CloudAttackData GET /api/v1/cloudattackdata */
export async function getCloudattackdata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudattackdataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudAttackData[] }>('/api/v1/cloudattackdata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 CloudAttackData Create 创建 CloudAttackData POST /api/v1/cloudattackdata */
export async function postCloudattackdata(
  body: API.CloudAttackData,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudAttackData }>('/api/v1/cloudattackdata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 CloudAttackData QueryByID 根据 ID 查询 CloudAttackData GET /api/v1/cloudattackdata/${param0} */
export async function getCloudattackdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudattackdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudAttackData }>(`/api/v1/cloudattackdata/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 CloudAttackData UpdateByID 根据 ID 修改 CloudAttackData PUT /api/v1/cloudattackdata/${param0} */
export async function putCloudattackdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putCloudattackdataIdParams,
  body: API.CloudAttackData,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudAttackData }>(`/api/v1/cloudattackdata/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 CloudAttackData DeleteByID 根据 ID 删除 CloudAttackData DELETE /api/v1/cloudattackdata/${param0} */
export async function deleteCloudattackdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCloudattackdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/cloudattackdata/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 CloudAttackData CreateBulk 批量创建 CloudAttackData POST /api/v1/cloudattackdata/bulk */
export async function postCloudattackdataBulk(
  body: API.CloudAttackData[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudAttackData[] }>('/api/v1/cloudattackdata/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 CloudAttackData DeleteBulk 根据 IDs 批量删除 CloudAttackData POST /api/v1/cloudattackdata/bulk/delete */
export async function postCloudattackdataBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/cloudattackdata/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
