// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 CleanData Query 根据指定字段、时间范围查询或搜索 CleanData GET /api/v1/cleandata */
export async function getCleandata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCleandataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CleanData[] }>('/api/v1/cleandata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 CleanData Create 创建 CleanData POST /api/v1/cleandata */
export async function postCleandata(body: API.CleanData, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.CleanData }>('/api/v1/cleandata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 CleanData QueryByID 根据 ID 查询 CleanData GET /api/v1/cleandata/${param0} */
export async function getCleandataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCleandataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CleanData }>(`/api/v1/cleandata/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 CleanData UpdateByID 根据 ID 修改 CleanData PUT /api/v1/cleandata/${param0} */
export async function putCleandataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putCleandataIdParams,
  body: API.CleanData,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CleanData }>(`/api/v1/cleandata/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 CleanData DeleteByID 根据 ID 删除 CleanData DELETE /api/v1/cleandata/${param0} */
export async function deleteCleandataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCleandataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/cleandata/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 CleanData CreateBulk 批量创建 CleanData POST /api/v1/cleandata/bulk */
export async function postCleandataBulk(body: API.CleanData[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.CleanData[] }>('/api/v1/cleandata/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 CleanData DeleteBulk 根据 IDs 批量删除 CleanData POST /api/v1/cleandata/bulk/delete */
export async function postCleandataBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/cleandata/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
