// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 SpectrumAlert Query 根据指定字段、时间范围查询或搜索 SpectrumAlert GET /api/v1/spectrumalert */
export async function getSpectrumalert(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpectrumalertParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SpectrumAlert[] }>('/api/v1/spectrumalert', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 SpectrumAlert Create 创建 SpectrumAlert POST /api/v1/spectrumalert */
export async function postSpectrumalert(body: API.SpectrumAlert, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.SpectrumAlert }>('/api/v1/spectrumalert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 SpectrumAlert QueryByID 根据 ID 查询 SpectrumAlert GET /api/v1/spectrumalert/${param0} */
export async function getSpectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpectrumalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SpectrumAlert }>(`/api/v1/spectrumalert/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 SpectrumAlert UpdateByID 根据 ID 修改 SpectrumAlert PUT /api/v1/spectrumalert/${param0} */
export async function putSpectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putSpectrumalertIdParams,
  body: API.SpectrumAlert,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SpectrumAlert }>(`/api/v1/spectrumalert/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 SpectrumAlert DeleteByID 根据 ID 删除 SpectrumAlert DELETE /api/v1/spectrumalert/${param0} */
export async function deleteSpectrumalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSpectrumalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/spectrumalert/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Query 获取攻击数据 SpectrumAlert Query 获取攻击数据 SpectrumAlert GET /api/v1/spectrumalert/attacking */
export async function getSpectrumalertAttacking(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSpectrumalertAttackingParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SpectrumAlert[] }>('/api/v1/spectrumalert/attacking', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 SpectrumAlert CreateBulk 批量创建 SpectrumAlert POST /api/v1/spectrumalert/bulk */
export async function postSpectrumalertBulk(
  body: API.SpectrumAlert[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SpectrumAlert[] }>('/api/v1/spectrumalert/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 SpectrumAlert DeleteBulk 根据 IDs 批量删除 SpectrumAlert POST /api/v1/spectrumalert/bulk/delete */
export async function postSpectrumalertBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/spectrumalert/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
