// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 SystemConfig Query 根据指定字段、时间范围查询或搜索 SystemConfig GET /api/v1/systemconfig */
export async function getSystemconfig(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSystemconfigParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SystemConfig[] }>('/api/v1/systemconfig', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 SystemConfig Create 创建 SystemConfig POST /api/v1/systemconfig */
export async function postSystemconfig(body: API.SystemConfig, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.SystemConfig }>('/api/v1/systemconfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 SystemConfig QueryByID 根据 ID 查询 SystemConfig GET /api/v1/systemconfig/${param0} */
export async function getSystemconfigId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSystemconfigIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SystemConfig }>(`/api/v1/systemconfig/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 SystemConfig UpdateByID 根据 ID 修改 SystemConfig PUT /api/v1/systemconfig/${param0} */
export async function putSystemconfigId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putSystemconfigIdParams,
  body: API.SystemConfig,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SystemConfig }>(`/api/v1/systemconfig/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 SystemConfig DeleteByID 根据 ID 删除 SystemConfig DELETE /api/v1/systemconfig/${param0} */
export async function deleteSystemconfigId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSystemconfigIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/systemconfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 SystemConfig CreateBulk 批量创建 SystemConfig POST /api/v1/systemconfig/bulk */
export async function postSystemconfigBulk(
  body: API.SystemConfig[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SystemConfig[] }>('/api/v1/systemconfig/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 SystemConfig DeleteBulk 根据 IDs 批量删除 SystemConfig POST /api/v1/systemconfig/bulk/delete */
export async function postSystemconfigBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/systemconfig/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
