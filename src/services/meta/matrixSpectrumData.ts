// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData GET /api/v1/matrixspectrumdata */
export async function getMatrixspectrumdata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixspectrumdataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumData[] }>('/api/v1/matrixspectrumdata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 MatrixSpectrumData Create 创建 MatrixSpectrumData POST /api/v1/matrixspectrumdata */
export async function postMatrixspectrumdata(
  body: API.MatrixSpectrumData,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumData }>('/api/v1/matrixspectrumdata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 MatrixSpectrumData QueryByID 根据 ID 查询 MatrixSpectrumData GET /api/v1/matrixspectrumdata/${param0} */
export async function getMatrixspectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMatrixspectrumdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixSpectrumData }>(
    `/api/v1/matrixspectrumdata/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** UpdateByID 根据 ID 修改 MatrixSpectrumData UpdateByID 根据 ID 修改 MatrixSpectrumData PUT /api/v1/matrixspectrumdata/${param0} */
export async function putMatrixspectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putMatrixspectrumdataIdParams,
  body: API.MatrixSpectrumData,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.MatrixSpectrumData }>(
    `/api/v1/matrixspectrumdata/${param0}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** DeleteByID 根据 ID 删除 MatrixSpectrumData DeleteByID 根据 ID 删除 MatrixSpectrumData DELETE /api/v1/matrixspectrumdata/${param0} */
export async function deleteMatrixspectrumdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteMatrixspectrumdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/matrixspectrumdata/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 MatrixSpectrumData CreateBulk 批量创建 MatrixSpectrumData POST /api/v1/matrixspectrumdata/bulk */
export async function postMatrixspectrumdataBulk(
  body: API.MatrixSpectrumData[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.MatrixSpectrumData[] }>(
    '/api/v1/matrixspectrumdata/bulk',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData POST /api/v1/matrixspectrumdata/bulk/delete */
export async function postMatrixspectrumdataBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/matrixspectrumdata/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
