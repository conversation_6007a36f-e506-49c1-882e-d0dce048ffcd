// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 Notify Query 根据指定字段、时间范围查询或搜索 Notify GET /api/v1/notify */
export async function getNotify(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNotifyParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.Notify[] }>('/api/v1/notify', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 Notify Create 创建 Notify POST /api/v1/notify */
export async function postNotify(body: API.Notify, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Notify }>('/api/v1/notify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 Notify QueryByID 根据 ID 查询 Notify GET /api/v1/notify/${param0} */
export async function getNotifyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getNotifyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Notify }>(`/api/v1/notify/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 Notify UpdateByID 根据 ID 修改 Notify PUT /api/v1/notify/${param0} */
export async function putNotifyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putNotifyIdParams,
  body: API.Notify,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Notify }>(`/api/v1/notify/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 Notify DeleteByID 根据 ID 删除 Notify DELETE /api/v1/notify/${param0} */
export async function deleteNotifyId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteNotifyIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/notify/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 Notify CreateBulk 批量创建 Notify POST /api/v1/notify/bulk */
export async function postNotifyBulk(body: API.Notify[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Notify[] }>('/api/v1/notify/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 Notify DeleteBulk 根据 IDs 批量删除 Notify POST /api/v1/notify/bulk/delete */
export async function postNotifyBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/notify/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
