// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 SocGroupTicket Query 根据指定字段、时间范围查询或搜索 SocGroupTicket GET /api/v1/socgroupticket */
export async function getSocgroupticket(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSocgroupticketParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SocGroupTicket[] }>('/api/v1/socgroupticket', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 SocGroupTicket Create 创建 SocGroupTicket POST /api/v1/socgroupticket */
export async function postSocgroupticket(
  body: API.SocGroupTicket,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SocGroupTicket }>('/api/v1/socgroupticket', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 SocGroupTicket QueryByID 根据 ID 查询 SocGroupTicket GET /api/v1/socgroupticket/${param0} */
export async function getSocgroupticketId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSocgroupticketIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SocGroupTicket }>(`/api/v1/socgroupticket/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 SocGroupTicket UpdateByID 根据 ID 修改 SocGroupTicket PUT /api/v1/socgroupticket/${param0} */
export async function putSocgroupticketId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putSocgroupticketIdParams,
  body: API.SocGroupTicket,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SocGroupTicket }>(`/api/v1/socgroupticket/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 SocGroupTicket DeleteByID 根据 ID 删除 SocGroupTicket DELETE /api/v1/socgroupticket/${param0} */
export async function deleteSocgroupticketId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSocgroupticketIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/socgroupticket/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 SocGroupTicket CreateBulk 批量创建 SocGroupTicket POST /api/v1/socgroupticket/bulk */
export async function postSocgroupticketBulk(
  body: API.SocGroupTicket[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SocGroupTicket[] }>('/api/v1/socgroupticket/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 SocGroupTicket DeleteBulk 根据 IDs 批量删除 SocGroupTicket POST /api/v1/socgroupticket/bulk/delete */
export async function postSocgroupticketBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/socgroupticket/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
