// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 HelloUser Query 根据指定字段、时间范围查询或搜索 HelloUser GET /api/v1/hellouser */
export async function getHellouser(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getHellouserParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.HelloUser[] }>('/api/v1/hellouser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 HelloUser Create 创建 HelloUser POST /api/v1/hellouser */
export async function postHellouser(body: API.HelloUser, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.HelloUser }>('/api/v1/hellouser', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 HelloUser QueryByID 根据 ID 查询 HelloUser GET /api/v1/hellouser/${param0} */
export async function getHellouserId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getHellouserIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.HelloUser }>(`/api/v1/hellouser/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 HelloUser UpdateByID 根据 ID 修改 HelloUser PUT /api/v1/hellouser/${param0} */
export async function putHellouserId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putHellouserIdParams,
  body: API.HelloUser,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.HelloUser }>(`/api/v1/hellouser/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 HelloUser DeleteByID 根据 ID 删除 HelloUser DELETE /api/v1/hellouser/${param0} */
export async function deleteHellouserId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteHellouserIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/hellouser/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 HelloUser CreateBulk 批量创建 HelloUser POST /api/v1/hellouser/bulk */
export async function postHellouserBulk(body: API.HelloUser[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.HelloUser[] }>('/api/v1/hellouser/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 HelloUser DeleteBulk 根据 IDs 批量删除 HelloUser POST /api/v1/hellouser/bulk/delete */
export async function postHellouserBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/hellouser/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
