// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 Group Query 根据指定字段、时间范围查询或搜索 Group GET /api/v1/group */
export async function getGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getGroupParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.ProtectGroup[] }>('/api/v1/group', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 Group Create 创建 Group POST /api/v1/group */
export async function postGroup(body: API.ProtectGroup, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.ProtectGroup }>('/api/v1/group', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 Group QueryByID 根据 ID 查询 Group GET /api/v1/group/${param0} */
export async function getGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getGroupIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.ProtectGroup }>(`/api/v1/group/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 Group UpdateByID 根据 ID 修改 Group PUT /api/v1/group/${param0} */
export async function putGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putGroupIdParams,
  body: API.ProtectGroup,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.ProtectGroup }>(`/api/v1/group/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 Group DeleteByID 根据 ID 删除 Group DELETE /api/v1/group/${param0} */
export async function deleteGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteGroupIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/group/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 Group CreateBulk 批量创建 Group POST /api/v1/group/bulk */
export async function postGroupBulk(body: API.ProtectGroup[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.ProtectGroup[] }>('/api/v1/group/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 Group DeleteBulk 根据 IDs 批量删除 Group POST /api/v1/group/bulk/delete */
export async function postGroupBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/group/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
