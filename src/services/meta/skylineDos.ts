// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 SkylineDos Query 根据指定字段、时间范围查询或搜索 SkylineDos GET /api/v1/skylinedos */
export async function getSkylinedos(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSkylinedosParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.SkylineDos[] }>('/api/v1/skylinedos', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 SkylineDos Create 创建 SkylineDos POST /api/v1/skylinedos */
export async function postSkylinedos(body: API.SkylineDos, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.SkylineDos }>('/api/v1/skylinedos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 SkylineDos QueryByID 根据 ID 查询 SkylineDos GET /api/v1/skylinedos/${param0} */
export async function getSkylinedosId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSkylinedosIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SkylineDos }>(`/api/v1/skylinedos/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 SkylineDos UpdateByID 根据 ID 修改 SkylineDos PUT /api/v1/skylinedos/${param0} */
export async function putSkylinedosId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putSkylinedosIdParams,
  body: API.SkylineDos,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.SkylineDos }>(`/api/v1/skylinedos/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 SkylineDos DeleteByID 根据 ID 删除 SkylineDos DELETE /api/v1/skylinedos/${param0} */
export async function deleteSkylinedosId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSkylinedosIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/skylinedos/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 SkylineDos CreateBulk 批量创建 SkylineDos POST /api/v1/skylinedos/bulk */
export async function postSkylinedosBulk(body: API.SkylineDos[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.SkylineDos[] }>('/api/v1/skylinedos/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 SkylineDos DeleteBulk 根据 IDs 批量删除 SkylineDos POST /api/v1/skylinedos/bulk/delete */
export async function postSkylinedosBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/skylinedos/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
