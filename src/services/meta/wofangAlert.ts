// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 WofangAlert Query 根据指定字段、时间范围查询或搜索 WofangAlert GET /api/v1/wofangalert */
export async function getWofangalert(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangalertParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.WofangAlert[] }>('/api/v1/wofangalert', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 WofangAlert Create 创建 WofangAlert POST /api/v1/wofangalert */
export async function postWofangalert(body: API.WofangAlert, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.WofangAlert }>('/api/v1/wofangalert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 WofangAlert QueryByID 根据 ID 查询 WofangAlert GET /api/v1/wofangalert/${param0} */
export async function getWofangalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.WofangAlert }>(`/api/v1/wofangalert/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 WofangAlert UpdateByID 根据 ID 修改 WofangAlert PUT /api/v1/wofangalert/${param0} */
export async function putWofangalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putWofangalertIdParams,
  body: API.WofangAlert,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.WofangAlert }>(`/api/v1/wofangalert/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 WofangAlert DeleteByID 根据 ID 删除 WofangAlert DELETE /api/v1/wofangalert/${param0} */
export async function deleteWofangalertId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteWofangalertIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/wofangalert/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 WofangAlert CreateBulk 批量创建 WofangAlert POST /api/v1/wofangalert/bulk */
export async function postWofangalertBulk(
  body: API.WofangAlert[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.WofangAlert[] }>('/api/v1/wofangalert/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 WofangAlert DeleteBulk 根据 IDs 批量删除 WofangAlert POST /api/v1/wofangalert/bulk/delete */
export async function postWofangalertBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/wofangalert/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
