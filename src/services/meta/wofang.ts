// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 Wofang Query 根据指定字段、时间范围查询或搜索 Wofang GET /api/v1/wofang */
export async function getWofang(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.Wofang[] }>('/api/v1/wofang', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 Wofang Create 创建 Wofang POST /api/v1/wofang */
export async function postWofang(body: API.Wofang, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Wofang }>('/api/v1/wofang', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 Wofang QueryByID 根据 ID 查询 Wofang GET /api/v1/wofang/${param0} */
export async function getWofangId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Wofang }>(`/api/v1/wofang/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 Wofang UpdateByID 根据 ID 修改 Wofang PUT /api/v1/wofang/${param0} */
export async function putWofangId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putWofangIdParams,
  body: API.Wofang,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Wofang }>(`/api/v1/wofang/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 Wofang DeleteByID 根据 ID 删除 Wofang DELETE /api/v1/wofang/${param0} */
export async function deleteWofangId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteWofangIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/wofang/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Query 根据 牵引类型和ip 查询 沃防ip牵引状态 Query 根据 牵引类型和ip 查询 沃防ip牵引状态 GET /api/v1/wofang/api */
export async function getWofangApi(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangApiParams,
  options?: { [key: string]: any },
) {
  const { dragType: param0, ips: param1, ...queryParams } = params;
  return request<API.Result & { data?: API.Response }>('/api/v1/wofang/api', {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Add 将ip添加到沃防牵引或黑洞清洗 Add 将ip添加到沃防牵引或黑洞清洗 POST /api/v1/wofang/api */
export async function postWofangApi(body: API.Add, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Response }>('/api/v1/wofang/api', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** Delete 将ip从沃防牵引或黑洞清洗 删除 Delete 将ip从沃防牵引或黑洞清洗 删除 DELETE /api/v1/wofang/api/${param0}/${param1} */
export async function deleteWofangApiDragTypeIp(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteWofangApiDragTypeIpParams,
  options?: { [key: string]: any },
) {
  const { dragType: param0, ip: param1, ...queryParams } = params;
  return request<API.Result & { data?: API.Response }>(`/api/v1/wofang/api/${param0}/${param1}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Query 根据 ip 查询 沃防ip牵引状态 Query 根据 ip 查询 沃防ip牵引状态 GET /api/v1/wofang/api2 */
export async function getWofangApi2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWofangApi2Params,
  options?: { [key: string]: any },
) {
  const { ips: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Response }>('/api/v1/wofang/api2', {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Add 将ip添加到沃防牵引 Add 将ip添加到沃防牵引 POST /api/v1/wofang/api2 */
export async function postWofangApi2(body: API.Add, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Response }>('/api/v1/wofang/api2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** Delete 将ip从沃防牵引 删除 Delete 将ip从沃防牵引 删除 DELETE /api/v1/wofang/api2/${param0} */
export async function deleteWofangApi2Ip(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteWofangApi2IpParams,
  options?: { [key: string]: any },
) {
  const { ip: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.Response }>(`/api/v1/wofang/api2/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 Wofang CreateBulk 批量创建 Wofang POST /api/v1/wofang/bulk */
export async function postWofangBulk(body: API.Wofang[], options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.Wofang[] }>('/api/v1/wofang/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 Wofang DeleteBulk 根据 IDs 批量删除 Wofang POST /api/v1/wofang/bulk/delete */
export async function postWofangBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/wofang/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
