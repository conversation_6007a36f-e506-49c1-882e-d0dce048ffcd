// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as casbinRule from './casbinRule';
import * as chart from './chart';
import * as cleanData from './cleanData';
import * as cloudAlert from './cloudAlert';
import * as cloudAttackData from './cloudAttackData';
import * as cloudFlowData from './cloudFlowData';
import * as dataSync from './dataSync';
import * as group from './group';
import * as helloUser from './helloUser';
import * as matrixSpectrumAlert from './matrixSpectrumAlert';
import * as matrixSpectrumData from './matrixSpectrumData';
import * as matrixStrategy from './matrixStrategy';
import * as nds from './nds';
import * as notify from './notify';
import * as protectGroup from './protectGroup';
import * as skylineDos from './skylineDos';
import * as socGroup from './socGroup';
import * as socGroupTicket from './socGroupTicket';
import * as spectrumAlert from './spectrumAlert';
import * as spectrumData from './spectrumData';
import * as strategy from './strategy';
import * as systemApi from './systemApi';
import * as systemConfig from './systemConfig';
import * as tenant from './tenant';
import * as user from './user';
import * as userOperationLog from './userOperationLog';
import * as wofang from './wofang';
import * as wofangAlert from './wofangAlert';
export default {
  casbinRule,
  chart,
  cleanData,
  cloudAlert,
  cloudAttackData,
  cloudFlowData,
  dataSync,
  group,
  helloUser,
  matrixSpectrumAlert,
  matrixSpectrumData,
  matrixStrategy,
  nds,
  notify,
  protectGroup,
  skylineDos,
  socGroup,
  socGroupTicket,
  spectrumAlert,
  spectrumData,
  strategy,
  systemApi,
  systemConfig,
  tenant,
  user,
  userOperationLog,
  wofang,
  wofangAlert,
};
