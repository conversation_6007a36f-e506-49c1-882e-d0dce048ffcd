// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 CloudFlowData Query 根据指定字段、时间范围查询或搜索 CloudFlowData GET /api/v1/cloudflowdata */
export async function getCloudflowdata(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudflowdataParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudFlowData[] }>('/api/v1/cloudflowdata', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 CloudFlowData Create 创建 CloudFlowData POST /api/v1/cloudflowdata */
export async function postCloudflowdata(body: API.CloudFlowData, options?: { [key: string]: any }) {
  return request<API.Result & { data?: API.CloudFlowData }>('/api/v1/cloudflowdata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 CloudFlowData QueryByID 根据 ID 查询 CloudFlowData GET /api/v1/cloudflowdata/${param0} */
export async function getCloudflowdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCloudflowdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudFlowData }>(`/api/v1/cloudflowdata/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** UpdateByID 根据 ID 修改 CloudFlowData UpdateByID 根据 ID 修改 CloudFlowData PUT /api/v1/cloudflowdata/${param0} */
export async function putCloudflowdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putCloudflowdataIdParams,
  body: API.CloudFlowData,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.CloudFlowData }>(`/api/v1/cloudflowdata/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** DeleteByID 根据 ID 删除 CloudFlowData DeleteByID 根据 ID 删除 CloudFlowData DELETE /api/v1/cloudflowdata/${param0} */
export async function deleteCloudflowdataId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCloudflowdataIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/cloudflowdata/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 CloudFlowData CreateBulk 批量创建 CloudFlowData POST /api/v1/cloudflowdata/bulk */
export async function postCloudflowdataBulk(
  body: API.CloudFlowData[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.CloudFlowData[] }>('/api/v1/cloudflowdata/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 CloudFlowData DeleteBulk 根据 IDs 批量删除 CloudFlowData POST /api/v1/cloudflowdata/bulk/delete */
export async function postCloudflowdataBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/cloudflowdata/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
