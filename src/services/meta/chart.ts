// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据 IP和起始时间 查询 分光流量信息 Query 根据 IP和起始时间 查询 分光流量信息 GET /api/v1/chart/spectrum */
export async function getChartSpectrum(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getChartSpectrumParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.LineData[] }>('/api/v1/chart/spectrum', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
