// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** Query 根据指定字段、时间范围查询或搜索 UserOperationLog Query 根据指定字段、时间范围查询或搜索 UserOperationLog GET /api/v1/useroperationlog */
export async function getUseroperationlog(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUseroperationlogParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.UserOperationLog[] }>('/api/v1/useroperationlog', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Create 创建 UserOperationLog Create 创建 UserOperationLog POST /api/v1/useroperationlog */
export async function postUseroperationlog(
  body: API.UserOperationLog,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.UserOperationLog }>('/api/v1/useroperationlog', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** QueryByID 根据 ID 查询 UserOperationLog QueryByID 根据 ID 查询 UserOperationLog GET /api/v1/useroperationlog/${param0} */
export async function getUseroperationlogId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUseroperationlogIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.UserOperationLog }>(
    `/api/v1/useroperationlog/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** UpdateByID 根据 ID 修改 UserOperationLog UpdateByID 根据 ID 修改 UserOperationLog PUT /api/v1/useroperationlog/${param0} */
export async function putUseroperationlogId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putUseroperationlogIdParams,
  body: API.UserOperationLog,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result & { data?: API.UserOperationLog }>(
    `/api/v1/useroperationlog/${param0}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** DeleteByID 根据 ID 删除 UserOperationLog DeleteByID 根据 ID 删除 UserOperationLog DELETE /api/v1/useroperationlog/${param0} */
export async function deleteUseroperationlogId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUseroperationlogIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Message>(`/api/v1/useroperationlog/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** CreateBulk 批量创建 UserOperationLog CreateBulk 批量创建 UserOperationLog POST /api/v1/useroperationlog/bulk */
export async function postUseroperationlogBulk(
  body: API.UserOperationLog[],
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.UserOperationLog[] }>('/api/v1/useroperationlog/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** DeleteBulk 根据 IDs 批量删除 UserOperationLog DeleteBulk 根据 IDs 批量删除 UserOperationLog POST /api/v1/useroperationlog/bulk/delete */
export async function postUseroperationlogBulkOpenApiDelete(
  body: API.DeleteItem,
  options?: { [key: string]: any },
) {
  return request<API.Message>('/api/v1/useroperationlog/bulk/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
