// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';


/** Query 根据指定字段、时间范围查询或搜索 ProtectGroup Query 根据指定字段、时间范围查询或搜索 ProtectGroup GET /api/v1/protectgroup */
export async function getCommonProtectgroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getProtectgroupParams,
  options?: { [key: string]: any },
) {
  return request<API.Result & { data?: API.ProtectGroup[] }>('/api/v1/common/protectgroup', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}