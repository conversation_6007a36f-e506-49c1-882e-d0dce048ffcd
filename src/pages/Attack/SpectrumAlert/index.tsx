import { ActionType, ProColumns, ProForm, ProFormTextArea } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { deleteSpectrumalertId, getSpectrumalert, postSpectrumalert, postSpectrumalertBulkOpenApiDelete, putSpectrumalertId } from '@/services/meta/spectrumAlert';
import { spectrumAlertColumns } from '@/columns/SpectrumAlert';
import { transformEndTime, transformStartTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import { Access, useAccess } from '@umijs/max';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import { getTenant } from '@/services/meta/tenant';


const IndexForm: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.SpectrumAlert[]>([]);
  const access = useAccess();
  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.ProtectGroup>[] = [
    ...spectrumAlertColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              remark: record.remark,
            }
          }
          //更新页面表单
          updateDetailPage={<>
            <Access
              accessible={access.canAdmin}
            >
              <>原项目：{record.edges?.tenant?.name}-{record.edges?.tenant?.code}</>
              <ProFormSelectLayout
                name="tenant_id"
                label="修改项目"
                search={getTenant}
                placeholder="请输入项目名称或代号进行搜索"
              // rules={[{ required: true, message: '请输入项目' }]}
              />
            </Access>
            <RemarkProFormTextArea />
          </>
          }
        />,
        //详情（跳转页面）
        <LinkLayout key="link" to={`/attack/alert/detail/${record.id}`} />,
        //查看
        // <ShowRowDetailLayout
        //   key="view"
        //   setShowDetailPage={setShowDetailPage}
        //   setShowDetail={setShowDetail}
        //   detailPage={<>
        //     <ProDescriptionsLayout data={record} columns={columns} column={1} />
        //   </>}
        // />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteSpectrumalertId}
          id={record.id}
        />,
      ],
    }
  ];
  const transformTime = (values: any) => {
    return {
      // ...transformCreateTime(values),
      ...transformStartTime(values),
      ...transformEndTime(values),
    }
  }

  //GET 数据转换，可以在查询时对字段进行特殊处理
  // const transformGetData = () => {
  // console.log(data)
  // return {
  // // 查询时，增加字段type，值为g
  //   type: 'g'
  // }
  // }


  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getSpectrumalert}
    editMethod={putSpectrumalertId}
    newMethod={postSpectrumalert}
    deleteBulkMethod={postSpectrumalertBulkOpenApiDelete}

    // //提交时（新建/编辑），将单行数据转换成数组
    // //默认flase，如果true，需要同时传入转换函数
    // transform={true}
    // transformPostData={transformPostData}
    // transformGetData={transformGetData}
    transformTime={transformTime}

    //多选操作
    //显示多选操作；默认显示
    showRowSelect={true}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}

    //单击行数据，增加背景色，双击取消；默认不生效
    //每次都会遍历所有行，有点消耗资源？
    // rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}

    //新建
    //显示新建按钮；默认显示
    showNew={false}
    // addModalDetail={addModalDetail}
    // setAddModalDetail={setAddModalDetail}
    // addDetailPage={addDetailPage}
    // setAddDetailPage={setAddDetailPage}
    // newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}
  />

};
export default IndexForm;
