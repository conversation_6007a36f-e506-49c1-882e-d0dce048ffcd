import { strategyColumns } from '@/columns/Strategy';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import {
  getStrategy,
  postStrategy,
  postStrategyBulkOpenApiDelete,
  putStrategyId,
} from '@/services/meta/strategy';
import { getTenant } from '@/services/meta/tenant';
import { transformTime } from '@/utils/transform';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormDigit,
  ProFormRadio,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const IndexForm: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.Strategy[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //编辑页面
  const editPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="name" label="名称" rules={[{ required: true }]} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormRadio.Group
          initialValue={10010}
          name="isp_code"
          label="IP类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: 1,
              label: 'BGP',
            },
            {
              value: 10010,
              label: '联通',
            },
            {
              value: 10000,
              label: '电信',
              disabled: true,
            },
            {
              value: 10086,
              label: '移动',
              disabled: true,
            },
          ]}
          radioType="button"
        />

        <ProFormRadio.Group
          initialValue={1}
          name="type"
          label="防护类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: 1,
              label: '联通沃防',
            },
            // {
            //   value: 10010,
            //   label: '联通沃防',
            // },
            {
              value: 10000,
              label: '电信',
              disabled: true,
            },
            {
              value: 10086,
              label: '移动',
              disabled: true,
            },
          ]}
          radioType="button"
        />
        <ProFormSwitch name="enabled" label="启用" />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          label="bps阈值"
          name="bps"
          initialValue={60000000000}
          min={60000000000}
          max={6000000000000}
          fieldProps={{
            precision: 0,
            step: 100000,
            formatter: (value) => `${value}bit`,
            parser: (value) => value!.replace('bit', '')
          }}
        />
        {/* <ProFormDigit
          label="bps次数"
          name="bps_count"
          initialValue={1}
          min={1}
          max={10}
          fieldProps={{ precision: 0 }}
        /> */}
        {/* </ProForm.Group>
      <ProForm.Group> */}
        <ProFormDigit
          label="pps阈值"
          name="pps"
          initialValue={60000000000}
          // min={60000000000}
          max={6000000000000}
          fieldProps={{
            precision: 0,
            step: 100000,
            formatter: (value) => `${value}bit`,
            parser: (value) => value!.replace('bit', '')
          }}
        />
        {/* <ProFormDigit
          label="pps次数"
          name="pps_count"
          initialValue={1}
          min={1}
          max={10}
          fieldProps={{ precision: 0 }}
        /> */}
      </ProForm.Group>
      <RemarkProFormTextArea />
    </>
  );

  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      {/* <ProFormSwitch name="system" label="系统策略" /> */}
      <ProFormSelectLayout
        name="tenant_id"
        label="项目"
        search={getTenant}
        placeholder="请输入项目名称或代号进行搜索"
        rules={[{ required: true, message: '请输入项目' }]}
      />
      {editPageDetail}
    </>
  );

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.Strategy>[] = [
    ...strategyColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}
          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={{
            //id必须设置
            id: record.id,
            enabled: record.enabled,
            name: record.name,
            type: record.type,
            isp_code: record.isp_code,
            bps: record.bps,
            bps_count: record.bps_count,
            pps: record.pps,
            pps_count: record.pps_count,
            remark: record.remark,
          }}
          //更新页面表单
          updateDetailPage={editPageDetail}
        />,
        //详情（跳转页面）
        // <LinkLayout key="link" to={`/protect/strategy/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={
            <>
              <ProDescriptionsLayout data={record} columns={columns} column={2} />
            </>
          }
        />,
        //删除
        // <PopconfirmDeleteLayout
        //   key="delete"
        //   actionRef={actionRef}
        //   api={deleteStrategyId}
        //   id={record.id}
        // />,
      ],
    },
  ];

  return (
    <ProTableLayout
      actionRef={actionRef}
      columns={columns}
      //Service 方法
      getMethod={getStrategy}
      editMethod={putStrategyId}
      newMethod={postStrategy}
      deleteBulkMethod={postStrategyBulkOpenApiDelete}
      //提交时（新建/编辑），将单行数据转换成数组
      //默认flase，如果true，需要同时传入转换函数
      transform={false}
      // transformPostData={transformPostData}
      transformTime={transformTime}
      //多选操作
      //显示多选操作；默认显示
      showRowSelect={true}
      selectedRowsState={selectedRowsState}
      setSelectedRows={setSelectedRows}
      //单击行数据，增加背景色，双击取消；默认不生效
      //每次都会遍历所有行，有点消耗资源？
      // rowSelectBackground={true}

      //显示分页栏；默认显示
      pagination={true}
      //显示搜索框；默认显示
      showSearch={true}
      //显示工具栏；默认显示
      /* showToolbar={false} */

      //新建
      //显示新建按钮；默认显示
      showNew={true}
      addModalDetail={addModalDetail}
      setAddModalDetail={setAddModalDetail}
      addDetailPage={addDetailPage}
      setAddDetailPage={setAddDetailPage}
      newPageDetail={newPageDetail}
      // 显示
      showDetail={showDetail}
      setShowDetail={setShowDetail}
      showDetailPage={showDetailPage}
      setShowDetailPage={setShowDetailPage}
      // 更新
      updateDetailPage={updateDetailPage}
      setUpdateDetailPage={setUpdateDetailPage}
      updateValues={updateValues}
      setUpdateValues={setUpdateValues}
      // 使用ModalForm或者使用DrawerForm更新
      updateDrawerDetail={updateDrawerDetail}
      setUpdateDrawerDetail={setUpdateDrawerDetail}
      updateModalDetail={updateModalDetail}
      setUpdateModalDetail={setUpdateModalDetail}
    />
  );
};
export default IndexForm;
