import { socGroupTicketColumns } from '@/columns/SocGroupTicket';
import { deleteSocgroupticketId, getSocgroupticket, postSocgroupticket, postSocgroupticketBulkOpenApiDelete, putSocgroupticketId } from '@/services/meta/socGroupTicket';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { transformCreateTime, transformOpTime, transformUpdatedTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { history, FormattedMessage, useAccess, Access } from '@umijs/max';


const IndexPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const access = useAccess();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.SocGroupTicket[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.SocGroupTicket>[] = [
    ...socGroupTicketColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              //如果没有备注，可以去掉
              remark: record.remark,
              //其他需要更新的字段可以在这里添加
            }
          }
          //更新页面表单
          updateDetailPage={<>
            {/*TODO：其他需要更新的字段的表单可以在这里添加*/}

            {/*如果没有备注，可以去掉*/}
            <RemarkProFormTextArea />
          </>}
        />,
        //详情（跳转页面）
        // TODO：根据需求修改路径
        // <LinkLayout key="link" to={`/process/ticket/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={<>
            <ProDescriptionsLayout data={record} columns={columns} column={2} />
          </>}
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteSocgroupticketId}
          id={record.id}
        />,
      ],
    }
  ];


  const transformPostData = (data: any) => {
    let divertTtype = { "清洗上线": 0, "清洗下线": 1, "清洗调优": 2 }
    return {
      // 提交时，将单行数据转换成数组
      ip_list: data['ip_list']?.split(/[(\r\n)\r\n]+/),
      product_name: data['product_name']?.split('-')[0],
      product_code: data['product_name'].split('-')[1],
      divert_type: divertTtype[data['divert_type']],
    }
  }

  const transformTime = (values: any) => {
    return {
      ...transformCreateTime(values),
      ...transformUpdatedTime(values),
      ...transformOpTime(values),
    }
  }

  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getSocgroupticket}
    editMethod={putSocgroupticketId}
    newMethod={postSocgroupticket}
    deleteBulkMethod={postSocgroupticketBulkOpenApiDelete}

    //提交时（新建/编辑），将单行数据转换成数组
    //默认false，如果true，需要同时传入转换函数
    transform={true}
    transformPostData={transformPostData}
    transformTime={transformTime}

    //多选操作
    //显示多选操作；默认显示
    showRowSelect={true}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}

    //单击行数据，增加背景色，双击取消；默认不生效
    //每次都会遍历所有行，有点消耗资源？
    // rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}
    //显示搜索框；默认显示
    showSearch={true}
    //显示工具栏；默认显示
    showToolbar={true}

    //新建
    //显示新建按钮；默认显示
    showNew={false}
    addModalDetail={addModalDetail}
    setAddModalDetail={setAddModalDetail}
    addDetailPage={addDetailPage}
    setAddDetailPage={setAddDetailPage}
    // newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}


    //使用自定义新建页面
    extratoolBar={
      <Access
        key="new"
        accessible={access.canNew}
      >
        <Button
          type="primary"
          key="add"
          onClick={() => {
            history.push('/process/ticket/new')
          }}
        >
          <PlusOutlined /> <FormattedMessage id="pages.searchTable.new" defaultMessage="New" />
        </Button>
      </Access>
    }
  />

};
export default IndexPage;