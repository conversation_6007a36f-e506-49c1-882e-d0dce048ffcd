import { socGroupTicketColumns } from '@/columns/SocGroupTicket';
import { deleteSocgroupticketId, getSocgroupticket, postSocgroupticket, postSocgroupticketBulkOpenApiDelete, putSocgroupticketId } from '@/services/meta/socGroupTicket';
import { ActionType, ProColumns, ProForm, ProFormDigit, ProFormInstance, ProFormRadio, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { transformTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import { getTenant } from '@/services/meta/tenant';


const IndexPage: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.SocGroupTicket[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  const formRef = useRef<ProFormInstance>();

  const editPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="name" label="工单标题" rules={[{ required: true }]} />
        <ProFormText name="ip" label="IP" rules={[{ required: true }]} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormRadio.Group
          initialValue={'qy'}
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: 'qy',
              label: '牵引清洗',
            },
            {
              value: 'hd',
              label: '黑洞',
            },
          ]}
          radioType="button"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          label="自动解封时长（秒）"
          name="un_drag_second"
          initialValue={30}
          min={5}
          max={6000000000000}
          fieldProps={{ precision: 0, step: 1 }}
          rules={[{ required: true, message: '请输入时间' }]}
        />
      </ProForm.Group>
      <ProForm.Group>

        <RemarkProFormTextArea />
      </ProForm.Group>
    </>
  )

  const description =
    `业务其他特性，信息越全面，误杀率越低，推荐使用以下模板
1、服务是否使用TCP/UDP/ICMP协议；
2、正常业务SYN/ACK/UDP/ICMP流量峰值；
3、如有TCP业务，是长连接还是短连接，数据包平均大小及交互过程；
4、如有UDP业务，请具体到应用层，比如是否使用DNS/NTP/SSDP协议等；
5、服务是否有Web业务；
6、如有Web业务，给出域名及日常PV，UV，QPS情况；
7、是否存在各类小包；
8、是否存在其他产品特性。`
  const configArgs =
    `(自定义防护阈值信息，可指定部分或全部，推荐使用以下模板) ：
监控阈值(手工影响分光报警，自动影响路由变化)：
1.目标IP总量(bps/pps)= ___bps / ___pps
2.目标IP-SYN总量(bps/pps)= ___bps / ___pps
3.目标IP-ACK总量(bps/pps)= ___bps / ___pps
4.目标IP-UDP总量(bps/pps)= ___bps / ___pps
5.目标IP-ICMP总量(bps/pps)= ___bps / ___pps
清洗阈值(影响误杀率和漏杀率，请谨慎填写)：
1.目标IP无需清洗能完全处理的带宽上限(Mbit/s) = ___ Mbit/s
2.目标IP能处理的TCP新建连接数上限(个/s) = ___ 个/s
3.目标IP能处理的TCP并发连接数上限(个/s) = ___ 个/s
4.目标IP设置的TCP协议keepalive超时时间(s) = ___ s
5.目标IP正常业务是否有海外IP访问(是/否) = ___`
  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      <ProFormSelectLayout
        name="tenant_id"
        label="项目"
        search={getTenant}
        placeholder="请输入项目名称或代号进行搜索"
      // rules={[{ required: true, message: '请输入项目' }]}
      />
      <ProFormRadio.Group
        name="department_id"
        initialValue={62}
        label="所属部门（集团SOC的）"
        rules={[{ required: true, message: '请选择权限类型' }]}
        options={[
          {
            value: 62,
            label: '在线游戏事业部',
          },
        ]}
        radioType="button"
        disabled
      />
      <ProForm.Group>
        <ProFormText name="name" label="工单标题" rules={[{ required: true }]} width="xl" disabled initialValue={"【申请】"} />
        <ProFormText name="type" label="类型" hidden initialValue={"DOSDIVERT"} />

      </ProForm.Group>

      <ProForm.Group>
        <ProFormSelectLayout
          name="product_name"
          label="产品"
          search={getTenant}
          placeholder="请输入项目名称或代号进行搜索"
          valueKey="name"
          valueKey2="code"
          valueSize={2}
          rules={[{ required: true, message: '请输入产品' }]}

        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormRadio.Group
          name="divert_type"
          label="清洗类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: "清洗上线",
              label: '清洗上线',
            },
            {
              value: '清洗下线',
              label: '清洗下线',
            },
            {
              value: '清洗调优',
              label: '清洗调优',
            },
          ]}
          radioType="button"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea
          name="ip_list"
          label="IP列表"
          width="md"
          rules={[
            { required: true },
            { pattern: new RegExp(/^[0-9-./\n]*$/, 'g'), message: '格式错误' },
          ]}
        />
        {/* <ProFormText name="min_bandwidth" label="牵引IP列表中最小带宽" tooltip="IP列表中最小的物理带宽或压测带宽(非业务流量)" /> */}
        <ProFormDigit
          label="牵引IP列表中最小带宽"
          tooltip="IP列表中最小的物理带宽或压测带宽(非业务流量)；支持2位小数"
          name="min_bandwidth"
          initialValue={0}
          min={0}
          max={500}
          fieldProps={{
            precision: 2,
            formatter: (value) => `${value}Gbps`,
            parser: (value) => value!.replace('Gbps', '')
          }}
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormRadio.Group
          name="op_type"
          label="操作方式"
          rules={[{ required: true, message: '请选择方式' }]}
          options={[
            {
              value: 0,
              label: '自动（推荐）',
            },
            {
              value: 1,
              label: '手动',
            },
          ]}
          radioType="button"
        />
        <ProFormText name="op_time" label="操作时间" />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormRadio.Group
          name="config_type"
          label="参数配置类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: 0,
              label: '默认（推荐）',
            },
            {
              value: 1,
              label: '自定义防护阈值',
            },
          ]}
          radioType="button"
          tooltip="默认：由集团安全部根据分光历史数据确定各项防护参数"
        />
      </ProForm.Group>
      <ProForm.Group>
        < ProFormTextArea name="config_args" label="自定义防护参数配置" width="xl" initialValue={configArgs}
          fieldProps={{ rows: 15 }} />
      </ProForm.Group>

      <ProFormText name="contact_list" label="紧急联系人列表" />
      <ProFormText name="follow_list" label="集团SOC跟踪者ID列表" />

      <ProFormTextArea name="description" label="描述信息" width="xl" initialValue={description}
        fieldProps={{ rows: 10 }}
      />

      <ProForm.Group>

        <RemarkProFormTextArea />
      </ProForm.Group>
    </>
  )


  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.SocGroupTicket>[] = [
    ...socGroupTicketColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              //如果没有备注，可以去掉
              remark: record.remark,
              //其他需要更新的字段可以在这里添加
            }
          }
          //更新页面表单
          updateDetailPage={<>
            {/*TODO：其他需要更新的字段的表单可以在这里添加*/}

            {/*如果没有备注，可以去掉*/}
            <RemarkProFormTextArea />
          </>}
        />,
        //详情（跳转页面）
        // TODO：根据需求修改路径
        // <LinkLayout key="link" to={`/process/ticket/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={<>
            <ProDescriptionsLayout data={record} columns={columns} column={2} />
          </>}
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteSocgroupticketId}
          id={record.id}
        />,
      ],
    }
  ];



  const transformPostData = (data: any) => {
    let divertTtype = { "清洗上线": 0, "清洗下线": 1, "清洗调优": 2 }
    if (data['divert_type'])
      return {
        // 提交时，将单行数据转换成数组
        ip_list: data['ip_list']?.split(/[(\r\n)\r\n]+/),
        product_name: data['product_name']?.split('-')[0],
        product_code: data['product_name'].split('-')[1],
        divert_type: divertTtype[data['divert_type']],
      }
  }


  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getSocgroupticket}
    editMethod={putSocgroupticketId}
    newMethod={postSocgroupticket}
    deleteBulkMethod={postSocgroupticketBulkOpenApiDelete}

    //提交时（新建/编辑），将单行数据转换成数组
    //默认false，如果true，需要同时传入转换函数
    transform={true}
    transformPostData={transformPostData}
    transformTime={transformTime}

    //多选操作
    //显示多选操作；默认显示
    showRowSelect={true}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}

    //单击行数据，增加背景色，双击取消；默认不生效
    //每次都会遍历所有行，有点消耗资源？
    // rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}
    //显示搜索框；默认显示
    showSearch={true}
    //显示工具栏；默认显示
    showToolbar={true}

    //新建
    //显示新建按钮；默认显示
    showNew={true}
    addModalDetail={addModalDetail}
    setAddModalDetail={setAddModalDetail}
    addDetailPage={addDetailPage}
    setAddDetailPage={setAddDetailPage}
    newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}
  // onModalValuesChange={(values) => {
  //   //设置标题
  //   if (values.product_name || values.divert_type) {
  //     let productName, type
  //     productName = formRef?.current?.getFieldValue('product_name')?.split('-')[0]
  //     if (productName === undefined) {
  //       productName = ""
  //     }
  //     type = formRef?.current?.getFieldValue('divert_type')
  //     if (type === undefined) {
  //       type = ""
  //     }
  //     formRef?.current?.setFieldsValue({
  //       // name: "【" + productName + "】申请【" + type + "】",
  //       name: productName + "【申请】" + type
  //     });
  //   }

  // }}
  // modalFormRef={formRef}
  />

};
export default IndexPage;
