import { contactListColumns } from "@/columns/SocGroupTicket";
import InfoLayout from "@/layouts/extend/InfoLayout";
import ProFormSelectLayout from "@/layouts/extend/ProFormSelectLayout";
import RemarkProFormTextArea from "@/layouts/form/RemarkProFormTextArea";
import { handleAdd } from "@/services/common";
import { postSocgroupticket } from "@/services/meta/socGroupTicket";
import { getTenant } from "@/services/meta/tenant";
import { expandData, ipListPlaceholder, timeFormat } from "@/utils/util";
import { EditableProTable, PageContainer, ProCard, ProColumns, ProForm, ProFormDateTimePicker, ProFormDigit, ProFormInstance, ProFormRadio, ProFormText, ProFormTextArea } from "@ant-design/pro-components";
import { useRef, useState } from "react";


const upDescription =
    `业务其他特性，信息越全面，误杀率越低，推荐使用以下模板
1、服务是否使用TCP/UDP/ICMP协议；
2、正常业务SYN/ACK/UDP/ICMP流量峰值；
3、如有TCP业务，是长连接还是短连接，数据包平均大小及交互过程；
4、如有UDP业务，请具体到应用层，比如是否使用DNS/NTP/SSDP协议等；
5、服务是否有Web业务；
6、如有Web业务，给出域名及日常PV，UV，QPS情况；
7、是否存在各类小包；
8、是否存在其他产品特性。`
const configArgs =
    `(自定义防护阈值信息，可指定部分或全部，推荐使用以下模板) ：
监控阈值(手工影响分光报警，自动影响路由变化)：
1.目标IP总量(bps/pps)= ___bps / ___pps
2.目标IP-SYN总量(bps/pps)= ___bps / ___pps
3.目标IP-ACK总量(bps/pps)= ___bps / ___pps
4.目标IP-UDP总量(bps/pps)= ___bps / ___pps
5.目标IP-ICMP总量(bps/pps)= ___bps / ___pps
清洗阈值(影响误杀率和漏杀率，请谨慎填写)：
1.目标IP无需清洗能完全处理的带宽上限(Mbit/s) = ___ Mbit/s
2.目标IP能处理的TCP新建连接数上限(个/s) = ___ 个/s
3.目标IP能处理的TCP并发连接数上限(个/s) = ___ 个/s
4.目标IP设置的TCP协议keepalive超时时间(s) = ___ s
5.目标IP正常业务是否有海外IP访问(是/否) = ___`

const ipListTxtPlaceholder =
    `一行一条数据，支持IPv4及IPv6，IP(IPv6)段，IP(IPv6)CIDR，且不能包含 空格 空行
IP段的IPv6最后不能是::
示例：
*******
*******/32
*******-12
*******-********
2403:c80:200:1005::2aba:c06
2403:c80:200:1005::/96
2403:c80:200:1005::2aba:c06-ceb
2403:c80:200:1005::2aba:c06-2403:c80:200:1005::2aba:ceb
`
const donwDescription =
    `下线原因，补充说明信息
`
const tuningDescription =
    `故障信息模板：
1.故障时间：2023.02.03 18:02:04 - 2023.03.04 20:03:05 
2.故障现象：完全中断/丢包/浙江省中断 
3.故障影响：用户数2000全断，连接数下降1000，故障占比50%。
`
const defaultData: any[] = [{}]

const transformPostData = (data: any) => {
    let divertTtype = { "清洗上线": 1, "清洗下线": 2, "清洗调优": 3 }
    let followIds: number[] = []
    if (data['follow_list']) {
        let follows = data['follow_list']?.split(/[(\r\n)\r\n]+/)
        follows.forEach((element: string) => {
            followIds.push(parseInt(element))
        });
    }
    return {
        // 提交时，将单行数据转换成数组
        ip_list: data['ip_list']?.split(/[(\r\n)\r\n]+/),
        product_name: data['product_name']?.split('-')[0],
        product_code: data['product_name'].split('-')[1],
        divert_type: divertTtype[data['divert_type']],
        follow_list: followIds,
    }
}
const columns: ProColumns<API.User2>[] = [
    ...contactListColumns,
    {
        title: '操作',
        valueType: 'option',
    }
]
const AddTicketForm: React.FC<any> = () => {
    const formRef = useRef<ProFormInstance>();

    const [dragType, setDragType] = useState<string>('');
    const [opType, setOpType] = useState<number>();
    const [configType, setConfigType] = useState<number>();

    const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
        defaultData.map((item) => item.id),
    );

    const newPageDetail = (
        <>
            <ProForm.Group>
                <ProFormSelectLayout
                    name="tenant_id"
                    label="项目"
                    search={getTenant}
                    placeholder="请输入项目名称或代号进行搜索"
                // rules={[{ required: true, message: '请输入项目' }]}
                />
                <ProFormRadio.Group
                    name="department_id"
                    label="所属部门（集团SOC的）"
                    rules={[{ required: true, message: '请选择权限类型' }]}
                    options={[
                        {
                            value: 62,
                            label: '在线游戏事业部',
                        },
                    ]}
                    radioType="button"
                    disabled
                />
                <ProForm.Group>
                    <ProFormTextArea name="follow_list" label="集团SOC跟踪者ID列表" placeholder="一行一个ID" initialValue={"5314"} />
                </ProForm.Group>
            </ProForm.Group>
            <ProForm.Group>
                <ProFormText name="name" label="工单标题" rules={[{ required: true }]} width="xl" disabled />
                <ProFormText name="type" label="类型" hidden />

            </ProForm.Group>

            <ProForm.Group>
                <ProFormSelectLayout
                    name="product_name"
                    label="产品"
                    search={getTenant}
                    placeholder="请输入项目名称或代号进行搜索"
                    valueKey="name"
                    valueKey2="code"
                    valueSize={2}
                    rules={[{ required: true, message: '请输入产品' }]}

                />
            </ProForm.Group>
            <ProForm.Group>
                <ProFormRadio.Group
                    name="divert_type"
                    label="清洗类型"
                    rules={[{ required: true, message: '请选择类型' }]}
                    options={[
                        {
                            value: "清洗上线",
                            label: '清洗上线',
                        },
                        {
                            value: '清洗下线',
                            label: '清洗下线',
                        },
                        {
                            value: '清洗调优',
                            label: '清洗调优',
                        },
                    ]}
                    radioType="button"
                />
            </ProForm.Group>
            <ProForm.Group>
                <ProFormTextArea
                    name="ip_list"
                    label={<InfoLayout prefixText="IP列表" content={ipListPlaceholder} />}
                    width="xl"
                    rules={[
                        { required: true },
                        { pattern: new RegExp(/^[0-9-./\n]*$/, 'g'), message: '格式错误' },
                    ]}
                    placeholder={ipListTxtPlaceholder}
                    fieldProps={{ rows: 10 }}
                />

                {/* <ProFormText name="min_bandwidth" label="牵引IP列表中最小带宽" tooltip="IP列表中最小的物理带宽或压测带宽(非业务流量)" /> */}
                {(dragType === "清洗上线" || dragType === "清洗调优") && (
                    <>
                        <ProFormDigit
                            label="牵引IP列表中最小带宽"
                            tooltip="IP列表中最小的物理带宽或压测带宽(非业务流量)；支持2位小数"
                            name="min_bandwidth"
                            initialValue={0}
                            min={0}
                            max={500}
                            fieldProps={{
                                precision: 2,
                                formatter: (value) => `${value}Gbps`,
                                parser: (value) => value!.replace('Gbps', '')
                            }}
                        />
                    </>
                )}
                {dragType === "清洗调优" && (
                    <b style={{ color: 'red' }} > 该IP所在群组阈值将统一调整（如果只需修改或删除防护群组IP，可在防护群组编辑）</b>
                )}
            </ProForm.Group>


            <ProForm.Group>
                {dragType === "清洗上线" && (
                    <ProFormRadio.Group
                        name="op_type"
                        label="操作方式(牵引触发方式)"
                        rules={[{ required: true, message: '请选择方式' }]}
                        options={[
                            {
                                value: 1,
                                label: '自动（推荐）',
                            },
                            {
                                value: 2,
                                label: '手动',
                            },
                        ]}
                        radioType="button"
                    />
                )}
                {(dragType === "清洗下线" || dragType === "清洗调优") && (
                    <ProFormRadio.Group
                        name="op_type"
                        label="操作方式(牵引触发方式)"
                        rules={[{ required: true, message: '请选择方式' }]}
                        options={[
                            {
                                value: 1,
                                label: '自动（推荐）',
                                disabled: true,
                            },
                            {
                                value: 2,
                                label: '手动',
                            },
                        ]}
                        radioType="button"
                    />
                )}


                {opType === 2 && (
                    <>
                        {/* <ProFormText name="op_time" label="操作时间" /> */}
                        <ProFormDateTimePicker
                            name="op_time"
                            label="操作时间"
                            fieldProps={{
                                format: (value) => value.format(timeFormat),
                            }}
                        />
                    </>
                )}
            </ProForm.Group>

            {
                (dragType === "清洗上线" || dragType === "清洗调优") && (
                    <ProForm.Group>
                        <ProFormRadio.Group
                            name="config_type"
                            label="参数配置类型"
                            rules={[{ required: true, message: '请选择类型' }]}
                            options={[
                                {
                                    value: 1,
                                    label: '默认（推荐）',
                                },
                                {
                                    value: 2,
                                    label: '自定义防护阈值',
                                },
                            ]}
                            radioType="button"
                        />
                    </ProForm.Group>
                )
            }
            {
                configType === 1 && (
                    < b style={{ color: 'red' }}>由集团安全部根据分光历史数据确定各项防护参数</b>
                )
            }
            {
                configType === 2 && (
                    <ProForm.Group>
                        < ProFormTextArea name="config_args" label="自定义防护参数配置" width="xl"
                            fieldProps={{ rows: 13 }} placeholder={configArgs}
                            rules={[{ required: true, message: '请输入自定义防护参数' }]}
                        />
                    </ProForm.Group>
                )
            }


            <ProForm.Item
                label="应急接口人"
                name="contact_list"
                trigger="onValuesChange"
                rules={[{ required: true, message: '请输入应急接口人' }]}
            >
                <EditableProTable<API.User2>
                    rowKey="id"
                    toolBarRender={false}
                    columns={columns}
                    recordCreatorProps={{
                        newRecordType: 'dataSource',
                        position: 'top',
                        record: () => ({ id: Date.now() }),
                    }}
                    editable={{
                        type: 'multiple',
                        editableKeys,
                        onChange: setEditableRowKeys,
                        actionRender: (row, _, dom) => {
                            return [dom.delete];
                        },
                    }}
                />
            </ProForm.Item>
            {
                (dragType === "清洗上线") && (
                    <ProFormTextArea name="description" label="描述信息" width="xl"
                        fieldProps={{ rows: 9 }}
                        placeholder={upDescription}
                    />
                )
            }

            {
                (dragType === "清洗下线") && (
                    <ProFormTextArea name="description" label="描述信息" width="xl"
                        fieldProps={{ rows: 9 }}
                        placeholder="下线原因，补充说明信息"
                    />
                )
            }
            {
                (dragType === "清洗调优") && (
                    <ProFormTextArea name="description" label="描述信息" width="xl"
                        fieldProps={{ rows: 9 }}
                        placeholder={
                            `故障信息模板：
1.故障时间：2023.02.03 18:02:04 - 2023.03.04 20:03:05 
2.故障现象：完全中断/丢包/浙江省中断 
3.故障影响：用户数2000全断，连接数下降1000，故障占比50%。
`}
                    />
                )
            }


            <ProForm.Group>
                <RemarkProFormTextArea />
            </ProForm.Group>
        </>
    )

    return (<>
        <PageContainer>
            <ProCard>
                <ProForm
                    formRef={formRef}
                    style={{ margin: 'auto', marginTop: 8, maxWidth: 800 }}
                    layout="vertical"
                    initialValues={{
                        // config_args: configArgs,
                        // description: description,
                        department_id: 62,
                        // config_type: 0,
                        // op_type: 0,
                        type: "DOSDIVERT",
                        name: "【申请】",
                        // divert_type: "清洗上线",

                    }}
                    submitter={{
                        render: (props, doms) => { return [...doms] }
                    }}
                    onFinish={async (values) => {
                        //将单行数据转换成数组
                        const data = expandData(true, values, transformPostData)
                        const success = await handleAdd(postSocgroupticket, data);
                        if (success) {
                            formRef.current?.resetFields();
                            history.back();
                        }
                    }}
                    onValuesChange={(values) => {
                        //设置标题
                        if (values.product_name || values.divert_type) {
                            let productName, type
                            productName = formRef?.current?.getFieldValue('product_name')?.split('-')[0]
                            if (productName === undefined) {
                                productName = ""
                            }
                            type = formRef?.current?.getFieldValue('divert_type')
                            if (type === undefined) {
                                type = ""
                            }
                            formRef?.current?.setFieldsValue({
                                // name: "【" + productName + "】申请【" + type + "】",
                                name: productName + "【申请】" + type
                            });
                        }
                        //清洗类型
                        if (values.divert_type) {
                            setDragType(values.divert_type)
                            if (values.divert_type === "清洗上线") {
                                formRef?.current?.setFieldsValue({ description: upDescription });
                            }
                            if (values.divert_type === "清洗下线") {
                                formRef?.current?.setFieldsValue({ description: donwDescription });
                            }
                            if (values.divert_type === "清洗调优") {
                                formRef?.current?.setFieldsValue({ description: tuningDescription });
                            }
                            if (values.divert_type === "清洗下线" || values.divert_type === "清洗调优") {
                                formRef?.current?.setFieldsValue({ op_type: 2 });
                                setOpType(2)
                            }
                        }
                        //操作方式-自动
                        if (values.op_type === 1) {
                            setOpType(1)
                        }
                        //操作方式-手动
                        if (values.op_type === 2) {
                            setOpType(2)
                        }
                        //参数配置类型-默认
                        if (values.config_type === 1) {
                            setConfigType(1)
                        }
                        //参数配置类型-自定义防护阈值
                        if (values.config_type === 2) {
                            setConfigType(2)
                            formRef?.current?.setFieldsValue({ config_args: configArgs });
                        }
                    }}
                >
                    {newPageDetail}
                </ProForm>
            </ProCard>
        </PageContainer>
    </>
    );
};
export default AddTicketForm;
