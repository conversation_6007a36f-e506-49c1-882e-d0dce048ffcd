import { cleanDataColumns } from '@/columns/CleanData';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import {
  deleteCleandataId,
  getCleandata,
  postCleandata,
  postCleandataBulkOpenApiDelete,
  putCleandataId,
} from '@/services/meta/cleanData';
import { transformCreateTime, transformTimeTime } from '@/utils/transform';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef, useState } from 'react';

const IndexForm: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.CleanData[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.CleanData>[] = [
    ...cleanDataColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //详情（跳转页面）
        // <LinkLayout key="link" to={`/data/clean/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={
            <>
              <ProDescriptionsLayout data={record} columns={columns} column={2} />
            </>
          }
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteCleandataId}
          id={record.id}
        />,
      ],
    },
  ];

  const transformTime = (values: any) => ({
    ...transformCreateTime(values),
    ...transformTimeTime(values),
  });
  return (
    <ProTableLayout
      actionRef={actionRef}
      columns={columns}
      //Service 方法
      getMethod={getCleandata}
      editMethod={putCleandataId}
      newMethod={postCleandata}
      deleteBulkMethod={postCleandataBulkOpenApiDelete}
      //提交时（新建/编辑），将单行数据转换成数组
      //默认flase，如果true，需要同时传入转换函数
      // transform={true}
      // transformPostData={transformPostData}
      transformTime={transformTime}
      //多选操作
      //显示多选操作；默认显示
      showRowSelect={true}
      selectedRowsState={selectedRowsState}
      setSelectedRows={setSelectedRows}
      //单击行数据，增加背景色，双击取消；默认不生效
      //每次都会遍历所有行，有点消耗资源？
      // rowSelectBackground={true}

      //显示分页栏；默认显示
      pagination={true}
      //新建
      //显示新建按钮；默认显示
      showNew={false}
      addModalDetail={addModalDetail}
      setAddModalDetail={setAddModalDetail}
      addDetailPage={addDetailPage}
      setAddDetailPage={setAddDetailPage}
      // newPageDetail={newPageDetail}

      // 显示
      showDetail={showDetail}
      setShowDetail={setShowDetail}
      showDetailPage={showDetailPage}
      setShowDetailPage={setShowDetailPage}
      // 更新
      updateDetailPage={updateDetailPage}
      setUpdateDetailPage={setUpdateDetailPage}
      updateValues={updateValues}
      setUpdateValues={setUpdateValues}
      // 使用ModalForm或者使用DrawerForm更新
      updateDrawerDetail={updateDrawerDetail}
      setUpdateDrawerDetail={setUpdateDrawerDetail}
      updateModalDetail={updateModalDetail}
      setUpdateModalDetail={setUpdateModalDetail}
    />
  );
};
export default IndexForm;
