import { matrixStrategyColumns } from '@/columns/MatrixStrategy';
import { deleteMatrixstrategyId, getMatrixstrategy, postMatrixstrategy, postMatrixstrategyBulkOpenApiDelete, putMatrixstrategyId } from '@/services/meta/matrixStrategy';
import { ActionType, EditableProTable, ProColumns, ProForm, ProFormDigit, ProFormRadio, ProFormSwitch, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { transformTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import { notNullPlaceholder } from '@/utils/util';
import InfoLayout from '@/layouts/extend/InfoLayout';


const IndexPage: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.MatrixStrategy[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="name" label="名称" width="lg" rules={[{ required: true }]} />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormDigit
          label={<InfoLayout title="监控bps" prefixText="监控bps" content="超过阈值会在【攻击数据-机房出口告警】产生告警" />}
          name="monitor_bps"
          initialValue={1000000000}
          min={1000000000}
          max={6000000000000}
          fieldProps={{
            precision: 0,
            step: 100000,
            formatter: (value) => `${value}bit`,
            parser: (value) => value!.replace('bit', '')
          }}
        />
        <ProFormDigit
          label={<InfoLayout title="牵引bps" prefixText="牵引bps" content="超过阈值会把当前被攻击的IP提交到【被攻击IP防护类型】对应防护" />}
          name="drag_bps"
          initialValue={1000000000}
          min={1000000}
          max={6000000000000}
          fieldProps={{
            precision: 0,
            step: 100000,
            formatter: (value) => `${value}bit`,
            parser: (value) => value!.replace('bit', '')
          }}
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormRadio.Group
          label="区域"
          name="region"
          rules={[{ required: true }]}
          options={[
            {
              value: 'HangZhou',
              label: '杭州',
            },
          ]}
          radioType="button"
        />
        <ProFormRadio.Group
          label="类型"
          name="net_type"
          rules={[{ required: true }]}
          options={[
            {
              value: 'BGP',
              label: 'BGP',
            },
            {
              value: 'Static',
              label: 'Static',
            },
          ]}
          radioType="button"
        />
        <ProFormRadio.Group
          label="运营商"
          name="isp"
          rules={[{ required: true }]}
          options={[
            {
              value: 'ChinaUnicom',
              label: '联通',
            },
            {
              value: '无',
              label: '无',
            },
          ]}
          radioType="button"
        />
      </ProForm.Group>

      <ProForm.Group>
        {/* <ProFormSwitch name="enabled" label="启用" /> */}

        <ProFormRadio.Group
          name="drag_type"
          label={<InfoLayout title="被攻击IP防护类型" prefixText="被攻击IP防护类型" content="根据ip.netease.com查询IP的ISP并提交到防护" />}
          rules={[{ required: true, message: '请选择被攻击IP防护类型' }]}
          options={[
            {
              value: 1,
              label: '联通沃防',
            },
            // {
            //   value: 10010,
            //   label: '联通沃防',
            // },
            {
              value: 10000,
              label: '电信',
              disabled: true,
            },
            {
              value: 10086,
              label: '移动',
              disabled: true,
            },
            {
              value: -1,
              label: '无',
            },
          ]}
          radioType="button"
        />
        {/* <ProFormSwitch name="enabled" label="启用" /> */}
      </ProForm.Group>

      <ProForm.Group>
        <RemarkProFormTextArea />
      </ProForm.Group>

    </>

  )


  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.MatrixStrategy>[] = [
    ...matrixStrategyColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              name: record.name,
              region: record.region,
              net_type: record.net_type,
              isp: record.isp,
              monitor_bps: record.monitor_bps,
              drag_bps: record.drag_bps,
              drag_type: record.drag_type,
              remark: record.remark,
            }
          }
          //更新页面表单
          updateDetailPage={newPageDetail}
        />,
        //详情（跳转页面）
        // TODO：根据需求修改路径
        // <LinkLayout key="link" to={`/protect/matrix/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={<>
            <ProDescriptionsLayout data={record} columns={columns} column={2} />
          </>}
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteMatrixstrategyId}
          id={record.id}
        />,
      ],
    }
  ];



  //POST 数据转换，可以在提交时对字段进行特殊处理
  // const transformPostData = (data: any) => {
  //   return {
  //     // 提交时，将单行数据转换成数组
  //     interfaces: data['interfaces']?.split(/[(\r\n)\r\n]+/)
  //   }
  // }

  //GET 数据转换，可以在查询时对字段进行特殊处理
  // const transformGetData = () => {
  //   return {
  //   // 查询时，增加字段type，值为g
  //     type: 'g'
  //   }
  // }

  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getMatrixstrategy}
    editMethod={putMatrixstrategyId}
    newMethod={postMatrixstrategy}
    deleteBulkMethod={postMatrixstrategyBulkOpenApiDelete}


    //默认false，如果true，需要同时传入转换函数
    transform={true}
    transformTime={transformTime}
    //提交时（新建/编辑），数据转换：将单行数据转换成数组
    // transformPostData={transformPostData}
    // 查询时，数据转换
    // transformGetData={transformGetData}


    //多选操作
    //显示多选操作；默认显示
    showRowSelect={false}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}

    //单击行数据，增加背景色，双击取消；默认不生效
    //每次都会遍历所有行，有点消耗资源？
    // rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}
    //显示搜索框；默认显示
    showSearch={true}
    //显示工具栏；默认显示
    showToolbar={true}

    //新建
    //显示新建按钮；默认显示
    showNew={true}
    addModalDetail={addModalDetail}
    setAddModalDetail={setAddModalDetail}
    addDetailPage={addDetailPage}
    setAddDetailPage={setAddDetailPage}
    newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}
    // 编辑提交时，二次确认，默认否
    // secondConfirm={true}
    // 二次确认文本
    // secondConfirmContent={"是否继续"}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}
  />

};
export default IndexPage;
