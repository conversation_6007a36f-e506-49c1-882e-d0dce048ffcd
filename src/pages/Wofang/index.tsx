import { wofangColumns } from '@/columns/Wofang';
import { deleteWofangId, getWofang, postWofang, postWofangBulkOpenApiDelete, putWofangId } from '@/services/meta/wofang';
import { ActionType, ProColumns, ProForm, ProFormDateTimePicker, ProFormDigit, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { transformTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import { getTenant } from '@/services/meta/tenant';
import { timeFormat } from '@/utils/util';


const IndexPage: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.Wofang[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  const editPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="name" label="标题" rules={[{ required: true }]} />
        <ProFormText name="ip" label="IP" rules={[{ required: true }]} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormRadio.Group
          initialValue={'qy'}
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
          options={[
            {
              value: 'qy',
              label: '牵引清洗',
            },
            {
              value: 'hd',
              label: '黑洞',
              disabled: true,
            },
          ]}
          radioType="button"
        />
        <ProFormDateTimePicker
          name="start_time"
          label="牵引清洗开始时间"
          placeholder="默认当前时间开始"
          fieldProps={{
            format: (value) => value.format(timeFormat),
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          label="自动解封时长（秒，最小60秒）"
          name="un_drag_second"
          initialValue={1800}
          min={60}
          max={6000000000000}
          fieldProps={{ precision: 0, step: 1 }}
          rules={[{ required: true, message: '请输入时间' }]}
        />
      </ProForm.Group>
      <ProForm.Group>

        <RemarkProFormTextArea />
      </ProForm.Group>
    </>
  )


  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      <ProFormSelectLayout
        name="tenant_id"
        label="项目"
        search={getTenant}
        placeholder="请输入项目名称或代号进行搜索"
      // rules={[{ required: true, message: '请输入项目' }]}
      />
      {editPageDetail}
    </>
  )


  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.Wofang>[] = [
    ...wofangColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              // name: record.name,
              // ip: record.ip,
              // type: record.type,
              // un_drag_second: record.un_drag_second,
              remark: record.remark,
              //其他需要更新的字段可以在这里添加
            }
          }
          //更新页面表单
          updateDetailPage={<>
            {/* {editPageDetail} */}
            <RemarkProFormTextArea />
          </>}
        />,
        //详情（跳转页面）
        // TODO：根据需求修改路径
        // <LinkLayout key="link" to={`/process/wofang/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={<>
            <ProDescriptionsLayout data={record} columns={columns} column={2} />
          </>}
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteWofangId}
          id={record.id}
        />,
      ],
    }
  ];


  //提交时，将单行数据转换成数组
  // const transformPostData = (data: any) => {
  //   return {
  //     dataList: data['dataList']?.split(/[(\r\n)\r\n]+/)
  //   }
  // }

  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getWofang}
    editMethod={putWofangId}
    newMethod={postWofang}
    deleteBulkMethod={postWofangBulkOpenApiDelete}

    //提交时（新建/编辑），将单行数据转换成数组
    //默认false，如果true，需要同时传入转换函数
    transform={true}
    // transformPostData={transformPostData}
    transformTime={transformTime}

    //多选操作
    //显示多选操作；默认显示
    showRowSelect={true}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}

    //单击行数据，增加背景色，双击取消；默认不生效
    //每次都会遍历所有行，有点消耗资源？
    // rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}
    //显示搜索框；默认显示
    showSearch={true}
    //显示工具栏；默认显示
    showToolbar={true}

    //新建
    //显示新建按钮；默认显示
    showNew={true}
    addModalDetail={addModalDetail}
    setAddModalDetail={setAddModalDetail}
    addDetailPage={addDetailPage}
    setAddDetailPage={setAddDetailPage}
    newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}
  />

};
export default IndexPage;
