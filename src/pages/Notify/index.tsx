import { notifyColumns } from '@/columns/Notify';
import InfoLayout from '@/layouts/extend/InfoLayout';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import {
  deleteNotifyId,
  getNotify,
  postNotify,
  postNotifyBulkOpenApiDelete,
  putNotifyId,
} from '@/services/meta/notify';
import { getTenant } from '@/services/meta/tenant';
import { transformTime } from '@/utils/transform';
import { notNullPlaceholder } from '@/utils/util';
import { ActionType, ProColumns, ProForm, ProFormCheckbox, ProFormSwitch, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Access, useAccess } from '@umijs/max';
import { useRef, useState } from 'react';

const IndexPage: React.FC = () => {

  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.Notify[]>([]);
  const access = useAccess();

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  const saPage = (<>
    <ProForm.Group>
      <ProFormSwitch name="sa_notify_popo" label="通过泡泡通知项目SA列表" />
      <ProFormSwitch name="sa_notify_email" label="通过邮件通知项目SA列表" />
    </ProForm.Group>
  </>)
  const popoContent = <>
    一行一条数据，不能包含空行<br />
    受泡泡群组权限影响，要将消息发送到popo群组时，发送方需要在【workflow-人员管理-添加popo群】<br />
    链接：<a href='https://workflow2.x.netease.com/#/user_management' target="_blank" rel="noreferrer" >https://workflow2.x.netease.com/#/user_management</a>
  </>

  //编辑页面
  const editPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="name" label="名称" rules={[{ required: true }]} />
      </ProForm.Group>
      {/* <Access
        accessible={access.canAdmin}
      >
        <ProFormSwitch name="system" label="系统策略" />
      </Access> */}
      <ProForm.Group>
        <ProFormSwitch name="enabled" label="总开关" />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSwitch name="popo" label="泡泡通知" />
        <ProFormSwitch name="email" label="邮件通知" />
        <ProFormSwitch name="sms" label="短信通知" />
        <ProFormSwitch name="phone" label="电话通知" />
      </ProForm.Group>
      {/* <Access
        accessible={!access.canAdmin}
      > */}
      {saPage}
      {/* </Access> */}
      <ProForm.Group>

        <ProFormTextArea
          name="emails"
          label={<InfoLayout prefixText="邮件列表" />}
          width="md"
          placeholder={notNullPlaceholder}
        // rules={[{ required: true, message: '请输入邮件' }]}
        />



        <ProFormTextArea
          name="phones"
          label={<InfoLayout prefixText="电话列表" />}
          width="md"
          placeholder={notNullPlaceholder}
        />
        <ProFormTextArea
          name="popo_groups"
          label={<InfoLayout prefixText="泡泡群列表" content={popoContent} />}
          width="md"
          placeholder="一行一条数据；受泡泡群组权限影响，要将消息发送到popo群组时，发送方需要在【workflow-人员管理-添加popo群】"
        />
        <ProFormTextArea
          name="ip_whitelists"
          label={<InfoLayout prefixText="通知白名单IP列表" content="一行一条数据，不能包含空行；白名单中的IP将不再通知" />}
          width="md"
          placeholder={notNullPlaceholder}
        />
      </ProForm.Group>



      <RemarkProFormTextArea />
    </>
  );


  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.Notify>[] = [
    ...notifyColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}
          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={{
            //id必须设置
            id: record.id,
            name: record.name,
            enabled: record.enabled,
            popo: record.popo,
            email: record.email,
            sms: record.sms,
            phone: record.phone,
            remark: record.remark,
            sa_notify_email: record.sa_notify_email,
            sa_notify_popo: record.sa_notify_popo,
            emails: record.emails?.join('\n'),
            phones: record.phones?.join('\n'),
            popo_groups: record.popo_groups?.join('\n'),
            ip_whitelists: record.ip_whitelists?.join('\n'),
          }}
          //更新页面表单
          updateDetailPage={
            <>
              {editPageDetail}
            </>
          }
        />,
        //详情（跳转页面）
        // <LinkLayout key="link" to={`/notify/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={
            <>
              <ProDescriptionsLayout data={record} columns={columns} column={2} />
            </>
          }
        />,
        // //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteNotifyId}
          id={record.id}
        />,
      ],
    },
  ];

  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      <ProFormSelectLayout
        name="tenant_id"
        label="项目"
        search={getTenant}
        placeholder="请输入项目名称或代号进行搜索"

        rules={[{ required: true, message: '请输入项目' }]}
      />
      {editPageDetail}
    </>
  );

  //提交时，将单行数据转换成数组
  const transformPostData = (data: any) => {
    return {
      emails: data['emails']?.split(/[(\r\n)\r\n]+/),
      phones: data['phones']?.split(/[(\r\n)\r\n]+/),
      popo_groups: data['popo_groups']?.split(/[(\r\n)\r\n]+/),
      ip_whitelists: data['ip_whitelists']?.split(/[(\r\n)\r\n]+/)
    };
  };





  return (
    <>
      <ProTableLayout
        actionRef={actionRef}
        columns={columns}
        //Service 方法
        getMethod={getNotify}
        editMethod={putNotifyId}
        newMethod={postNotify}
        deleteBulkMethod={postNotifyBulkOpenApiDelete}
        //提交时（新建/编辑），将单行数据转换成数组
        //默认false，如果true，需要同时传入转换函数
        transform={true}
        transformPostData={transformPostData}
        transformTime={transformTime}
        //多选操作
        //显示多选操作；默认显示
        showRowSelect={true}
        selectedRowsState={selectedRowsState}
        setSelectedRows={setSelectedRows}
        //单击行数据，增加背景色，双击取消；默认不生效
        //每次都会遍历所有行，有点消耗资源？
        // rowSelectBackground={true}

        //显示分页栏；默认显示
        pagination={true}
        //显示搜索框；默认显示
        showSearch={true}
        //显示工具栏；默认显示
        showToolbar={true}
        //新建
        //显示新建按钮；默认显示
        showNew={true}
        addModalDetail={addModalDetail}
        setAddModalDetail={setAddModalDetail}
        addDetailPage={addDetailPage}
        setAddDetailPage={setAddDetailPage}
        newPageDetail={newPageDetail}
        // 显示
        showDetail={showDetail}
        setShowDetail={setShowDetail}
        showDetailPage={showDetailPage}
        setShowDetailPage={setShowDetailPage}
        // 更新
        updateDetailPage={updateDetailPage}
        setUpdateDetailPage={setUpdateDetailPage}
        updateValues={updateValues}
        setUpdateValues={setUpdateValues}
        // 使用ModalForm或者使用DrawerForm更新
        updateDrawerDetail={updateDrawerDetail}
        setUpdateDrawerDetail={setUpdateDrawerDetail}
        updateModalDetail={updateModalDetail}
        setUpdateModalDetail={setUpdateModalDetail}
      />

    </>
  );
};
export default IndexPage;
