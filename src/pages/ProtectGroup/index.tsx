import { protectGroupColumns } from '@/columns/ProtectGroup';
import InfoLayout from '@/layouts/extend/InfoLayout';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProFormSelectLayout from '@/layouts/extend/ProFormSelectLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import {
  deleteProtectgroupId,
  getProtectgroup,
  postProtectgroup,
  postProtectgroupBulkOpenApiDelete,
  putProtectgroupId,
} from '@/services/meta/protectGroup';
import { getTenant } from '@/services/meta/tenant';
import { transformTime } from '@/utils/transform';
import { ipListPlaceholder, notNullPlaceholder } from '@/utils/util';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Access, useAccess } from '@umijs/max';
import { Popover } from 'antd';
import { useRef, useState } from 'react';

const IndexForm: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.ProtectGroup[]>([]);
  const access = useAccess();
  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.ProtectGroup>[] = [
    ...protectGroupColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}
          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={{
            //id必须设置
            id: record.id,
            // tenant_id: record.tenant_id,
            //将数组转换成单行数据
            ip_list: record.ip_list?.join('\n'),
            remark: record.remark,
          }}
          //更新页面表单
          updateDetailPage={
            <>
              <Access
                accessible={access.canAdmin}
              >
                <>原项目：{record.edges?.tenant?.name}-{record.edges?.tenant?.code}</>
                <ProFormSelectLayout
                  name="tenant_id"
                  label="修改项目"
                  search={getTenant}
                  placeholder="请输入项目名称或代号进行搜索"
                // rules={[{ required: true, message: '请输入项目' }]}
                />
              </Access>
              <ProForm.Group>
                <ProFormTextArea
                  name="ip_list"
                  label={<InfoLayout prefixText="IP列表" content={ipListPlaceholder} />}
                  width="xl"
                  placeholder={notNullPlaceholder}
                  rules={[
                    { required: true },
                    { pattern: new RegExp(/^[0-9-a-zA-Z:./\n]*$/, 'g'), message: '格式错误' },
                  ]}
                  fieldProps={{ rows: 15 }}
                />

              </ProForm.Group>

              <RemarkProFormTextArea />
            </>
          }
        />,
        //详情（跳转页面）
        <LinkLayout key="link" to={`/protect/group/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={
            <>
              <ProDescriptionsLayout data={record} columns={columns} column={2} />
            </>
          }
        />,
        //删除
        // <PopconfirmDeleteLayout
        //   key="delete"
        //   actionRef={actionRef}
        //   api={deleteProtectgroupId}
        //   id={record.id}
        // />,
      ],
    },
  ];

  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <>
      <ProFormSelectLayout
        name="tenant_id"
        label="项目"
        search={getTenant}
        placeholder="请输入项目名称或代号进行搜索"
        rules={[{ required: true, message: '请输入项目' }]}
      />
      <ProForm.Group>
        <ProFormText name="groupName" label="群组名" rules={[{ required: true }]} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea
          name="ip_list"
          label="IP列表"
          width="md"
          rules={[
            { required: true },
            { pattern: new RegExp(/^[0-9-./\n]*$/, 'g'), message: '格式错误' },
          ]}
        />
      </ProForm.Group>
      {/* <ProFormText
        rules={[
          {
            required: true,
          },
          {
            min: 8,
            message: '密码不能少于8个字符',
          },
        ]}
        name="password"
        label="密码"
        tooltip="密码不能少于8个字符"
      /> */}
    </>
  );

  //提交时，将单行数据转换成数组
  const transformPostData = (data: any) => {
    return {
      ip_list: data['ip_list']?.split(/[(\r\n)\r\n]+/),
    }
  };

  return (
    <ProTableLayout
      actionRef={actionRef}
      columns={columns}
      //Service 方法
      getMethod={getProtectgroup}
      editMethod={putProtectgroupId}
      newMethod={postProtectgroup}
      deleteBulkMethod={postProtectgroupBulkOpenApiDelete}
      //提交时（新建/编辑），将单行数据转换成数组
      //默认flase，如果true，需要同时传入转换函数
      transform={true}
      transformPostData={transformPostData}
      transformTime={transformTime}
      //多选操作
      //显示多选操作；默认显示
      showRowSelect={true}
      selectedRowsState={selectedRowsState}
      setSelectedRows={setSelectedRows}
      //单击行数据，增加背景色，双击取消；默认不生效
      //每次都会遍历所有行，有点消耗资源？
      // rowSelectBackground={true}

      //显示分页栏；默认显示
      pagination={true}
      //新建
      //显示新建按钮；默认显示
      showNew={false}
      addModalDetail={addModalDetail}
      setAddModalDetail={setAddModalDetail}
      addDetailPage={addDetailPage}
      setAddDetailPage={setAddDetailPage}
      newPageDetail={newPageDetail}
      // 显示
      showDetail={showDetail}
      setShowDetail={setShowDetail}
      showDetailPage={showDetailPage}
      setShowDetailPage={setShowDetailPage}
      // 更新
      updateDetailPage={updateDetailPage}
      setUpdateDetailPage={setUpdateDetailPage}
      updateValues={updateValues}
      setUpdateValues={setUpdateValues}
      secondConfirm={true}
      secondConfirmContent={"防护群组IP列表将直接向NDS提交，实时生效，是否继续"}

      // 使用ModalForm或者使用DrawerForm更新
      updateDrawerDetail={updateDrawerDetail}
      setUpdateDrawerDetail={setUpdateDrawerDetail}
      updateModalDetail={updateModalDetail}
      setUpdateModalDetail={setUpdateModalDetail}
    />
  );
};
export default IndexForm;
