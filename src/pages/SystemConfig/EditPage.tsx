import { notNullPlaceholder } from '@/utils/util';
import { InfoCircleOutlined } from '@ant-design/icons';
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import { Popover } from 'antd';
import { useState } from 'react';

type EditTableProps = {
  columns: ProColumns<any>[];
  actionRef: any;
  dataSource?: any;

  //Service 方法
  getMethod: any;
  // editMethod: any;
  newMethod: any;
  // deleteMethod: any;
};


const EditPage: React.FC<EditTableProps> = (props) => {

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  return (
    <>
      <EditableProTable<any>
        rowKey="id"
        recordCreatorProps={
          {
            position: 'bottom',
            record: () => ({ id: (Math.random() * 1000000).toFixed(0) }),
          }
        }

        loading={false}
        actionRef={props.actionRef}
        columns={props.columns}
        request={(params) => {
          return props.getMethod({ ...params })
        }}
        dataSource={props.dataSource}
        editable={{
          type: 'multiple',
          editableKeys,
          onSave: async (_, data) => {
            // 需要删除 id，由后端自动生成
            delete data['id'];
            delete data['index'];
            props.newMethod(data)
          },
          onChange: setEditableRowKeys,
        }}
      />
    </>
  );
};
export default EditPage;
