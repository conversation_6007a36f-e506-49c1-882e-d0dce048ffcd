import { systemConfigColumns } from '@/columns/SystemConfig';
import { deleteSystemconfigId, getSystemconfig, postSystemconfig, postSystemconfigBulkOpenApiDelete, putSystemconfigId } from '@/services/meta/systemConfig';
import { ActionType, EditableProTable, ProColumns, ProForm, ProFormCheckbox, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useRef, useState } from 'react';
import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import { transformTime } from '@/utils/transform';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import InfoLayout from '@/layouts/extend/InfoLayout';
import { notNullPlaceholder } from '@/utils/util';
import EditPage from './EditPage';


const IndexPage: React.FC = () => {
  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.SystemConfig[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //编辑页面
  const editPageDetail = (
    <>
      <ProForm.Group>
        <ProFormText name="wofang_test_ip" label="沃防牵引接口测试IP" />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormCheckbox.Group
          name="notify_scenes"
          label="通过电话和POPO通知的场景"
          options={[
            '沃防牵引接口测试失败',
            'NDS告警开始&被攻击IP不在NDS防护群组',
            '机房出口告警:开始',
            '机房出口告警开始&联通静态出口超过阈值&NDS告警IP是联通静态IP',
            '沃防提交牵引失败'
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea
          name="notify_phones"
          label={<InfoLayout prefixText="电话列表" />}
          width="md"
          placeholder={notNullPlaceholder}
        />

        <ProFormTextArea
          name="notify_emails"
          label={<InfoLayout prefixText="POPO通知" />}
          width="md"
          placeholder={notNullPlaceholder}
        />
        <ProFormTextArea
          name="ip_whitelists"
          label={<InfoLayout prefixText="通知白名单IP列表" content="一行一条数据，不能包含空行；仅对电话场景:【NDS告警开始&被攻击IP不在NDS防护群组】【沃防提交牵引失败】有效" />}
          width="md"
          placeholder="一行一条数据，不能包含空行；仅对电话场景:【NDS告警开始&被攻击IP不在NDS防护群组】【沃防提交牵引失败】有效"
        />
      </ProForm.Group>
      <RemarkProFormTextArea />
    </>
  );

  //新建页面的字段
  //如果编辑的字段也和新建页面一样（可操作的字段相同），可以共用
  const newPageDetail = (
    <ProForm.Group>
      {editPageDetail}
    </ProForm.Group>
  )


  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.SystemConfig>[] = [
    ...systemConfigColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}

          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={
            {//id必须设置
              id: record.id,
              wofang_test_ip: record.wofang_test_ip,
              //notify_scenes是个多选框，本来就是数组
              notify_scenes: record.notify_scenes,
              //将数组转换成单行数据，如果有
              notify_phones: record.notify_phones?.join('\n'),
              notify_emails: record.notify_emails?.join('\n'),
              ip_whitelists: record.ip_whitelists?.join('\n'),
              //如果没有备注，可以去掉
              remark: record.remark,


              //其他需要更新的字段可以在这里添加
            }
          }
          //更新页面表单
          updateDetailPage={<>
            {editPageDetail}
          </>}
        />,
        //详情（跳转页面）
        // TODO：根据需求修改路径
        // <LinkLayout key="link" to={`/setting/config/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={<>
            <ProDescriptionsLayout data={record} columns={columns} column={2} />
          </>}
        />,
        //删除
        // <PopconfirmDeleteLayout
        //   key="delete"
        //   actionRef={actionRef}
        //   api={deleteSystemconfigId}
        //   id={record.id}
        // />,
      ],
    }
  ];


  //提交时，将单行数据转换成数组
  const transformPostData = (data: any) => {
    return {
      notify_phones: data['notify_phones']?.split(/[(\r\n)\r\n]+/),
      notify_emails: data['notify_emails']?.split(/[(\r\n)\r\n]+/),
      ip_whitelists: data['ip_whitelists']?.split(/[(\r\n)\r\n]+/),
    };
  };


  return <>
    <ProTableLayout
      actionRef={actionRef}
      columns={columns}

      //Service 方法
      getMethod={getSystemconfig}
      editMethod={putSystemconfigId}
      newMethod={postSystemconfig}
      deleteBulkMethod={postSystemconfigBulkOpenApiDelete}


      //默认false，如果true，需要同时传入转换函数
      transform={true}
      transformTime={transformTime}
      //提交时（新建/编辑），数据转换：将单行数据转换成数组
      transformPostData={transformPostData}
      // 查询时，数据转换
      // transformGetData={transformGetData}


      //多选操作
      //显示多选操作；默认显示
      showRowSelect={false}
      selectedRowsState={selectedRowsState}
      setSelectedRows={setSelectedRows}

      //单击行数据，增加背景色，双击取消；默认不生效
      //每次都会遍历所有行，有点消耗资源？
      // rowSelectBackground={true}

      //显示分页栏；默认显示
      pagination={true}
      //显示搜索框；默认显示
      showSearch={false}
      //显示工具栏；默认显示
      showToolbar={true}

      //新建
      //显示新建按钮；默认显示
      showNew={false}
      addModalDetail={addModalDetail}
      setAddModalDetail={setAddModalDetail}
      addDetailPage={addDetailPage}
      setAddDetailPage={setAddDetailPage}
      newPageDetail={newPageDetail}

      // 显示
      showDetail={showDetail}
      setShowDetail={setShowDetail}
      showDetailPage={showDetailPage}
      setShowDetailPage={setShowDetailPage}

      // 更新
      updateDetailPage={updateDetailPage}
      setUpdateDetailPage={setUpdateDetailPage}
      updateValues={updateValues}
      setUpdateValues={setUpdateValues}
      // 编辑提交时，二次确认，默认否
      // secondConfirm={true}
      // 二次确认文本
      // secondConfirmContent={"是否继续"}

      // 使用ModalForm或者使用DrawerForm更新
      updateDrawerDetail={updateDrawerDetail}
      setUpdateDrawerDetail={setUpdateDrawerDetail}
      updateModalDetail={updateModalDetail}
      setUpdateModalDetail={setUpdateModalDetail}
    />
    {/* <EditPage
      getMethod={getSystemconfig}
      newMethod={postSystemconfig}
      actionRef={actionRef} columns={columns}
       /> */}
  </>
};
export default IndexPage;
