import { valueEnum } from "@/utils/util";
import { ProColumns, } from "@ant-design/pro-components";
import { Divider, Drawer } from "antd";
import ProDescriptionsExtend from "../../../layouts/extend/ProDescriptionsLayout";

export type ShowFormProps = {
  onCancel: () => void;
  showDetail: boolean;
  values: Partial<any>;
  columns: ProColumns<any>[];
};


const monitorInfoColumns: ProColumns<API.MonitorInfo>[] = [
  {
    title: 'ackBps',
    dataIndex: 'ackBps',
    valueType: 'text',
  },
  {
    title: '开启机器学习',
    dataIndex: 'autoML',
    valueType: 'select',
    valueEnum,
  },
  {
    title: 'icmpBps',
    dataIndex: 'icmpBps',
    valueType: 'text',
  },
  {
    title: 'icmpPps',
    dataIndex: 'icmpPps',
    valueType: 'text',
  },
  {
    title: 'synBps',
    dataIndex: 'synBps',
    valueType: 'text',
  },
  {
    title: 'synPps',
    dataIndex: 'synPps',
    valueType: 'text',
  },
  {
    title: 'totalBps',
    dataIndex: 'totalBps',
    valueType: 'text',
  },
  {
    title: 'totalPps',
    dataIndex: 'totalPps',
    valueType: 'text',
  },
  {
    title: 'udpBps',
    dataIndex: 'udpBps',
    valueType: 'text',
  },
  {
    title: 'udpPps',
    dataIndex: 'udpPps',
    valueType: 'text',
  },
]
const dragInfoColumns: ProColumns<API.DragInfo>[] = [
  {
    title: '自动牵引',
    dataIndex: 'autoDrag',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '自动回迁',
    dataIndex: 'autoUnDrag',
    valueType: 'select',
    valueEnum,
  },
]
const nds4ConfigColumns: ProColumns<API.Nds4Config>[] = [
  {
    title: 'ACL开关',
    dataIndex: 'sAcl',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '防护除SYN外的TCP flood攻击[0-关 1-开 2-自动]',
    dataIndex: 'sAntiOtherTcp',
    valueType: 'text',
  },
  {
    title: '黑名单开关',
    dataIndex: 'sBlacklist',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '特征匹配开关',
    dataIndex: 'sContentMatch',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '目的ip限速开关',
    dataIndex: 'sDstSpeedLimit',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '过滤特殊报文开关',
    dataIndex: 'sEliminatePkt',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '首包丢弃配置[0-关 1-开 2-自动]',
    dataIndex: 'sFirstPktDrop',
    valueType: 'text',
  },
  {
    title: '畸形包报文检测开关',
    dataIndex: 'sMalformedPkt',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '国外地理位置[0-关 1-开 2-自动]',
    dataIndex: 'sPositionForeign',
    valueType: 'text',
  },
  {
    title: 'idc国家地理位置[0-关 1-开 2-自动]',
    dataIndex: 'sPositionIdc',
    valueType: 'text',
  },
  {
    title: '源认证配置[0-关 1-开 2-自动]',
    dataIndex: 'sSourceCheck',
    valueType: 'text',
  },
  {
    title: '源ip限速开关',
    dataIndex: 'sSrcSpeedLimit',
    valueType: 'select',
    valueEnum,
  },
  {
    title: 'TCP反射配置[0-关 1-开 2-自动]',
    dataIndex: 'sTcpReflection',
    valueType: 'text',
  },
  {
    title: 'UDP反射配置[0-关 1-开 2-自动]',
    dataIndex: 'sUdpReflection',
    valueType: 'text',
  },
  {
    title: '白名单开关',
    dataIndex: 'sWhitelist',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '不清洗直接转发阈值',
    dataIndex: 'tNoCleanLimit',
    valueType: 'text',
  },
]
const nds6ConfigColumns: ProColumns<API.Nds6Config>[] = [
  {
    title: '目的ip限速开关',
    dataIndex: 'sDstSpeedLimit',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '畸形包报文检测开关',
    dataIndex: 'sMalformedPkt',
    valueType: 'select',
    valueEnum,
  },
  {
    title: '源ip限速开关',
    dataIndex: 'sSrcSpeedLimit',
    valueType: 'select',
    valueEnum,
  },
  {
    title: 'UDP反射配置[0-关 1-开 2-自动]',
    dataIndex: 'sUdpReflection',
    valueType: 'text',
  },
  {
    title: '不清洗直接转发阈值',
    dataIndex: 'tNoCleanLimit',
    valueType: 'text',
  },
]

const ProtectGroupShowForm: React.FC<ShowFormProps> = (props) => {
  return (
    <>
      <Drawer
        width={'40%'}
        open={props.showDetail}
        title="查看"
        onClose={() => {
          props.onCancel();
        }}
      >
        {/* 常规值可以直接用values */}
        {/* 其他值需要重新指定columns(新建新的ProDescriptions) */}
        <ProDescriptionsExtend data={props.values} columns={props.columns} />
        {/* 监控阈值 */}
        {props.values.monitorInfo && (
          <>
            <Divider />
            <ProDescriptionsExtend title="监控阈值" data={props.values.monitorInfo} columns={monitorInfoColumns} />
          </>
        )}
        {/* 牵引策略 */}
        {props.values.dragInfo && (
          <>
            <Divider />
            <ProDescriptionsExtend title="牵引策略" data={props.values.dragInfo} columns={dragInfoColumns} />
          </>
        )}

        {/* 清洗参数 */}
        {/* ipv4 */}
        {props.values.nds4Config && (
          <>
            <Divider />
            <ProDescriptionsExtend title="IPv4清洗参数" data={props.values.nds4Config} columns={nds4ConfigColumns} />
          </>
        )}
        {/* ipv6 */}
        {props.values.nds6Config && (
          <>
            <Divider />
            <ProDescriptionsExtend title="IPv6清洗参数" data={props.values.nds6Config} columns={nds6ConfigColumns} />
          </>
        )}

      </Drawer>
    </>
  );
};
export default ProtectGroupShowForm;
