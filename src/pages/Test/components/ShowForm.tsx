import { ProColumns } from "@ant-design/pro-components";
import { Drawer } from "antd";

export type ShowFormProps = {
  onCancel: () => void;
  showDetail: boolean;
  values: Partial<any>;
  columns: ProColumns<any>[];
  detailPage: any;
};



const ShowForm: React.FC<ShowFormProps> = (props) => {
  return (
    <>
      <Drawer
        width={'40%'}
        open={props.showDetail}
        title="查看"
        onClose={() => {
          props.onCancel();
        }}
      >
        {props.detailPage}
      </Drawer>
    </>
  )
};
export default ShowForm;
