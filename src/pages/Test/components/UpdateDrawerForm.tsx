import { PlusOutlined } from "@ant-design/icons/lib/icons";
import { DrawerForm, ProColumns, ProDescriptions, ProDescriptionsItemProps, ProForm, ProFormText } from "@ant-design/pro-components";
import { <PERSON><PERSON>, Divider, Drawer, message } from "antd";

export type UpdateFormProps = {
  onSubmit: (values: any) => Promise<void>;
  updateDrawerDetail: boolean;
  values: Partial<any>;
  columns: ProColumns<any>[];
  setUdateDrawerDetail: any;
  // btn: any;
};

const UpdateDrawerForm: React.FC<UpdateFormProps> = (props) => {
  return (
    <>
      <DrawerForm
        width={'40%'}
        title="编辑"
        onFinish={async (values) => {
          props.onSubmit(values);
        }}
        open={props.updateDrawerDetail}
        onOpenChange={props.setUdateDrawerDetail}
        autoFocusFirstInput
        drawerProps={{ destroyOnClose: true }}
      >
        <ProForm.Group>
          <ProFormText
            width="md"
            name="name"
            label="签约客户名称"
            tooltip="最长为 24 位"
            placeholder="请输入名称"
          />

          <ProFormText width="md" name="company" label="我方公司名称" placeholder="请输入名称" />
        </ProForm.Group>
      </DrawerForm>
    </>
  );
};
export default UpdateDrawerForm;
