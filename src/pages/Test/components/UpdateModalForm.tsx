import { ModalForm, ProColumns, ProDescriptions, ProForm, ProFormText } from "@ant-design/pro-components";

export type UpdateFormProps = {
  onSubmit: (values: any) => Promise<void>;
  updateModalDetail: boolean;
  values: Partial<any>;
  columns: ProColumns<any>[];
  setUpdateModalDetail: any;
};


const UpdateModalForm: React.FC<UpdateFormProps> = (props) => {
  return (
    <>
      <ModalForm
        title="编辑"
        onFinish={async (values) => {
          props.onSubmit(values);
        }}
        open={props.updateModalDetail}
        onOpenChange={props.setUpdateModalDetail}
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => console.log('run'),
        }}
        // 初始化需要更新的值
        initialValues={{
          //id必须
          id: props.values.id,
          groupName: props.values.groupName,
        }}
      >
        <ProDescriptions title={props.values.groupName}>
          <ProDescriptions.Item label="群组名"  >
            {props.values.groupName}
          </ProDescriptions.Item>
        </ProDescriptions>
        {/* TODO:根据字段新建对应的 ProForm.Group 表单项*/}

        {/* map:  title key value */}

        <ProForm.Group>
          <ProFormText name="id" label="编号" disabled />
          <ProFormText name="groupName" label="群组名" />
        </ProForm.Group>
      </ModalForm>
    </>
  );
};
export default UpdateModalForm;
