import { ModalForm, ProForm, ProFormInstance, ProFormText } from "@ant-design/pro-components";
import { useRef } from "react";

export type UpdateFormProps = {
  onSubmit: (values: any) => Promise<void>;
  addModalDetail: boolean;
  setAddModalDetail: any;
};


const AddModalForm: React.FC<UpdateFormProps> = (props) => {
  const restFormRef = useRef<ProFormInstance>();

  return (
    <>
      <ModalForm
        title="编辑"
        formRef={restFormRef}
        onFinish={async (values) => {
          props.onSubmit(values);
        }}
        open={props.addModalDetail}
        onOpenChange={props.setAddModalDetail}
        autoFocusFirstInput
        modalProps={{ destroyOnClose: true }}
        submitter={{
          searchConfig: {
            resetText: '重置',
          },
          resetButtonProps: {
            onClick: () => {
              restFormRef.current?.resetFields();
            },
          },
        }}
      >

        {/* TODO:根据字段新建对应的 ProForm.Group 表单项*/}
        <ProForm.Group>
          <ProFormText name="groupName" label="群组名" required />
        </ProForm.Group>
      </ModalForm>
    </>
  );
};
export default AddModalForm;
