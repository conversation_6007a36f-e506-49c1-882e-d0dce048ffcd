import { dragInfoColumns, monitorInfoColumns, nds4ConfigColumns, nds6ConfigColumns } from '@/columns/ProtectGroup';
import ShowArrayProCardLayout from '@/layouts/show/ArrayProCardLayout';
import { handleRemoveRow } from '@/services/common';
import { deleteProtectgroupId, getProtectgroup, postProtectgroup, postProtectgroupBulkOpenApiDelete, putProtectgroupId } from '@/services/meta/protectGroup';
import { ActionType, ProColumns, ProDescriptions, ProForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Divider, Popconfirm } from 'antd';
import { useRef, useState } from 'react';
import { Link } from 'umi';

import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ProTableLayout from '@/layouts/extend/ProTableLayout';
import { transformTime } from '@/utils/transform';


const IndexForm: React.FC<any> = () => {

  const actionRef = useRef<ActionType>();

  //多行选择
  const [selectedRowsState, setSelectedRows] = useState<API.ProtectGroup[]>([]);

  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);
  const [addDetailPage, setAddDetailPage] = useState<any>();

  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showDetailPage, setShowDetailPage] = useState<any>();

  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 更新的值
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();

  //pro colums：行编辑、查看、删除
  const columns: ProColumns<API.ProtectGroup>[] = [
    {
      title: '搜索',
      dataIndex: 'search',
      valueType: 'text',
      hideInDescriptions: true,
      hideInTable: true,
      tip: "需要搜索的值，多个值英文逗号,分隔；搜索可与时间范围查询同时生效",
    },
    {
      title: '群组名',
      dataIndex: 'groupName',
      valueType: 'text',
    },
    {
      title: '群组编号',
      dataIndex: 'groupId',
      valueType: 'text',
      sorter: true,
    },
    {
      title: 'IP列表',
      dataIndex: 'ipList',
      valueType: 'textarea',
      hideInTable: true,
      render: (_, row) => <ShowArrayProCardLayout data={row.ipList} title='可折叠' />
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTimeRange',
      // table中不显示
      hideInTable: true,
      hideInDescriptions: true,
      search: {
        transform: (value) => ({
          //dateTimeRange 转换 成gte和lte字段
          createdAtGte: value[0],
          createdAtLte: value[1],
        }),
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTimeRange',
      // table中不显示
      hideInTable: true,
      hideInDescriptions: true,
      search: {
        transform: (value) => ({
          //dateTimeRange 转换 成gte和lte字段
          updatedAtGte: value[0],
          updatedAtLte: value[1],
        }),
      },
    },
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        <a
          key="editable"
          onClick={() => {
            //可以在这里转值转换
            setUpdateValues({
              //id必须
              id: record.id,
              groupName: record.groupName,
              ipList: record.ipList,
            })
            setUpdateDetailPage(
              <>
                <ProDescriptions title={record.groupName}>
                  <ProDescriptions.Item label="群组名"  >
                    {record.groupName}
                  </ProDescriptions.Item>
                </ProDescriptions>
                {/* TODO:根据字段新建对应的 ProForm.Group 表单项*/}
                <ProForm.Group>
                  <ProFormText name="id" label="编号" disabled />
                  <ProFormText name="groupName" label="群组名" rules={[{ required: true }]} />
                </ProForm.Group>
                <ProForm.Group>
                  <ProFormTextArea name="ipList" label="IP列表" width="md" rules={[{ required: true }]} />
                </ProForm.Group>
              </>
            )
            //在这里控制使用drawer还是modal
            // setUpdateDrawerDetail(true);
            setUpdateModalDetail(true);
          }}
        >
          编辑
        </a>,
        <Link key="link" to={`/detail/${record.id}`} >
          嘻嘻
        </Link >,
        <a
          key="view"
          onClick={() => {
            setShowDetailPage(
              <>
                <ProDescriptionsLayout data={record} columns={columns} column={2} />
                {record.dragInfo && (
                  <>
                    <Divider />
                    <ProDescriptionsLayout title="牵引策略" data={record.dragInfo} columns={dragInfoColumns} column={2} />
                  </>
                )}
                {record.monitorInfo && (
                  <>
                    <Divider />
                    <ProDescriptionsLayout title="监控阈值" data={record.monitorInfo} columns={monitorInfoColumns} column={2} />
                  </>
                )}
                {record.nds4Config && (
                  <>
                    <Divider />
                    <ProDescriptionsLayout title="IPv4清洗参数" data={record.nds4Config} columns={nds4ConfigColumns} column={2} />
                  </>
                )}
                {record.nds6Config && (
                  <>
                    <Divider />
                    <ProDescriptionsLayout title="IPv6清洗参数" data={record.nds6Config} columns={nds6ConfigColumns} column={2} />
                  </>
                )}
              </>
            )
            setShowDetail(true);

          }}
        >
          查看
        </a>,
        <Popconfirm
          key="delete"
          title="Confirm?"
          onConfirm={async () => {
            const success = await handleRemoveRow(deleteProtectgroupId, record.id);
            if (success) {
              actionRef.current?.reloadAndRest?.();
            }
          }}
        >
          <a key="delete">删除</a>
        </Popconfirm>
      ],
    },
  ];

  //新建页面的字段
  const newPageDetail = (
    <ProForm.Group>
      <ProFormText name="groupName" label="群组名" rules={[{ required: true }]} />
      <ProFormText name="groupName2" label="群组名2" />
      <ProFormText
        rules={[
          {
            required: true,
          },
          {
            min: 8,
            message: '密码不能少于8个字符',
          },
        ]}
        name="password"
        label="密码"
        tooltip="密码不能少于8个字符"
      />
    </ProForm.Group>
  )

  return <ProTableLayout
    actionRef={actionRef}
    columns={columns}

    //Service 方法
    getMethod={getProtectgroup}
    editMethod={putProtectgroupId}
    newMethod={postProtectgroup}
    deleteBulkMethod={postProtectgroupBulkOpenApiDelete}

    //多选操作
    //显示多选操作；默认显示
    showRowSelect={true}
    selectedRowsState={selectedRowsState}
    setSelectedRows={setSelectedRows}
    transformTime={transformTime}

    //单击行数据，增加背景色，双击取消；默认不生效
    rowSelectBackground={true}

    //显示分页栏；默认显示
    pagination={true}

    //新建
    //显示新建按钮；默认显示
    showNew={true}
    addModalDetail={addModalDetail}
    setAddModalDetail={setAddModalDetail}
    addDetailPage={addDetailPage}
    setAddDetailPage={setAddDetailPage}
    newPageDetail={newPageDetail}

    // 显示
    showDetail={showDetail}
    setShowDetail={setShowDetail}
    showDetailPage={showDetailPage}
    setShowDetailPage={setShowDetailPage}

    // 更新
    updateDetailPage={updateDetailPage}
    setUpdateDetailPage={setUpdateDetailPage}
    updateValues={updateValues}
    setUpdateValues={setUpdateValues}

    // 使用ModalForm或者使用DrawerForm更新
    updateDrawerDetail={updateDrawerDetail}
    setUpdateDrawerDetail={setUpdateDrawerDetail}
    updateModalDetail={updateModalDetail}
    setUpdateModalDetail={setUpdateModalDetail}
  />

};
export default IndexForm;
