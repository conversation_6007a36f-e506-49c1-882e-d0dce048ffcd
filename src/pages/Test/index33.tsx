
import { protectGroupColumns } from '@/columns/ProtectGroup';
import AddModalForm from '@/layouts/add/ModalLayout';
import RemarkProFormTextArea from '@/layouts/form/RemarkProFormTextArea';
import EditRowLayout from '@/layouts/options/EditRowLayout';
import LinkLayout from '@/layouts/options/LinkLayout';
import PopconfirmDeleteLayout from '@/layouts/options/PopconfirmDeleteLayout';
import ShowRowDetailLayout from '@/layouts/options/ShowRowDetailLayout';
import ShowDrawer from '@/layouts/show/DrawerLayout';
import UpdateDrawerForm from '@/layouts/update/DrawerLayout';
import UpdateModalForm from '@/layouts/update/ModalLayout';
import { handleAdd, handleUpdate } from '@/services/common';
import { deleteProtectgroupId, getProtectgroup, postProtectgroup, postProtectgroupBulkOpenApiDelete, putProtectgroupId } from '@/services/meta/protectGroup';
import { transformOder, transformTime } from '@/utils/transform';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProForm, ProFormSwitch, ProFormText, ProFormTextArea, ProTable } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Table } from 'antd';
import { useRef, useState } from 'react';
import FooterToolbarLayout from '../../layouts/extend/FooterToolbarLayout';


import ProDescriptionsLayout from '../../layouts/extend/ProDescriptionsLayout';


// @ts-ignore
import styles from './split.less';

export type IndexFormProps = {
  onCancel: () => void;
  showDetail: boolean;
  values: Partial<any>;
  columns: ProColumns<any>[];
  // 显示选择行及页脚操作栏
  showRowSelect?: boolean;
  //表格行中显示编辑按钮
  showEdit?: boolean;
  //表格行中显示显示按钮
  showShow?: boolean;
  //表格行中显示删除按钮
  showDelete?: boolean;
  //表格行中编辑的样式
  editFormStyle?: "modal" | "drawer";
};



const IndexForm: React.FC<IndexFormProps> = (props) => {

  const actionRef = useRef<ActionType>();

  const [selectedRowsState, setSelectedRows] = useState<API.ProtectGroup[]>([]);
  // 查看 使用Drawer
  const [showDetail, setShowDetail] = useState<boolean>(false);
  // 更新 使用ModalForm
  const [updateModalDetail, setUpdateModalDetail] = useState<boolean>(false);
  // 更新 使用DrawerForm
  const [updateDrawerDetail, setUpdateDrawerDetail] = useState<boolean>(false);
  // 新建 使用ModalForm
  const [addModalDetail, setAddModalDetail] = useState<boolean>(false);

  const [showDetailPage, setShowDetailPage] = useState<any>();
  const [addDetailPage, setAddDetailPage] = useState<any>();
  const [updateDetailPage, setUpdateDetailPage] = useState<any>();
  const [updateValues, setUpdateValues] = useState<any>();


  const [, setRowClassName] = useState<string>('');
  const [rowId, setRowId] = useState<string>();

  //ProTable自定义，其他操作使用通用的页面

  //增加行操作：编辑、查看、删除
  const columns: ProColumns<API.ProtectGroup>[] = [
    ...protectGroupColumns,
    {
      title: '操作',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_text, record) => [
        //编辑
        <EditRowLayout
          key="edit"
          setUpdateValues={setUpdateValues}
          setUpdateDetailPage={setUpdateDetailPage}
          //在这里控制使用drawer还是modal，二选一
          // setUpdateDrawerDetail={setUpdateDrawerDetail}
          setUpdateModalDetail={setUpdateModalDetail}
          //需要更新的值，字段名与columns中相同
          //updateValues中id必须设置
          updateValues={{
            //id必须设置
            id: record.id,
            //将数组转换成单行数据
            ipList: record.ipList?.join('\n'),
            remark: record.remark,
          }}
          //更新页面表单
          updateDetailPage={
            <>
              <ProForm.Group>
                <ProFormTextArea
                  name="ipList"
                  label="IP列表"
                  width="md"
                  rules={[
                    { required: true },
                    { pattern: new RegExp(/^[0-9-./\n]*$/, 'g'), message: '格式错误' },
                  ]}
                />
              </ProForm.Group>
              <RemarkProFormTextArea />
            </>
          }
        />,
        //详情（跳转页面）
        <LinkLayout key="link" to={`/protect/group/detail/${record.id}`} />,
        //查看
        <ShowRowDetailLayout
          key="view"
          setShowDetailPage={setShowDetailPage}
          setShowDetail={setShowDetail}
          detailPage={
            <>
              <ProDescriptionsLayout data={record} columns={columns} column={2} />
            </>
          }
        />,
        //删除
        <PopconfirmDeleteLayout
          key="delete"
          actionRef={actionRef}
          api={deleteProtectgroupId}
          id={record.id}
        />,
      ],
    },
  ];

  return <PageContainer>
    < ProTable<API.ProtectGroup, API.Result>
      request={(params, sorter) => {
        // json 中 sorter 转换成 url order
        // {createdAt: 'ascend'} => order = created_at
        // {createdAt: 'descend'} => order = -created_at
        let order = transformOder(sorter)
        return getProtectgroup({ ...params, order })
      }}
      columns={columns}
      actionRef={actionRef}
      rowKey="id"
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        position: ['bottomCenter'],
        size: 'small',
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
      search={{
        labelWidth: 120,
      }}
      toolBarRender={() => [
        <Button
          type="primary"
          key="add"
          onClick={() => {
            setAddModalDetail(true);
            // setAddDetailPage(
            //   <ProForm.Group>
            //     <ProFormText name="groupName" label="群组名" rules={[{ required: true }]} />
            //     <ProFormText name="groupName2" label="群组名2" />
            //     {/* <ProFormText
            //       rules={[
            //         {
            //           required: true,
            //         },
            //         {
            //           min: 8,
            //           message: '密码不能少于8个字符',
            //         },
            //       ]}
            //       name="password"
            //       label="密码"
            //       tooltip="密码不能少于8个字符"
            //     /> */}
            //     <ProFormSwitch name="system" label="系统策略" />

            //     {showProject && (
            //       <ProFormText
            //         rules={[
            //           {
            //             required: true,
            //           },
            //           {
            //             min: 8,
            //             message: '密码不能少于8个字符',
            //           },
            //         ]}
            //         name="password"
            //         label="密码"
            //         tooltip="密码不能少于8个字符"
            //       />
            //     )}
            //   </ProForm.Group>
            // )
          }}
        >
          <PlusOutlined /> <FormattedMessage id="pages.searchTable.new" defaultMessage="New" />
        </Button>
      ]}
      //多选操行
      rowSelection={{
        onChange: (_: any, selectedRows: React.SetStateAction<API.ProtectGroup[]>) => {
          setSelectedRows(selectedRows);
        },
        selections: [Table.SELECTION_INVERT, Table.SELECTION_NONE],
      }}
      //时间转换成 RFC3339 样式
      dateFormatter={(value) => {
        return value.format('YYYY-MM-DDTHH:mm:ssZ');
      }}
      //reset时可以同时清除自定义提交的transform参数
      form={{
        // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
        // syncToUrl 分页，搜索、查询时，将get请求的参数同步到浏览器地址栏
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              ...transformTime(values),
            };
          }
          return values;
        },
      }}
      //浏览器中保存列状态
      columnsState={{
        persistenceKey: 'singe-page',
        persistenceType: 'localStorage',
      }}

      //单击行数据，增加背景色，双击取消
      onRow={
        (record) => {
          return {
            onClick: () => {
              setRowId(String(record.id))
              setRowClassName(styles.clickRowStyl)
            },
            onDoubleClick: () => {
              setRowId('')
              setRowClassName('')
            },
          };
        }
      }
      rowClassName={
        (record) => {
          return record.id === rowId ? styles['split-row-select-active'] : '';
        }
      }
    />


    {/* 页脚选择操作 */}
    {selectedRowsState?.length > 0 && (
      <FooterToolbarLayout
        api={postProtectgroupBulkOpenApiDelete}
        selectedRowsState={selectedRowsState}
        setSelectedRows={setSelectedRows}
        actionRef={actionRef}
      />
    )}

    {/* 新建 */}
    {addModalDetail && (
      <AddModalForm
        onSubmit={async (value) => {
          const success = await handleAdd(postProtectgroup, value);
          if (success) {
            setAddModalDetail(false);
            setAddDetailPage(undefined);
            actionRef.current?.reload();
          }
        }}
        addModalDetail={addModalDetail}
        setAddModalDetail={setAddModalDetail}
        detailPage={addDetailPage}

        onValuesChange={(values) => {
          console.log('vvv ', values)
          if (values.system) {
            setShowProject(true)
            setAddDetailPage(addDetailPage)
          } else {
            setShowProject(false)
            setAddDetailPage(addDetailPage)
          }
        }}
      />
    )
    }
    {/* 编辑 */}
    {updateDrawerDetail && (
      <UpdateDrawerForm
        onSubmit={async (value) => {
          const success = await handleUpdate(putProtectgroupId, value);
          if (success) {
            setUpdateDrawerDetail(false);
            setUpdateDetailPage(undefined);
            setUpdateValues(undefined);
            actionRef.current?.reload();
          }
        }}
        updateDrawerDetail={updateDrawerDetail}
        setUdateDrawerDetail={setUpdateDrawerDetail}
        initValues={updateValues}
        detailPage={updateDetailPage}
      />
    )
    }

    {updateModalDetail && (
      <UpdateModalForm
        onSubmit={async (value) => {
          const success = await handleUpdate(putProtectgroupId, value);
          if (success) {
            setUpdateModalDetail(false);
            setUpdateDetailPage(undefined);
            setUpdateValues(undefined);
            actionRef.current?.reload();
          }
        }}
        updateModalDetail={updateModalDetail}
        setUpdateModalDetail={setUpdateModalDetail}
        initValues={updateValues}
        detailPage={updateDetailPage}
      />
    )
    }

    {/* 查看详情 */}
    {showDetail && (
      <ShowDrawer
        onCancel={() => {
          setShowDetail(false);
          setShowDetailPage(undefined)
        }}
        showDetail={showDetail}
        detailPage={showDetailPage}
      />
    )
    }
  </PageContainer>;
};
export default IndexForm;
