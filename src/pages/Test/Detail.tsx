import { dragInfoColumns, monitorInfoColumns, nds4ConfigColumns, nds6ConfigColumns } from "@/columns/ProtectGroup";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import ShowDetailLayout from "@/layouts/show/DetailLayout";
import { getProtectgroupId } from "@/services/meta/ProtectGroup";
import { ProColumns } from "@ant-design/pro-components";
import ProDescriptionsLayout from "@/layouts/extend/ProDescriptionsLayout";


const DetailPage: React.FC<any> = () => {
    //复合字段使用 ProDescriptionsLayout解析
    const columns: ProColumns<API.ProtectGroup>[] = [
        {
            title: '群组名',
            dataIndex: 'groupName',
            valueType: 'text',
        },
        {
            title: '群组编号',
            dataIndex: 'groupId',
            valueType: 'text',
        },
        {
            title: 'IP列表',
            dataIndex: 'ipList',
            valueType: 'textarea',
            hideInTable: true,
            // ellipsis: true,
            render: (_, row) => <ShowArrayProCardLayout data={row.ipList} title='可折叠' />
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            valueType: 'dateTime',
            hideInSearch: true,
            // sorter: true,
        },
        {
            title: '更新时间',
            dataIndex: 'updatedAt',
            valueType: 'dateTime',
            hideInSearch: true,
            // sorter: true,
        },
        {
            title: '牵引策略',
            dataIndex: 'dragInfo',
            valueType: 'text',
            hideInSearch: true,
            render: (_, record) => (<ProDescriptionsLayout data={record.dragInfo} columns={dragInfoColumns} column={1} />)
        },

        {
            title: '监控阈值',
            dataIndex: 'monitorInfo',
            valueType: 'text',
            hideInSearch: true,
            render: (_, record) => (<ProDescriptionsLayout data={record.monitorInfo} columns={monitorInfoColumns} column={1} />),
        },
        {
            title: 'IPv4清洗参数',
            dataIndex: 'nds4Config',
            valueType: 'text',
            hideInSearch: true,
            render: (_, record) => (<ProDescriptionsLayout data={record.nds4Config} columns={nds4ConfigColumns} column={1} />)
        },
        {
            title: 'IPv6清洗参数',
            dataIndex: 'nds6Config',
            valueType: 'text',
            hideInSearch: true,
            render: (_, record) => (<ProDescriptionsLayout data={record.nds6Config} columns={nds6ConfigColumns} column={1} />)
        },
    ]

    return (
        <ShowDetailLayout
            title="防护群组详情"
            func={getProtectgroupId}
            columns={columns}
        />

    )
};
export default DetailPage;