import ProCard from "@ant-design/pro-card";
import { DatePicker } from "antd";
import React, { useState } from 'react';
import moment from 'moment';
import BaseLine from "@/layouts/chart/BaseLine";
import { getChartSpectrum } from "@/services/meta/chart";
import { PageContainer, ProForm, ProFormDateTimeRangePicker, ProFormGroup, ProFormText } from "@ant-design/pro-components";
import { disabledDate } from "@/columns/Comon";
import { str2Time } from "@/utils/transform";
import { gBase, spectrumLineCommnet } from "@/utils/util";

const SectrumLine: React.FC = () => {
  const [dataI, setDataI] = useState([]);
  const { RangePicker } = DatePicker;
  const today = moment()
  const lastDay = today.subtract(1, 'days')
  const lastDayEnd = moment(lastDay).endOf("day")
  const rangePresets: {
    label: string;
    value: [moment.Moment, moment.Moment];
  }[] = [
      { label: '近五分钟', value: [moment(today.subtract(5, 'minute')), today] },
      { label: '近十分钟', value: [moment(today.subtract(10, 'minute')), today] },
      { label: '近半小时', value: [moment(today.subtract(30, 'minute')), today] },
      { label: '近一小时', value: [moment(today.subtract(1, 'hour')), today] },
      { label: '近六小时', value: [moment(today.subtract(6, 'hour')), today] },
      { label: '近十二小时', value: [moment(today.subtract(12, 'hour')), today] },
      { label: '今天', value: [today.startOf("day"), today.endOf("day")] },
      { label: '昨天', value: [moment(lastDay).startOf("day"), lastDayEnd] },
      { label: '近一周', value: [moment(today.subtract(1, 'weeks')).startOf("day"), lastDayEnd] },
      { label: '近一个月', value: [moment(today.subtract(1, 'months')).startOf("day"), lastDayEnd] },
    ];
  const formatter = (v: number) => {
    return (v / gBase) + "G"
  }
  return (
    <>
      <PageContainer >
        <ProCard
          title="NDS分光数据"
          tooltip={spectrumLineCommnet}
        >
          <ProForm
            submitter={{
              render: (props, doms) => { return [...doms] }
            }}
            onReset={() => { setDataI([]) }}
            layout={"inline"}
            onFinish={async (values) => {
              let data = { "ip": values.ip, "start_time": str2Time(values.time[0]), "end_time": str2Time(values.time[1]) }
              const res = await getChartSpectrum(data);
              if (res.success && res.data !== null) {
                setDataI(res.data)
              } else {
                setDataI([])
              }
            }}
          // syncToUrl={(values, type) => {
          //   if (type === 'get') {
          //     console.log("get values", values)
          //     if (values.time !== undefined) {
          //       return { "ip": values.ip, "startTime": str2Time(values.time[0]), "endTime": str2Time(values.time[1]) };
          //     }
          //   }
          //   return values;
          // }}
          >
            <ProForm.Group>
              <ProFormText name="ip" label="IP" rules={[{ required: true }]} />
              <ProFormDateTimeRangePicker name="time" label="时间范围"
                renderFormItem={({ ...rest }) => {
                  return <RangePicker
                    presets={rangePresets}
                    showTime
                    disabledDate={disabledDate}
                    {...rest}
                  />
                }}
                rules={[{ required: true }]}
              />
            </ProForm.Group>
          </ProForm>
          <BaseLine data={dataI} formatMethod={formatter} />
        </ProCard>
      </PageContainer>
    </>
  );
}

export default SectrumLine;