import ContentDiv from "@/layouts/show/ContentDiv";
import ShowBitString from "@/layouts/show/ShowBitString";
import { getMatrixspectrumalertAttacking } from "@/services/meta/matrixSpectrumAlert";
import { regionEnum, netEnum, formatTime, ispMapEnum, timeIsNotNull, timePass } from "@/utils/util";
import { ActionType, ProList } from "@ant-design/pro-components";
import { Button, Space, Tag } from "antd";
import { useRef, useState } from "react";
import TimePass from "./TimePass";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";

const MatrixAttack: React.FC = () => {
    const actionRef = useRef<ActionType>();
    const [attacking, setAttacking] = useState<boolean>(false);
    const [showall, setShowall] = useState<string>('查看所有');
    return (<>
        <ProList<any>
            rowKey="id"
            actionRef={actionRef}
            headerTitle="机房出口告警"
            params={{ attacking }}
            toolBarRender={() => {
                return [
                    <Button key="change" onClick={() => {
                        if (attacking) {
                            setAttacking(false)
                            setShowall('查看所有')
                        } else {
                            setAttacking(true)
                            setShowall('仅攻击中')
                        }
                    }}>
                        {showall}
                    </Button>
                ];
            }}
            request={async (params) => {
                return getMatrixspectrumalertAttacking(params);
            }}
            pagination={{
                hideOnSinglePage: true,
                pageSize: 5,
                showSizeChanger: false,
                align: 'center',
            }}
            metas={{
                title: {
                    dataIndex: 'ip',
                    title: 'ip',
                    render: (_, row) => {
                        return (<>
                            <a href={'https://ddos.nie.netease.com/attack/matrix/detail/' + row.id} target="_blank" rel="noreferrer" >
                                {regionEnum[row.region]}
                                {ispMapEnum[row.isp]}
                                {netEnum[row.net_type]}
                            </a>
                        </>)
                    }
                },
                description: {
                    dataIndex: 'ip_list',
                    render: (_, row) => <ShowArrayProCardLayout data={row.ip_list} noCollapsible />
                },
                extra: {
                    render: (_: any, row) => (
                        <>
                            <Space align="start">
                                {/* //如果 防护状态 10，11，12
                                // 策略id不为空，则为超出阈值进行的牵引，
                                // 否则就是机房防护模块牵引

                                // 或者直接显示防护策略的名称 */}
                                {row.matrix_strategy_id && (<>
                                    <ContentDiv title="防护阈值" content={
                                        function () {
                                            return (<>
                                                策略: <a href={'https://ddos.nie.netease.com/protect/matrix/detail/' + row.matrix_strategy_id} target="_blank" rel="noreferrer" >{row.edges.matrix_strategy.name}</a><br />
                                                监控bps: <ShowBitString data={row.edges?.matrix_strategy?.monitor_bps} /> <br />
                                                牵引bps: <ShowBitString data={row.edges?.matrix_strategy?.drag_bps} /> <br />
                                            </>)
                                        }()
                                    } />
                                </>
                                )}
                                <ContentDiv title="告警值" content={<>
                                    bps: <ShowBitString data={row.bps} /> <br />
                                    占比: {(Math.round(row.bps / row.edges?.matrix_strategy?.monitor_bps * 10000) / 100.00 + "%")}<br />
                                </>
                                } />
                                {row.attack_info.maxBps !== 0 && (
                                    <ContentDiv title="峰值" content={<>
                                        bps: <ShowBitString data={row.attack_info.maxBps} /> <br />
                                    </>
                                    } />
                                )}
                                <ContentDiv title="时间" content={
                                    <TimePass startTime={row.start_time} receivedTime={row.created_at} endTime={row.end_time} />
                                } />
                            </Space>
                        </>
                    ),
                },
            }}
        />
    </>)
}
export default MatrixAttack;