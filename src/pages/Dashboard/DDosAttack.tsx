import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import ContentDiv from "@/layouts/show/ContentDiv";
import ShowBitString from "@/layouts/show/ShowBitString";
import { getSpectrumalertAttacking } from "@/services/meta/spectrumAlert";
import { ISPTypes, alertStatusEnumMap, alertColorMap, formatTime, timeIsNotNull, timePass } from "@/utils/util";
import { ActionType, ProList } from "@ant-design/pro-components";
import { Button, Tag, Space } from "antd";
import { useRef, useState } from "react";
import TimePass from "./TimePass";

const DDosAttack: React.FC = () => {
    const actionRef = useRef<ActionType>();
    const [attacking, setAttacking] = useState<boolean>(false);
    const [showall, setShowall] = useState<string>('查看所有');
    return (<>
        <ProList<any>
            rowKey="id"
            actionRef={actionRef}
            params={{ attacking }}
            headerTitle="DDoS攻击"
            toolBarRender={() => {
                return [
                    <Button key="change" onClick={() => {
                        if (attacking) {
                            setAttacking(false)
                            setShowall('查看所有')
                        } else {
                            setAttacking(true)
                            setShowall('仅攻击中')
                        }
                    }}>
                        {showall}
                    </Button>
                ];
            }}
            request={async (params) => {
                return getSpectrumalertAttacking(params);
            }}
            pagination={{
                hideOnSinglePage: true,
                pageSize: 5,
                showSizeChanger: false,
                align: 'center',
            }}
            metas={{
                title: {
                    dataIndex: 'ip',
                    title: 'ip',
                    render: (_, row) => {
                        return (<>
                            <a href={'https://ddos.nie.netease.com/attack/alert/detail/' + row.id} target="_blank" rel="noreferrer" >{row.ip}</a>
                        </>)
                    }
                },
                description: {
                    dataIndex: 'isp_code',
                    render: (_, row) => {
                        let isp, group_protect
                        if (row.isp_code) {
                            isp = <ShowArrayProCardLayout data={[row.isp_code]} dataMap={ISPTypes} noCollapsible />
                        }
                        const groupName = row.edges?.protect_group?.group_name
                        group_protect = <>
                            < ShowArrayProCardLayout data={row.protect_status} dataMap={alertStatusEnumMap} colorMap={alertColorMap} noCollapsible />
                        </>
                        if (groupName !== undefined) {
                            group_protect = <>
                                <Tag color="green">{groupName}</Tag>
                                {group_protect}
                            </>
                        }
                        return (<>
                            <Space size={0}>
                                {isp}
                                {group_protect}
                            </Space>
                            <br />
                            <>攻击类型: {row?.attack_type?.split(',').map((item) => <Tag key={item}>{item}</Tag>)}</>
                        </>
                        )
                    },
                },
                subTitle: {
                    dataIndex: 'tenant_code',
                    render: (_, row) => {
                        if (row.edges?.tenant?.name) {
                            return (<> {row.edges?.tenant?.name}-{row.edges?.tenant?.code}</>);
                        }
                    },
                },
                extra: {
                    render: (_: any, row) => (<>
                        <Space align="start">
                            {row.wofang_id && (<>
                                <ContentDiv title="沃防" content={
                                    function () {
                                        let wofangStatus
                                        let reson = "出口超阈值"
                                        if (row.strategy_id && row.max_bps >= row.edges?.strategy?.bps) {
                                            reson = "单IP超阈值"
                                        }
                                        if (row.edges?.wofang_ticket.status === "success") {
                                            wofangStatus = <Tag color="green">成功</Tag>
                                        } else {
                                            wofangStatus = <Tag color="red">失败</Tag>
                                        }
                                        return (<>
                                            <a href={'https://ddos.nie.netease.com/process/wofang/detail/' + row.wofang_id} target="_blank" rel="noreferrer" >{row.edges?.wofang_ticket?.name}</a><br />
                                            <Tag>{reson}</Tag><br />
                                            {wofangStatus}
                                        </>)
                                    }()
                                } />
                            </>
                            )}
                            {row.strategy_id && (<>
                                <ContentDiv title="NDS告警防护" content={
                                    function () {
                                        return (<>
                                            <a href={'https://ddos.nie.netease.com/protect/strategy/detail/' + row.strategy_id} target="_blank" rel="noreferrer" >{row.edges?.strategy?.name}</a><br />
                                            牵引bps:  <ShowBitString data={row.edges?.strategy?.bps} /><br />
                                            牵引pps:  <ShowBitString data={row.edges?.strategy?.pps} /><br />
                                        </>)
                                    }()
                                } />
                            </>
                            )}


                            <ContentDiv title="告警值" content={
                                <>
                                    bps: <ShowBitString data={row.max_bps} /> <br />
                                    pps: <ShowBitString data={row.max_bps} /><br />
                                </>
                            } />
                            {row.attack_info.maxBps !== 0 && (
                                <ContentDiv title="峰值" content={
                                    <>
                                        bps: <ShowBitString data={row.attack_info.maxBps} /> <br />
                                        pps: <ShowBitString data={row.attack_info.maxPps} /><br />
                                    </>
                                } />
                            )}
                            <ContentDiv title="时间" content={
                                function () {
                                    let base_time = <TimePass startTime={row.start_time} receivedTime={row.created_at} endTime={row.end_time} />
                                    if (row.edges?.wofang_ticket?.created_at && row.edges?.wofang_ticket.status === "success") {
                                        return (<>
                                            {base_time}
                                            沃防清洗: {formatTime(row.edges?.wofang_ticket?.created_at)} <br />
                                        </>)
                                    } else {
                                        return (<>{base_time}</>)
                                    }
                                }()
                            } />
                        </Space>
                    </>
                    ),
                },
            }}
        />
    </>)
}

export default DDosAttack;