import { formatTime, timeIsNotNull, timePass } from "@/utils/util";

type FormProps = {
    startTime: string;
    receivedTime: string;
    endTime: string;
};

const TimePass: React.FC<FormProps> = (props) => {
    let duration
    let startTime = <>攻击开始: {formatTime(props.startTime)} </>
    let receivedTime = <>接收告警: {formatTime(props.receivedTime)}</>
    let endTime = <>攻击结束: {formatTime(props.endTime)} </>
    let base_time = <>
        {startTime}<br />
        {receivedTime}<br />
    </>
    if (timeIsNotNull(props.endTime)) {
        base_time = <>
            {base_time}
            {endTime}<br />
        </>
        duration = timePass(props.startTime, props.endTime)
    } else {
        duration = timePass(props.startTime, new Date().toString())
    }
    base_time = <>
        {base_time}
        <>攻击持续: {duration} </><br />
    </>
    return (<>{base_time}</>)
}

export default TimePass;

