import ProCard from "@ant-design/pro-card";
import React from 'react';
import { PageContainer } from "@ant-design/pro-components";
import DDosAttack from "./DDosAttack";
import MatrixAttack from "./MatrixAttack";
import GrafanaFlow from "./GrafanaFlow";

const Dashboard: React.FC = () => {
  return (
    <PageContainer>
      <ProCard >
        <ProCard title="出口流量图">
          <GrafanaFlow />
        </ProCard>
      </ProCard>
      <ProCard>
        <DDosAttack />
      </ProCard>
      <ProCard >
        <MatrixAttack />
      </ProCard>
    </PageContainer>
  );
}

export default Dashboard;
