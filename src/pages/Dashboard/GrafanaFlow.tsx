import Cookies from 'js-cookie';
import React, { useRef, useEffect } from 'react';

const GrafanaFlow: React.FC = () => {

  // 根据实际情况，选择正式或测试环境。
  const GRAFANA_ORIGIN = 'https://grafana.monitor.nie.netease.com';
  // uid: dashboard uid，slug：dashboard 名称，根据仪表盘 url 进行调整
  const GRAFANA_PATHNAME = '/d/b2082606-8d85-4316-bd66-c4bc4a1f9eaf/ddos-interface-netflow-embeded';
  // 默认值，具体参数请参考下面 URL 参数说明
  const GRAFANA_SEARCH = '?orgId=1&viewPanel=2';
  // 当前项目
  const projectCode = 'sesa';
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const url = `${GRAFANA_ORIGIN}${GRAFANA_PATHNAME}${GRAFANA_SEARCH}`

  // 接收 / 发送 message
  useEffect(() => {
    const iframe = iframeRef.current
    function iframeOnloadHandler() {
      const cookies = {
        ACCESS_TOKEN: Cookies.get('ACCESS_TOKEN'),
        AUTH_USER: Cookies.get('AUTH_USER'),
        AUTH_PROJECT: projectCode,
      }
      // 用于登陆后页面重定向
      const location = {
        pathname: GRAFANA_PATHNAME,
        hash: window.location.hash,
        search: window.location.search,
      }
      if (iframe?.contentWindow) {
        iframe.contentWindow.postMessage({ cookies, location }, GRAFANA_ORIGIN)
      }
    }

    function listenGrafanaHandler({ data, origin }: MessageEvent) {
      const { message } = data
      // grafana 初始化时，发送 cookies
      if (message === 'grafana init') {
        iframeOnloadHandler()
      }
    }
    // iframe 资源加载完后，发送 cookies
    iframe?.addEventListener('load', iframeOnloadHandler)
    window.addEventListener('message', listenGrafanaHandler)

    return () => {
      iframe?.removeEventListener('load', iframeOnloadHandler)
      window.removeEventListener('message', listenGrafanaHandler)
    }
  }, [])

  return (
    <iframe
      ref={iframeRef}
      title="Grafana"
      src={url}
      frameBorder="0"
      width="100%"
      height="500"
    />

  );
}

export default GrafanaFlow;
