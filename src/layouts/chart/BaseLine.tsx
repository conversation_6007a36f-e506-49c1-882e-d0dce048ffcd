import { Line } from '@ant-design/charts';
import React from 'react';

export type Props = {
  data: any;
  formatMethod: any;
};

const BaseLine: React.FC<Props> = (props) => {
  const { data } = props;
  const config = {
    data,
    xField: 'time',
    yField: 'value',
    seriesField: 'name',
    // xAxis: { type: 'time' },
    yAxis: {
      label: {
        // formatter: function formatter(v) {
        //   // return (v / gBase);
        //   return (v / gBase).toFixed(2) + "G"
        // },
        formatter: props.formatMethod,
      },
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 5000,
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
  };

  return <Line {...config} />;
};

export default BaseLine;