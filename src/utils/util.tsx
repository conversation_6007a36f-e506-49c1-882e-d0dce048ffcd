import moment from "moment";

export const valueEnum = {
  true: { text: '是' },
  false: { text: '否' },
};

export const typeEnum = {
  1: { text: '防护群组' },
  2: { text: '虚拟线路Pool' },
};
export const protectEnum = {
  '-1': { text: '无' },
  1: { text: 'BGP' },
  10010: { text: '联通沃防' },
  10000: { text: '电信' },
  10086: { text: '移动' },
};

export const protectEnum2 = {
  '-1': { text: '无' },
  1: { text: '联通沃防' },
  10010: { text: '联通沃防' },
  10000: { text: '电信' },
  10086: { text: '移动' },
};

export const ispNameEnum = {
  'ChinaUnicom': { text: '联通' },
};

export const ispEnum = {
  '-1': { text: '未知' },
  1: { text: 'BGP' },
  10010: { text: '联通' },
  10000: { text: '电信' },
  10086: { text: '移动' },
};

export const dragTypeEnum = {
  qy: { text: '牵引清洗' },
  hd: { text: '黑洞' },
};

export const permissionEnum = {
  g: { text: '用户角色权限' },
  p: { text: '角色资源权限' },
};
export const divertTypeEnum = {
  1: { text: '上线' },
  2: { text: '下线' },
  3: { text: '调优' },
};

export const opTypeEnum = {
  1: { text: '自动' },
  2: { text: '手动' },
};

export const configTypeEnum = {
  1: { text: '默认' },
  2: { text: '自定义' },
};

export const selectOptions = [
  {
    value: 'false',
    label: '否',
  },
  {
    value: 'true',
    label: '是',
  },
];

export const protocolEnum = {
  6: { text: 'TCP' },
  17: { text: 'UDP' },
};

export const defenceLevelEnum = {
  0: { text: '监控而不丢包' },
  1: { text: '丢包' },
};

export const defenceModeEnum = {
  0: { text: '清洗dack攻击' },
  1: { text: '清洗单流流攻击' },
  2: { text: '清洗同源多流攻击' },
};

export const roleColumnEnum = {
  query: { text: '查询' },
  new: { text: '新建' },
  edit: { text: '编辑' },
  viewDetail: { text: '查看详情' },
  view: { text: '查看' },
  delete: { text: '删除' },
  bulkDelete: { text: '批量删除' },
  bulkCreate: { text: '批量创建' },
};

export const roleRadioEnum = [
  {
    value: 'query',
    label: '查询',
  },
  {
    value: 'view',
    label: '查看',
  },
  {
    value: 'viewDetail',
    label: '查看详情',
  },
  {
    value: 'new',
    label: '新建',
  },
  {
    value: 'edit',
    label: '编辑',
  },
  {
    value: 'delete',
    label: '删除',
  },
  {
    value: 'bulkDelete',
    label: '批量删除',
  },
  {
    value: 'bulkCreate',
    label: '批量创建',
  },
];

export const actRadioEnum = [
  {
    value: 'GET',
    label: '查询/查看/查看详情',
  },
  {
    value: 'POST',
    label: '新建/批量删除',
  },
  {
    value: 'PUT',
    label: '编辑',
  },
  {
    value: 'DELETE',
    label: '删除',
  },
];

export const attackStatusEnum = {
  1: { text: '结束' },
  2: { text: '攻击中' },
};

export const WofangTypes = {
  0: '自定义攻击类型',
  1: '自定义攻击类型',
  2: '自定义攻击类型',
  3: '自定义攻击类型',
  4: '自定义攻击类型',
  5: '自定义攻击类型',
  6: '自定义攻击类型',
  7: '自定义攻击类型',
  8: '自定义攻击类型',
  9: '自定义攻击类型',
  10: 'SYN Flood',
  11: 'ACK Flood',
  12: 'SYN-ACK Flood',
  13: 'FIN/RST Flood',
  14: 'Concurrent Connections Flood',
  15: 'New Connections Flood',
  16: 'TCP Fragment Flood',
  17: 'TCP Fragment Bandwidth Overflow',
  18: 'TCP Bandwidth Overflow',
  19: 'UDP Flood',
  20: 'UDP Fragment Flood',
  21: 'UDP Fragment Bandwidth Overflow',
  22: 'UDP Bandwidth Overflow',
  23: 'ICMP Flood',
  24: 'Other Bandwidth Overflow',
  25: 'Single IP Bandwidth Overflow',
  26: 'HTTPS Flood',
  27: 'HTTP Flood',
  28: '',
  29: 'DNS Query Flood',
  30: 'DNS Reply Flood',
  31: 'SIP Flood',
  32: 'Blacklist',
  33: 'URI Monitor',
  34: 'Global TCP Fragment Abnormal',
  35: 'Global TCP Abnormal',
  36: 'Global UDP Fragment Abnormal',
  37: 'Global UDP Abnormal',
  38: 'Global ICMP Abnormal',
  39: 'Global Other Abnormal',
  40: 'TCP Connection Flood',
  41: 'Domain Hijacking',
  42: 'DNS Cache Poisoning',
  43: 'DNS Reflection',
  44: 'DNS Size Abnormal',
  45: 'Source DNS Request Flow Abnormal',
  46: 'Source DNS Reply Flow Abnormal',
  47: 'DNS Request Domain Flow Abnormal',
  48: 'DNS Reply Domain Flow Abnormal',
  49: 'DNS IP TTL Check Fail',
  50: 'DNS Format Error',
  51: 'DNS Cache Match',
  52: 'Port Scanning Attack',
  53: 'TCP Malformed',
  54: 'BGP Flood Attack',
  55: 'TCP-authenticated UDP Attack',
  56: 'DNS No Such Name',
  57: 'Other Flood',
  58: 'Zone Bandwidth Overflow',
  59: 'HTTP Slow Attack',
  60: 'Botnets/Trojan horses/Worms Attack',
  61: 'Malicious Domains Attack',
  62: 'Filter Attack',
  63: 'Web Attack',
  64: 'SIP Source Rate Abnormity',
  65: 'Anti-Malware',
  66: 'IP Reputation',
  67: 'Location Attack',
  68: 'Destination IP new session rate limiting',
  69: 'TCP Protocol Block',
  70: 'UDP Protocol Block',
  71: 'ICMP Protocol Block',
  72: 'Other Protocol Block',
  73: 'Host Traffic Over Flow',
  74: 'UDP Malformed',
  75: 'TCP Dport Traffic Limit',
  76: 'TCP Dport Relation Defense',
  99: 'Dark IP',
  200: 'Ip frangment attack',
  201: 'Ip malformation attack',
  202: 'Acl filter',
  203: 'CC',
  204: 'Abnormal flow ',
  205: 'TCP limit',
  206: 'ehlink',
  207: 'unusual',
};

export const paramEnum = {
  0: { text: '关' },
  1: { text: '开' },
  2: { text: '自动' },
};

export const alertStatusEnumMap = {
  0: 'NDS-未防护',
  1: 'NDS-自动牵引',
  2: 'NDS-自动回牵',
  3: 'NDS-不自动牵引',
  4: 'NDS-不自动回牵',

  10: '联通沃防：成功',
  11: '联通沃防：失败',
  12: '联通沃防：已防护',
};
export const alertStatusEnumMap2 = {
  0: '未防护',
  10: '联通沃防：成功',
  11: '联通沃防：失败',
  12: '联通沃防：已防护',
};
export const alertColorMap = {
  0: 'red',
  1: 'green',
  2: 'green',
  3: 'orange',
  4: 'orange',

  10: 'green',
  11: 'red',
  12: 'green',
};

export const alertStatusEnum = [
  {
    label: 'NDS',
    options: [
      { value: 0, label: '未防护' },
      { value: 1, label: '自动牵引' },
      { value: 2, label: '自动回牵' },
      { value: 3, label: '不自动牵引' },
      { value: 4, label: '不自动回牵' },
    ],
  },
  // { value: 0, label: 'NDS-未防护' },
  // { value: 1, label: 'NDS-自动牵引' },
  // { value: 2, label: 'NDS-自动回牵' },
  // { value: 3, label: 'NDS-不自动牵引' },
  // { value: 4, label: 'NDS-不自动回牵' },

  {
    label: '联通沃防',
    options: [
      { value: 10, label: '成功' },
      { value: 11, label: '失败' },
      { value: 12, label: '已防护' }
    ],
  },
  // { value: 10, label: '联通沃防：成功' },
  // { value: 11, label: '联通沃防：失败' },
];

export const alertStatusEnum2 = [
  { value: 0, label: '未防护' },
  {
    label: '联通沃防',
    options: [
      { value: 10, label: '成功' },
      { value: 11, label: '失败' },
      { value: 12, label: '已防护' }
    ],
  },
  // { value: 10, label: '联通沃防：成功' },
  // { value: 11, label: '联通沃防：失败' },
];

export const timeFormat = 'YYYY-MM-DDTHH:mm:ssZ';
export const timeIsNotNull = (data: any) => {
  if (data && data !== '0001-01-01T00:00:00Z') {
    return true;
  }
  return false;
};

//判断是否需要通过传入的func处理data
//处理结果拼到一个json里
export const expandData = (expand: boolean | undefined, data: any, func: any) => {
  if (expand && data && func) {
    return { ...data, ...func(data) };
  }
  return data;
};

let kBase = Math.pow(10, 3);
let mBase = Math.pow(10, 6);
export const gBase = Math.pow(10, 9);
let tBase = Math.pow(10, 12);
let pBase = Math.pow(10, 15);
let eBase = Math.pow(10, 18);

// Bit2Str 比特数据转换成可读的数据
export const Bit2Str = (input: number) => {
  switch (true) {
    case input <= 0:
      return '';
    case input < kBase:
      return input.toFixed(2) + 'B';
    case input >= kBase && input < mBase:
      return (input / kBase).toFixed(2) + 'K';
    case input >= mBase && input < gBase:
      return (input / mBase).toFixed(2) + 'M';
    case input >= gBase && input < tBase:
      return (input / gBase).toFixed(2) + 'G';
    case input >= tBase && input < pBase:
      return (input / tBase).toFixed(2) + 'T';
    case input >= pBase && input < eBase:
      return (input / pBase).toFixed(2) + 'P';
    case input >= eBase:
      return 'max attack';
    default:
      return '';
  }
};

export const notNullPlaceholder = '一行一条数据，不能包含空行';
export const ipListPlaceholder = (
  <>
    一行一条数据，支持IPv4及IPv6，IP(IPv6)段，IP(IPv6)CIDR，且不能包含 空格 空行
    <br />
    IP段的IPv6最后不能是::
    <br />
    示例：
    <br />
    *******
    <br />
    *******/32
    <br />
    *******-12
    <br />
    *******-********
    <br />
    2403:c80:200:1005::2aba:c06
    <br />
    2403:c80:200:1005::/96
    <br />
    2403:c80:200:1005::2aba:c06-ceb
    <br />
    2403:c80:200:1005::2aba:c06-2403:c80:200:1005::2aba:ceb
  </>
);
export const spectrumLineCommnet = (
  <>
    <table border="1">
      <thead>
        <tr>
          <th colSpan="2">每条数据采样时间</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>时间范围</th>
          <th>采样时间</th>
        </tr>
        <tr>
          <td>小于30分钟</td>
          <td>1秒</td>
        </tr>
        <tr>
          <td>30分钟-1小时</td>
          <td>2秒</td>
        </tr>
        <tr>
          <td>1小时-6小时</td>
          <td>15秒</td>
        </tr>
        <tr>
          <td>6小时-12小时</td>
          <td>1分钟</td>
        </tr>
        <tr>
          <td>12小时-7天</td>
          <td>10分钟</td>
        </tr>

        <tr>
          <td>7天-半个月</td>
          <td>15分钟</td>
        </tr>
        <tr>
          <td>半个月-1个月</td>
          <td>30分钟</td>
        </tr>
        <tr>
          <td>1个月-3个月</td>
          <td>1小时</td>
        </tr>
      </tbody>
    </table>
  </>
);

export const cldDataEnum = {
  v2: { text: 'v2' },
  v3: { text: 'v3' },
  lbc: { text: 'lbc' },
};

export const ISPTypes = {
  '-1': '未知',
  0: '未知',
  1: 'BGP',
  2: 'Anycast',
  10000: '电信',
  10010: '联通',
  10086: '移动',
  10050: '铁通',
  985211: '教育网',
  600804: '鹏博士',
  96171: '华数',
  9990: '方正网络',
  96196: '歌华网络',
  1688: '阿里云',
  6800: '电信通',
  8882: '有线通',
  8883: '科技网',
  8884: '视讯宽带',
};

export const formatTime = (data: any) => {
  return moment(data).format('YYYY-MM-DD HH:mm:ss')
}


export const ispMapEnum = {
  'ChinaUnicom': '联通',
};

export const regionEnum = {
  'HangZhou': '杭州',
};


export const netEnum = {
  'Static': '静态',
  'BGP': 'BGP',
};

export const timePass = (start_time: string, end_time: string) => {
  let startTime = new Date(start_time); // 开始时间
  let endTime = new Date(end_time); // 结束时间
  let usedTime = endTime.getTime() - startTime.getTime(); // 相差的毫秒数
  let days = Math.floor(usedTime / (24 * 3600 * 1000)); // 计算出天数
  let leavel = usedTime % (24 * 3600 * 1000); // 计算天数后剩余的时间
  let hours = Math.floor(leavel / (3600 * 1000)); // 计算剩余的小时数
  let leavel2 = leavel % (3600 * 1000); // 计算剩余小时后剩余的毫秒数
  let minutes = Math.floor(leavel2 / (60 * 1000)); // 计算剩余的分钟数
  let ss = leavel2 / 1000 - minutes * 60
  if (days !== 0) {
    return days + '天' + hours + '时' + minutes + '分' + ss + '秒';
  } else {
    if (hours !== 0) {
      return hours + '时' + minutes + '分' + ss + '秒';
    }
    if (minutes !== 0) {
      return minutes + '分' + ss + '秒';
    }
    return + ss + '秒';
  }
}