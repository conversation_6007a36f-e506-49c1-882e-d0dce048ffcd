const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  console.log("Proxy setup is runninggggggggggg...");
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://127.0.0.1:9001',
      changeOrigin: true,
      onProxyReq: (proxyReq, req, res) => {
        // Add custom headers
        proxyReq.setHeader('Authorization', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6Imd1b2thaWx1bjAxIiwiZXhwIjoxNzI5MjM5MDkzLCJuYmYiOjE3Mjg2MzQyOTMsImlhdCI6MTcyODYzNDI5M30.rePBLcfj86TUacBSH7M6z_WJxHb8zNaHoFLNUJ7HurQ');
      },
    })
  );
};