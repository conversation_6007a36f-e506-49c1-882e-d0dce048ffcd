import { projectColumns } from "@/columns/Comon";
import { protectGroupColumns } from "@/columns/ProtectGroup";
import ProDescriptionsLayout from "@/layouts/extend/ProDescriptionsLayout";
import ProTableLayout from "@/layouts/extend/ProTableLayout";
import { typeEnum } from "@/utils/util";
import { ProColumns } from "@ant-design/pro-components";
import { Tag } from "antd";

type ShowFormProps = {
    data?: Partial<any>[];
};

export const groupCardColumns: ProColumns<API.ProtectGroup>[] = [
    ...projectColumns,
    {
        title: '群组名',
        dataIndex: 'group_name',
        valueType: 'text',
    },
    {
        title: '类型',
        dataIndex: 'type',
        valueType: 'text',
        valueEnum: typeEnum,
    },
    {
        title: '牵引策略',
        dataIndex: 'drag_info',
        valueType: 'text',
        hideInSearch: true,
        hideInDescriptions: true,
        render: (_, record) => {
            let dragColor = "orange", unDragColor = "orange"
            let dragText = "不自动牵引", backDragText = "不自动回牵"
            if (record.drag_info?.autoDrag) {
                dragColor = "green"
                dragText = "自动牵引"
            }
            if (record.drag_info?.autoUnDrag) {
                unDragColor = "green"
                backDragText = "自动回牵"
            }
            return (<>
                <Tag color={dragColor}>{dragText}</Tag>
                <Tag color={unDragColor}>{backDragText}</Tag>
            </>)
        }
    },
]
const GroupProCard: React.FC<ShowFormProps> = (props) => {
    if (props.data) {
        if (props.data.length === 1) {
            return <ProDescriptionsLayout data={props.data[0]} columns={protectGroupColumns} column={2} />
        }
        return (<>
            <ProTableLayout
                expandData={{
                    expandedRowRender: (record) => <ProDescriptionsLayout data={record} columns={protectGroupColumns} column={2} />,
                    rowExpandable: (record) => record.group_name !== 'Not Expandable',
                }}
                getMethod={() => { }}
                showRowSelect={false}
                showSearch={false}
                showToolbar={false}
                withPageContainer={false}
                columns={groupCardColumns}
                dataSource={props.data}
            />
        </>)
    }
    return <></>
};
export default GroupProCard;
