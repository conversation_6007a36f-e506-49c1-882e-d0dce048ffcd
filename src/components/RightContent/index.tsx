import { SearchOutlined } from '@ant-design/icons';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { SelectLang, useModel } from '@umijs/max';
import React, { useRef, useState } from 'react';
import Avatar from './AvatarDropdown';
import { Input, InputRef, Modal, message } from 'antd';

import GroupProCard from './GroupProCard';
import { PageContainer } from '@ant-design/pro-components';
import { getCommonProtectgroup } from '@/services/common/ProtectGroup';

const { Search } = Input;

export type SiderTheme = 'light' | 'dark';


const GlobalHeaderRight: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [groupData, setGroupData] = useState();
  const inputRef = useRef<InputRef>(null);

  setTimeout(() => {
    inputRef.current?.focus()
  }, 1);

  const className = useEmotionCss(() => {
    return {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      gap: 8,
    };
  });

  const actionClassName = useEmotionCss(({ token }) => {
    return {
      display: 'flex',
      float: 'right',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      cursor: 'pointer',
      padding: '0 12px',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    };
  });

  const { initialState } = useModel('@@initialState');

  if (!initialState || !initialState.settings) {
    return null;
  }

  const showModal = () => {
    setIsModalOpen(true);
  };


  const handleCancel = () => {
    setIsModalOpen(false);
    setGroupData(undefined)
  };
  const onSearch = async (value: any) => {
    setLoading(true)
    if (value === "") {
      setGroupData(undefined)
    } else {
      const res = await getCommonProtectgroup({ "expand_ip": value });
      if (res.success) {
        setGroupData(res.data)
      } else {
        setGroupData(undefined)
        message.info(res.message)
      }
    }
    setLoading(false)
  };

  return (
    <div className={className}>
      {/* <span
        className={actionClassName}
        onClick={() => {
          window.open('https://pro.ant.design/docs/getting-started');
        }}
      >
        <QuestionCircleOutlined />
      </span> */}

      <span className={actionClassName} onClick={showModal}>
        <SearchOutlined />
      </span>

      <Avatar />
      <SelectLang className={actionClassName} />


      <Modal open={isModalOpen} onCancel={handleCancel}
        footer={null}
        closable={false}
        destroyOnClose
        width={'60%'}
      >
        <PageContainer
          ghost
          header={{
            title: null,
            breadcrumb: {},
          }}
        >
          <Search placeholder="模糊查询IP所在NDS防护群组【回车即搜索】"
            onSearch={onSearch}
            bordered={false}
            loading={loading}
            autoFocus={true}
            ref={inputRef}
          />
          <GroupProCard data={groupData} />
        </PageContainer>
      </Modal>
    </div >
  );
};
export default GlobalHeaderRight;
