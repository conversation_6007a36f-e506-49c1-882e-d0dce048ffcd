import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import ShowProTableLayout from "@/layouts/show/ShowProTableLayout";
import TimeLayout from "@/layouts/show/TimeLayout";
import { configTypeEnum, divertTypeEnum, opTypeEnum } from "@/utils/util";
import { ProColumns } from "@ant-design/pro-components";
import { Tag } from "antd";
import { projectColumns, remarkColumns, searchColumns, timeColumns } from "./Comon";

export const contactListColumns: ProColumns<API.User2>[] = [
    {
        title: '姓名',
        dataIndex: 'name',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '电话',
        dataIndex: 'phone',
        valueType: 'text',
        hideInSearch: true,
    },
]


export const socGroupTicketColumns: ProColumns<API.SocGroupTicket>[] = [
    ...searchColumns,
    ...projectColumns,
    {
        title: '标题',
        dataIndex: 'name',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => {
            if (record.group_ticket_id) {
                return <>{record.name}<Tag color="green">工单-{record.group_ticket_id}</Tag></>
            }
            return <>{record.name}<Tag color="red">工单提交失败</Tag></>
        }
    },
    {
        title: '产品',
        dataIndex: 'product_name',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => {
            if (record.product_name !== "" && record.product_code !== "") {
                return <>{record.product_name}-{record.product_code}</>
            }
            if (record.product_name !== "" && record.product_code === "") {
                return <>{record.product_name}</>
            }
            if (record.product_name === "" && record.product_code !== "") {
                return <>{record.product_code}</>
            }
            return <>-</>
        }
    },
    // {
    //     title: '产品代号',
    //     dataIndex: 'product_code',
    //     valueType: 'text',
    //     hideInSearch: true,
    // },

    // {
    //     title: '类型',
    //     dataIndex: 'type',
    //     valueType: 'text',
    //     hideInTable: true,
    //     hideInSearch: true,
    // },
    {
        title: '参数配置',
        dataIndex: 'config_args',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: '参数',
        dataIndex: 'config_type',
        valueType: 'select',
        valueEnum: configTypeEnum,
        hideInSearch: true,
    },
    {
        title: '应急接口人',
        dataIndex: 'contact_list',
        valueType: 'textarea',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => (<ShowProTableLayout dataSource={record.contact_list} columns={contactListColumns} />)
    },
    {
        title: '创建者',
        dataIndex: 'create_user_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        hideInDescriptions: true,
    },
    {
        title: '部门ID',
        dataIndex: 'department_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },

    {
        title: '清洗类型',
        dataIndex: 'divert_type',
        valueType: 'select',
        valueEnum: divertTypeEnum,
        hideInSearch: true,
    },
    {
        title: '集团SOC跟踪者ID列表',
        dataIndex: 'follow_list',
        valueType: 'textarea',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => (<ShowArrayProCardLayout data={record.follow_list} />)
    },
    {
        title: 'IP列表',
        dataIndex: 'ip_list',
        valueType: 'textarea',
        hideInTable: true,
        render: (_, row) => <ShowArrayProCardLayout data={row.ip_list} title='可折叠' />
    },
    {
        title: '牵引IP最小带宽',
        dataIndex: 'min_bandwidth',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },
    {
        title: '操作方式',
        dataIndex: 'op_type',
        valueType: 'select',
        valueEnum: opTypeEnum,
        hideInSearch: true,
    },
    {
        title: '操作时间',
        dataIndex: 'op_time',
        valueType: 'dateTime',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <TimeLayout time={record.op_time} />
    },
    {
        title: '操作时间',
        dataIndex: 'op_time',
        valueType: 'dateTimeRange',
        // table中不显示
        hideInTable: true,
        //详情中不显示
        hideInDescriptions: true,
        search: {
            transform: (value) => ({
                //dateTimeRange 转换 成gte和lte字段
                op_time_gte: value[0],
                op_time_lte: value[1],
            })
        },
    },


    {
        title: 'NDS工单编号',
        dataIndex: 'group_ticket_id',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },
    {
        title: '错误信息',
        dataIndex: 'error_info',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },
    {
        title: '描述',
        dataIndex: 'description',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    ...remarkColumns,
    ...timeColumns,
]
