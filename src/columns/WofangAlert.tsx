import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, createTimeColumns, remarkColumns, projectColumns, updateTimeColumns, startEndTimeColumns } from "./Comon";
import { WofangTypes, attackStatusEnum } from "@/utils/util";
import ShowBitString from "@/layouts/show/ShowBitString";
export const wofangAlertColumns: ProColumns<API.WofangAlert>[] = [
    ...searchColumns,
    ...projectColumns,
    {
        title: 'IP',
        dataIndex: 'zone_ip',
        valueType: 'text',
        sorter: true,
    },
    {
        title: 'attack_id',
        dataIndex: 'attack_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: '类型',
        dataIndex: 'attack_type',
        valueType: 'textarea',
        hideInSearch: true,
        render: (_, row) => <ShowArrayProCardLayout data={row.attack_type} dataMap={WofangTypes} noCollapsible />
    },
    {
        title: '状态',
        dataIndex: 'attack_status',
        valueType: 'select',
        valueEnum: attackStatusEnum,
        hideInSearch: true,
    },
    {
        title: 'device_ip',
        dataIndex: 'device_ip',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: '攻击bps',
        dataIndex: 'max_in_bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.max_in_bps} />
    },
    {
        title: '清洗bps',
        dataIndex: 'max_drop_bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.max_drop_bps} />

    },

    ...startEndTimeColumns,
    ...remarkColumns,
]
