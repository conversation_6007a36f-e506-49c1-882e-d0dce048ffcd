import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, endTimeColumns, remarkColumns, startTimeColumns, projectColumns, emptyColumns, matrixCommonColumns } from "./Comon";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import { Select, Tag } from "antd";
import ShowBitString from "@/layouts/show/ShowBitString";
import { alertColorMap, alertStatusEnum2, alertStatusEnumMap2, ispNameEnum } from "@/utils/util";
import ProDescriptionsLayout from "@/layouts/extend/ProDescriptionsLayout";
import { wofangColumns } from "./Wofang";
import { matrixStrategyColumns } from "./MatrixStrategy";

export const edgeColumns: ProColumns<API.SpectrumAlertEdges>[] = [
    // {
    //     title: '联通沃防',
    //     dataIndex: 'wofang_ticket',
    //     valueType: 'text',
    //     render: (_, record) => (<ProDescriptionsLayout data={record.wofang_ticket} columns={wofangColumns} column={3} />)
    // },
    {
        title: '防护策略',
        dataIndex: 'matrix_strategy',
        valueType: 'text',
        render: (_, record) => (<ProDescriptionsLayout data={record.matrix_strategy} columns={matrixStrategyColumns} column={3} />)
    },
]
export const matrixSpectrumAlertColumns: ProColumns<API.MatrixSpectrumAlert>[] = [
    ...searchColumns,
    ...matrixCommonColumns,
    {
        title: 'IP列表',
        dataIndex: 'ip_list',
        valueType: 'textarea',
        // hideInTable: true,
        hideInSearch: true,
        render: (_, row) => <ShowArrayProCardLayout data={row.ip_list} noCollapsible />
    },
    {
        title: '防护状态',
        dataIndex: 'protect_status',
        valueType: 'textarea',
        hideInTable: true,
        hideInDescriptions: true,
        hideInSearch: true,
        render: (_, row) => {
            return < ShowArrayProCardLayout data={row.protect_status} dataMap={alertStatusEnumMap2} colorMap={alertColorMap} noCollapsible />
        }
    },
    {
        title: '策略',
        dataIndex: 'matrix_strategy',
        valueType: 'text',
        hideInSearch: true,
        render: (_, row) => {
            return <>
                {row.edges?.matrix_strategy?.name}
            </>
        }
    },
    {
        title: '类型',
        dataIndex: 'attack_type',
        hideInSearch: true,
        render: (_, row) => row?.attack_type?.split(',').map((item) => <Tag key={item}>{item}</Tag>),
    },
    {
        title: '告警bps',
        dataIndex: 'bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.bps} />
    },
    {
        title: '最大bps',
        dataIndex: 'max_bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.attack_info?.maxBps} />
    },

    {
        title: 'wofang_id',
        dataIndex: 'wofang_id',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
        hideInDescriptions: true,
    },
    ...startTimeColumns,
    ...endTimeColumns,
    ...remarkColumns,
    ...emptyColumns,
    ...emptyColumns,
    {
        dataIndex: 'edges',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => (<ProDescriptionsLayout data={record.edges} columns={edgeColumns} column={1} />)
    },
]
