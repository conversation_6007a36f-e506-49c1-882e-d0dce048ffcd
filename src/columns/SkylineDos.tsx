import ProDescriptionsLayout from "@/layouts/extend/ProDescriptionsLayout";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, createTimeColumns, endTimeColumns, remarkColumns, startTimeColumns, projectColumns, updateTimeColumns } from "./Comon";
import { attackCounterColumns } from "./AttackCounter";
import ProTableLayout from "@/layouts/extend/ProTableLayout";
export const skylineDosColumns: ProColumns<API.SkylineDos>[] = [
    ...searchColumns,
    ...projectColumns,

    {
        title: '资源',
        dataIndex: 'resource',
        valueType: 'text',
    },
    {
        title: '资源类型',
        dataIndex: 'resource_type',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '区域',
        dataIndex: 'region',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '攻击类型',
        dataIndex: 'vector_types',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.vector_types} title='可折叠' noCollapsible />
    },
    {
        title: '状态',
        dataIndex: 'status',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '攻击数据',
        dataIndex: 'attack_counters',
        valueType: 'textarea',
        hideInTable: true,
        render: (_, record) => (
            // <ProDescriptionsLayout data={record.attack_counters} columns={attackCounterColumns} column={1} />
            <ProTableLayout
                // expandData={{
                //     expandedRowRender: (record) => <ProDescriptionsLayout data={record} columns={protectGroupColumns} column={2} />,
                //     rowExpandable: (record) => record.group_name !== 'Not Expandable',
                // }}
                getMethod={() => { }}
                showRowSelect={false}
                showSearch={false}
                showToolbar={false}
                withPageContainer={false}
                columns={attackCounterColumns}
                dataSource={record.attack_counters}
            />

        )
    },
    {
        title: 'attack_id',
        dataIndex: 'attack_id',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },

    {
        title: '持续时间',
        dataIndex: 'duration_time',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },
    ...startTimeColumns,
    ...endTimeColumns,
    // ...createTimeColumns,
    // ...updateTimeColumns,
    ...remarkColumns,
]
