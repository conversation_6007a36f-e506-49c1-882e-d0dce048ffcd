import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, timeColumns, remarkColumns } from "./Comon";
import { cldDataEnum } from "@/utils/util";
export const dataSyncColumns: ProColumns<API.DataSync>[] = [
    ...searchColumns,
    {
        title: '来源',
        dataIndex: 'data_type',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '类型',
        dataIndex: 'type',
        valueType: 'select',
        valueEnum: cldDataEnum,
    },
    {
        title: '前一次数据',
        dataIndex: 'pre_data_list',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.pre_data_list} title='可折叠' />
    },
    {
        title: '当前数据',
        dataIndex: 'data_list',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.data_list} title='可折叠' />
    },


    ...remarkColumns,
    ...timeColumns,
]
