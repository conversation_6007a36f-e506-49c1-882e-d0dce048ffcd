import { valueEnum } from '@/utils/util';
import { ProColumns } from '@ant-design/pro-components';
import { searchColumns, timeColumns } from './Comon';
export const userColumns: ProColumns<API.User>[] = [
  ...searchColumns,
  {
    title: '用户名',
    dataIndex: 'name',
    valueType: 'text',
  },
  {
    title: '密码',
    dataIndex: 'password',
    valueType: 'text',
    hideInTable: true,
    hideInSearch: true,
  },
  {
    title: '超级管理员',
    dataIndex: 'super_admin',
    valueType: 'select',
    valueEnum: valueEnum,
    hideInSearch: true,
  },

  {
    title: '有效',
    dataIndex: 'valid',
    valueType: 'select',
    valueEnum: valueEnum,
    hideInSearch: true,
  },
  ...timeColumns,
];
