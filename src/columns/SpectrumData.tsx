import ShowBitString from "@/layouts/show/ShowBitString";
import { ProColumns } from "@ant-design/pro-components";
import { createTimeColumns, projectColumns, singelTimeColumns } from "./Comon";
export const spectrumDataColumns: ProColumns<API.SpectrumData>[] = [
    ...projectColumns,
    {
        title: 'IP',
        dataIndex: 'ip',
        valueType: 'text',
    },
    {
        title: 'bps',
        dataIndex: 'bps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.bps} />

    },
    {
        title: 'pps',
        dataIndex: 'pps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.pps} />
    },
    {
        title: 'ack_bps',
        dataIndex: 'ack_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ack_bps} />
    },
    {
        title: 'ack_pps',
        dataIndex: 'ack_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ack_pps} />
    },
    {
        title: 'syn_bps',
        dataIndex: 'syn_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.syn_bps} />
    },
    {
        title: 'syn_pps',
        dataIndex: 'syn_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.syn_pps} />
    },
    {
        title: 'syn_ack_bps',
        dataIndex: 'syn_ack_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.syn_ack_bps} />
    },
    {
        title: 'syn_ack_pps',
        dataIndex: 'syn_ack_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.syn_ack_pps} />
    },
    {
        title: 'icmp_bps',
        dataIndex: 'icmp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.icmp_bps} />
    },
    {
        title: 'icmp_pps',
        dataIndex: 'icmp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.icmp_pps} />
    },
    {
        title: 'small_pps',
        dataIndex: 'small_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.small_pps} />
    },
    {
        title: 'ntp_bps',
        dataIndex: 'ntp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ntp_bps} />
    },
    {
        title: 'ntp_pps',
        dataIndex: 'ntp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ntp_pps} />
    },
    {
        title: 'dns_query_bps',
        dataIndex: 'dns_query_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.dns_query_bps} />
    },
    {
        title: 'dns_query_pps',
        dataIndex: 'dns_query_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.dns_query_pps} />
    },
    {
        title: 'dns_answer_bps',
        dataIndex: 'dns_answer_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.dns_answer_bps} />
    },
    {
        title: 'dns_answer_pps',
        dataIndex: 'dns_answer_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.dns_answer_pps} />
    },
    {
        title: 'ssdp_bps',
        dataIndex: 'ssdp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ssdp_bps} />
    },
    {
        title: 'ssdp_pps',
        dataIndex: 'ssdp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.ssdp_pps} />
    },
    {
        title: 'udp_bps',
        dataIndex: 'udp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.udp_bps} />
    },
    {
        title: 'udp_pps',
        dataIndex: 'udp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.udp_pps} />
    },
    {
        title: 'qps',
        dataIndex: 'qps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.qps} />
    },
    {
        title: 'data_type',
        dataIndex: 'data_type',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'host',
        dataIndex: 'host',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'ip_type',
        dataIndex: 'ip_type',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'monitor',
        dataIndex: 'monitor',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'monitor_id',
        dataIndex: 'monitor_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'product',
        dataIndex: 'product',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },

    {
        title: 'receive_count',
        dataIndex: 'receive_count',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },

    {
        title: 'spectrum_alert_id',
        dataIndex: 'spectrum_alert_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },

    ...singelTimeColumns,
    ...createTimeColumns,
]
