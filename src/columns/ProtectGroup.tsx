import ProDescriptionsLayout from "@/layouts/extend/ProDescriptionsLayout";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import ShowBitString from "@/layouts/show/ShowBitString";
import { paramEnum, typeEnum, valueEnum, } from "@/utils/util";
import { ProCard, ProColumns } from "@ant-design/pro-components";
import { projectColumns, remarkColumns, searchColumns, timeColumns } from "./Comon";
import { Tag } from "antd";

//ProtectGroup的不含操作的字段
//复合字段（只展示）的ProColumns放在这里
//复合字段不需要表格的行操作[行编辑、查看、删除]
//具体操作由实际页面设置

//监控信息
export const monitorInfoColumns: ProColumns<API.MonitorInfo>[] = [
    {
        title: '总bps',
        dataIndex: 'totalBps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.totalBps} />
    },
    {
        title: '总pps',
        dataIndex: 'totalPps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.totalPps} />
    },
    {
        title: '开启机器学习',
        dataIndex: 'autoML',
        valueType: 'select',
        valueEnum,
    },
    {
        title: 'ack_bps',
        dataIndex: 'ack_bps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.ackBps} />
    },
    {
        title: 'ack_pps',
        dataIndex: 'ack_pps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.ackPps} />
    },
    {
        title: 'icmp_bps',
        dataIndex: 'icmp_bps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.icmpBps} />
    },
    {
        title: 'icmp_pps',
        dataIndex: 'icmp_pps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.icmpPps} />
    },
    {
        title: 'syn_bps',
        dataIndex: 'syn_bps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.synBps} />
    },
    {
        title: 'syn_pps',
        dataIndex: 'syn_pps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.ackBps} />
    },

    {
        title: 'udp_bps',
        dataIndex: 'udp_bps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.udpBps} />
    },
    {
        title: 'udp_pps',
        dataIndex: 'udp_pps',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.udpPps} />
    },
]
//牵引信息
export const dragInfoColumns: ProColumns<API.DragInfo>[] = [
    {
        title: '自动牵引',
        dataIndex: 'autoDrag',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '自动回迁',
        dataIndex: 'autoUnDrag',
        valueType: 'select',
        valueEnum,
    },
]
//ipv4配置
export const nds4ConfigColumns: ProColumns<API.Nds4Config>[] = [
    {
        title: '不清洗直接转发阈值',
        dataIndex: 'tNoCleanLimit',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.tNoCleanLimit} />
    },
    {
        title: 'ACL开关',
        dataIndex: 'sAcl',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '防护除SYN外的TCP flood攻击',
        dataIndex: 'sAntiOtherTcp',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: '黑名单开关',
        dataIndex: 'sBlacklist',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '特征匹配开关',
        dataIndex: 'sContentMatch',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '目的ip限速开关',
        dataIndex: 'sDstSpeedLimit',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '过滤特殊报文开关',
        dataIndex: 'sEliminatePkt',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '首包丢弃配置',
        dataIndex: 'sFirstPktDrop',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: '畸形包报文检测开关',
        dataIndex: 'sMalformedPkt',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '国外地理位置',
        dataIndex: 'sPositionForeign',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: 'idc国家地理位置',
        dataIndex: 'sPositionIdc',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: '源认证配置',
        dataIndex: 'sSourceCheck',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: '源ip限速开关',
        dataIndex: 'sSrcSpeedLimit',
        valueType: 'select',
        valueEnum,
    },
    {
        title: 'TCP反射配置',
        dataIndex: 'sTcpReflection',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: 'UDP反射配置',
        dataIndex: 'sUdpReflection',
        valueType: 'text',
        valueEnum: paramEnum,
    },
    {
        title: '白名单开关',
        dataIndex: 'sWhitelist',
        valueType: 'select',
        valueEnum,
    },
]
//ipv6配置
export const nds6ConfigColumns: ProColumns<API.Nds6Config>[] = [
    {
        title: '不清洗直接转发阈值',
        dataIndex: 'tNoCleanLimit',
        valueType: 'text',
        render: (_, record) => <ShowBitString data={record.tNoCleanLimit} />
    },
    {
        title: '目的ip限速开关',
        dataIndex: 'sDstSpeedLimit',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '畸形包报文检测开关',
        dataIndex: 'sMalformedPkt',
        valueType: 'select',
        valueEnum,
    },
    {
        title: '源ip限速开关',
        dataIndex: 'sSrcSpeedLimit',
        valueType: 'select',
        valueEnum,
    },
    {
        title: 'UDP反射配置',
        dataIndex: 'sUdpReflection',
        valueType: 'text',
        valueEnum: paramEnum,
    },
]

export const protectGroupColumns: ProColumns<API.ProtectGroup>[] = [
    ...searchColumns,
    ...projectColumns,
    {
        title: '群组名',
        dataIndex: 'group_name',
        valueType: 'text',
    },
    {
        title: 'NDS群组编号',
        dataIndex: 'group_id',
        valueType: 'text',
        sorter: true,
        hideInSearch: true,
    },
    {
        title: 'IP列表',
        dataIndex: 'ip_list',
        valueType: 'textarea',
        hideInTable: true,
        hideInSearch: true,
        render: (_, row) => <ShowArrayProCardLayout data={row.ip_list} title='可折叠' />
    },
    {
        title: 'IPv4',
        dataIndex: 'expand_ip',
        valueType: 'textarea',
        hideInTable: true,
        render: (_, row) => {
            return <ProCard
                title="IPv4列表展开"
                collapsible
                defaultCollapsed
                ghost
            >
                {row.expand_ip}
            </ProCard>

        }
    },
    {
        title: '类型',
        dataIndex: 'type',
        valueType: 'text',
        valueEnum: typeEnum,
    },
    {
        title: '牵引策略',
        dataIndex: 'drag_info',
        valueType: 'text',
        hideInSearch: true,
        hideInDescriptions: true,
        render: (_, record) => {
            let dragColor = "orange", unDragColor = "orange"
            let dragText = "不自动牵引", backDragText = "不自动回牵"
            if (record.drag_info?.autoDrag) {
                dragColor = "green"
                dragText = "自动牵引"
            }
            if (record.drag_info?.autoUnDrag) {
                unDragColor = "green"
                backDragText = "自动回牵"
            }
            return (<>
                <Tag color={dragColor}>{dragText}</Tag>
                <Tag color={unDragColor}>{backDragText}</Tag>
            </>)
        }
    },
    ...timeColumns,
    ...remarkColumns,
    {
        title: '牵引策略',
        dataIndex: 'drag_info',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
        render: (_, record) => (<ProDescriptionsLayout data={record.drag_info} columns={dragInfoColumns} column={1} />)
    },

    {
        title: '监控阈值',
        dataIndex: 'monitor_info',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
        render: (_, record) => (<ProDescriptionsLayout data={record.monitor_info} columns={monitorInfoColumns} column={1} />),
    },
    {
        title: 'IPv4清洗参数',
        dataIndex: 'nds4_config',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
        render: (_, record) => (<ProDescriptionsLayout data={record.nds4_config} columns={nds4ConfigColumns} column={1} />)
    },
    {
        title: 'IPv6清洗参数',
        dataIndex: 'nds6_config',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
        render: (_, record) => (<ProDescriptionsLayout data={record.nds6_config} columns={nds6ConfigColumns} column={1} />)
    },
];