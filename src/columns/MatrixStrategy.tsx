import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, remarkColumns, nameColumns, matrixCommonColumns } from "./Comon";
import { timeColumns } from "./Comon";
import ShowBitString from "@/layouts/show/ShowBitString";
import { ispNameEnum, protectEnum2 } from "@/utils/util";
export const matrixStrategyColumns: ProColumns<API.MatrixStrategy>[] = [
    ...searchColumns,
    ...nameColumns,
    ...matrixCommonColumns,
    {
        title: '被攻击IP防护类型',
        dataIndex: 'drag_type',
        valueType: 'text',
        hideInSearch: true,
        valueEnum: protectEnum2,
    },
    {
        title: '监控bps',
        dataIndex: 'monitor_bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.monitor_bps} />
    },
    {
        title: '牵引bps',
        dataIndex: 'drag_bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.drag_bps} />
    },


    ...timeColumns,
    ...remarkColumns,
]
