import { ProColumns } from "@ant-design/pro-components";
import { cloudCommonColumns, cloudCommonTimeColumns } from "./CloudCommon";
import ShowBitString from "@/layouts/show/ShowBitString";
export const cloudFlowDataColumns: ProColumns<API.CloudFlowData>[] = [
    ...cloudCommonColumns,
    {
        title: 'pps>1000次数',
        dataIndex: 'flow_over_max_pps_count',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: '最大pps',
        dataIndex: 'max_attack_pps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.max_attack_pps} />

    },
    {
        title: '告警ID',
        dataIndex: 'cloud_alert_id',
        valueType: 'text',
        hideInSearch: true,
        hideInTable: true,
    },
    ...cloudCommonTimeColumns,
]
