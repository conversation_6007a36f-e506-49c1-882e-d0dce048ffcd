import TimeLayout from "@/layouts/show/TimeLayout";
import { dragTypeEnum } from "@/utils/util";
import { ProColumns } from "@ant-design/pro-components";
import { Tag } from "antd";
import { searchColumns, remarkColumns, projectColumns, timeColumns } from "./Comon";
export const wofangColumns: ProColumns<API.Wofang>[] = [
    ...searchColumns,
    ...projectColumns,
    {
        title: '标题',
        dataIndex: 'name',
        valueType: 'text',
    },
    {
        title: 'IP',
        dataIndex: 'ip',
        valueType: 'text',
    },
    {
        title: '类型',
        dataIndex: 'type',
        valueType: 'select',
        valueEnum: dragTypeEnum,
        // hideInSearch: true,
    },
    {
        title: '开始时间',
        dataIndex: 'start_time',
        valueType: 'dateTime',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <TimeLayout time={record.start_time} />
    },
    {
        title: '牵引时长',
        dataIndex: 'un_drag_second',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => {
            if (record.un_drag_second) {
                return <>{record.un_drag_second}秒</>
            } else
                return <>-</>
        }
    },
    {
        title: '状态',
        dataIndex: 'status',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => {
            if (record.status === "success") {
                return <Tag color="green">成功</Tag>
            } else
                return <>
                    <Tag color="red">失败</Tag>
                    <Tag>{record.error_info}</Tag>
                </>

        }
    },
    {
        title: '错误信息',
        dataIndex: 'error_info',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: '创建者',
        dataIndex: 'create_user_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        hideInDescriptions: true,
    },
    ...remarkColumns,
    ...timeColumns,
]
