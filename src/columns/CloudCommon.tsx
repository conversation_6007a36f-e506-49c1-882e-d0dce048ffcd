import { ProColumns } from "@ant-design/pro-components";
import { searchColumns, createTimeColumns, endTimeColumns, remarkColumns, startTimeColumns, projectColumns, updateTimeColumns } from "./Comon";
import { protocolEnum } from "@/utils/util";
export const cloudCommonColumns: ProColumns<any>[] = [
    ...searchColumns,
    ...projectColumns,
    {
        title: 'IP',
        dataIndex: 'dst_ip',
        valueType: 'text',
    },
    {
        title: '端口',
        dataIndex: 'dst_port',
        valueType: 'text',
    },
    {
        title: '源IP',
        dataIndex: 'src_ip',
        valueType: 'text',
    },
    {
        title: '源端口',
        dataIndex: 'src_port',
        valueType: 'text',
    },
    {
        title: '协议',
        dataIndex: 'protocol',
        valueType: 'text',
        hideInSearch: true,
        valueEnum: protocolEnum,
    },

]
export const cloudCommonTimeColumns: ProColumns<any>[] = [
    ...startTimeColumns,
    ...endTimeColumns,
    ...remarkColumns,
    // ...createTimeColumns,
    // ...updateTimeColumns,

]

