import { ProColumns } from "@ant-design/pro-components";
import { createTimeColumns, remarkColumns, searchColumns, updateTimeColumns } from "./Comon";
export const userOperationLogColumns: ProColumns<API.UserOperationLog>[] = [
    ...searchColumns,
    {
        title: '用户',
        dataIndex: 'username',
        valueType: 'text',
    },
    {
        title: 'HTTP方法',
        dataIndex: 'method',
        valueType: 'text',
    },
    {
        title: '项目',
        dataIndex: 'project',
        valueType: 'text',
    },
    {
        title: 'URI',
        dataIndex: 'uri',
        valueType: 'text',
        // ellipsis: true,
    },
    {
        title: '请求body',
        dataIndex: 'request_body',
        valueType: 'text',
        ellipsis: true,
    },
    {
        title: 'request_id',
        dataIndex: 'request_id',
        valueType: 'text',
        hideInTable: true,
    },
    ...createTimeColumns,
    ...remarkColumns,
]
