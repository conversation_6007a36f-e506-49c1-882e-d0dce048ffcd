import ProDescriptionsLayout from '@/layouts/extend/ProDescriptionsLayout';
import ShowBitString from '@/layouts/show/ShowBitString';
import {
  ISPTypes,
  alertColorMap,
  alertStatusEnum,
  alertStatusEnumMap,
  timeIsNotNull,
} from '@/utils/util';
import { ProColumns } from '@ant-design/pro-components';
import { Select, Tag } from 'antd';
import { searchColumns, projectColumns, remarkColumns, startEndTimeColumns } from './Comon';
import { protectGroupColumns } from './ProtectGroup';
import { wofangColumns } from './Wofang';
import ShowArrayProCardLayout from '@/layouts/show/ArrayProCardLayout';

export const attackInfoColumns: ProColumns<API.AttackInfo>[] = [
  {
    title: 'bps',
    dataIndex: 'maxBps',
    valueType: 'text',
    render: (_, record) => <ShowBitString data={record.maxBps} />,
  },
  {
    title: '清洗bps',
    dataIndex: 'maxCleanBps',
    valueType: 'text',
    render: (_, record) => <ShowBitString data={record.maxCleanBps} />,
  },
  {
    title: 'pps',
    dataIndex: 'maxPps',
    valueType: 'text',
    render: (_, record) => <ShowBitString data={record.maxPps} />,
  },
  {
    title: '清洗pps',
    dataIndex: 'maxCleanPps',
    valueType: 'text',
    render: (_, record) => <ShowBitString data={record.maxCleanPps} />,
  },
];

export const edgeColumns: ProColumns<API.SpectrumAlertEdges>[] = [
  {
    title: '联通沃防',
    dataIndex: 'wofang_ticket',
    valueType: 'text',
    render: (_, record) => (
      <ProDescriptionsLayout data={record.wofang_ticket} columns={wofangColumns} column={3} />
    ),
  },
  {
    title: '防护群组',
    dataIndex: 'protect_group',
    valueType: 'text',
    render: (_, record) => (
      <ProDescriptionsLayout data={record.protect_group} columns={protectGroupColumns} column={3} />
    ),
  },
];

export const spectrumAlertColumns: ProColumns<API.SpectrumAlert>[] = [
  ...searchColumns,
  ...projectColumns,
  {
    title: '防护状态',
    key: 'protect_status',
    dataIndex: 'protect_status',
    hideInTable: true,
    hideInDescriptions: true,
    renderFormItem: ({ ...rest }) => {
      return (
        <Select
          mode="multiple"
          allowClear
          options={alertStatusEnum}
          placeholder="Please select"
          {...rest}
        />
      );
    },
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    valueType: 'text',
    sorter: true,
    render: (_, record) => {
      return (
        <>
          {record.ip}
          <ShowArrayProCardLayout data={[record.isp_code]} dataMap={ISPTypes} noCollapsible />
        </>
      );
    },
  },
  // {
  //     title: '防护状态',
  //     dataIndex: 'protect_status',
  //     valueType: 'select',
  //     valueEnum: alertStatusEnum,
  //     // hideInTable: true,
  // },
  {
    title: '防护状态',
    dataIndex: 'protect_status',
    valueType: 'textarea',
    // hideInTable: true,
    hideInSearch: true,
    render: (_, row) => {
      const groupName = row.edges?.protect_group?.group_name;
      if (groupName !== undefined) {
        return (
          <>
            <Tag>{groupName}</Tag>
            <ShowArrayProCardLayout
              data={row.protect_status}
              dataMap={alertStatusEnumMap}
              colorMap={alertColorMap}
              noCollapsible
            />
          </>
        );
      }
      return (
        <ShowArrayProCardLayout
          data={row.protect_status}
          dataMap={alertStatusEnumMap}
          colorMap={alertColorMap}
          noCollapsible
        />
      );
    },
  },
  // {
  //     title: '防护状态',
  //     hideInSearch: true,
  //     render: (_, row) => {
  //         let rNds, rWofang, result
  //         let dragColor = "orange", unDragColor = "orange"
  //         if (row.protect_group_id) {
  //             let dragInfo = row.edges?.protect_group?.drag_info
  //             if (dragInfo?.autoDrag === true) {
  //                 dragColor = "green"
  //             }
  //             if (dragInfo?.autoUnDrag === true) {
  //                 unDragColor = "green"
  //             }
  //             rNds = <>
  //                 <Tag color={dragColor}>NDS-自动牵引</Tag>
  //                 <Tag color={unDragColor}>NDS-自动回牵</Tag>
  //             </>
  //         }
  //         const wofang_ticket = row.edges?.wofang_ticket
  //         if (wofang_ticket) {
  //             let color, wofangStatus = "失败"
  //             if (wofang_ticket === "success") {
  //                 color = "green"
  //                 wofangStatus = "成功"
  //             } else {
  //                 color = "red"
  //             }
  //             rWofang = <Tag color={color}>联通沃防：{wofangStatus}</Tag>
  //         }
  //         if (rNds === undefined && rWofang === undefined) {
  //             result = (<Tag color="red">尚未防护</Tag>)
  //         } else {
  //             result = (<>{rNds} {rWofang}</>)
  //         }
  //         return (<>{result}</>)
  //     }
  // },
  {
    title: '类型',
    dataIndex: 'attack_type',
    render: (_, row) => row?.attack_type?.split(',').map((item) => <Tag key={item}>{item}</Tag>),
  },
  // {
  //     title: '告警bps',
  //     dataIndex: 'max_bps',
  //     valueType: 'text',
  //     hideInSearch: true,
  //     render: (_, record) => <ShowBitString data={record.max_bps} />
  // },
  // {
  //     title: '告警pps',
  //     dataIndex: 'max_pps',
  //     valueType: 'text',
  //     hideInSearch: true,
  //     render: (_, record) => <ShowBitString data={record.max_pps} />
  // },
  {
    title: '告警值',
    dataIndex: 'max_pps',
    valueType: 'text',
    hideInSearch: true,
    render: (_, record) => {
      return (
        <>
          bps:
          <ShowBitString data={record.max_bps} />
          <br />
          pps:
          <ShowBitString data={record.max_pps} />
        </>
      );
    },
  },
  {
    title: '峰值',
    dataIndex: 'attack_info',
    valueType: 'text',
    hideInSearch: true,
    width: 280,
    // hideInTable: true,
    render: (_, record) => {
      if (timeIsNotNull(record.end_time)) {
        return (
          <ProDescriptionsLayout data={record.attack_info} columns={attackInfoColumns} column={2} />
        );
      }
    },
  },
  // ...createTimeColumns,
  ...startEndTimeColumns,
  ...remarkColumns,
  {
    dataIndex: 'edges',
    valueType: 'text',
    hideInTable: true,
    hideInSearch: true,
    render: (_, record) => (
      <ProDescriptionsLayout data={record.edges} columns={edgeColumns} column={1} />
    ),
  },
];
