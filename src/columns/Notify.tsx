import TagLayout from '@/layouts/extend/TagLayout';
import ShowArrayProCardLayout from '@/layouts/show/ArrayProCardLayout';
import { ProColumns } from '@ant-design/pro-components';
import { Tag } from 'antd';
import { createTimeColumns, projectColumns, remarkColumns, searchColumns, updateTimeColumns } from './Comon';
export const notifyColumns: ProColumns<API.Notify>[] = [
  ...searchColumns,
  ...projectColumns,
  {
    title: '名称',
    dataIndex: 'name',
    valueType: 'text',
    hideInSearch: true,
    render: (_, row) => {
      if (row.system) {
        return (
          <>
            {row.name} <Tag>系统策略</Tag>
          </>
        );
      }
      return <> {row.name}</>;
    },
  },
  {
    title: '通知开关',
    dataIndex: 'enabled',
    valueType: 'text',
    hideInSearch: true,
    render: (_, row) => {
      const page = (<>
        <TagLayout
          prefixText="总开关"
          enabled={row.enabled}
        />
        <br />
        <TagLayout
          prefixText="泡泡通知"
          enabled={row.popo}
        />
        <br />
        <TagLayout
          prefixText="邮件通知"
          enabled={row.email}
        />
        <br />
        <TagLayout
          prefixText="短信通知"
          enabled={row.sms}
        />
        <br />
        <TagLayout
          prefixText="电话通知"
          enabled={row.phone}
        />
      </>)
      if (row.system) {
        return (page)
      } else {
        return (
          <>
            {page}
            <br />
            <TagLayout
              prefixText="通过邮件通知项目SA列表"
              enabled={row.sa_notify_email}
            />
            <br />
            <TagLayout
              prefixText="通过泡泡通知项目SA列表"
              enabled={row.sa_notify_popo}
            />
          </>
        )
      }


    },
  },
  // {
  //   title: '开启popo通知',
  //   dataIndex: 'popo',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  // },
  // {
  //   title: '开启邮件通知',
  //   dataIndex: 'email',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  //   hideInTable: true,
  //   hideInSearch: true,
  // },
  // {
  //   title: '开启短信通知',
  //   dataIndex: 'sms',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  //   hideInSearch: true,
  //   hideInTable: true,
  // },
  // {
  //   title: '开启电话通知',
  //   dataIndex: 'phone',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  //   hideInSearch: true,
  //   hideInTable: true,
  // },

  // {
  //   title: 'SA通知开关',
  //   dataIndex: 'saNotifyEmail',
  //   valueType: 'text',
  //   valueEnum: valueEnum,
  //   hideInSearch: true,
  //   render: (_, row) => {
  //     return (
  //       <>
  //         <TagLayout
  //           prefixText="通过邮件通知项目SA列表"
  //           enabled={row.saNotifyEmail}
  //         />
  //         <br />
  //         <TagLayout
  //           prefixText="通过泡泡通知项目SA列表"
  //           enabled={row.saNotifyPopo}
  //         />
  //       </>
  //     )
  //   },
  // },

  {
    title: '邮件列表',
    dataIndex: 'emails',
    valueType: 'textarea',
    hideInTable: true,
    hideInSearch: true,
    render: (_, row) => <ShowArrayProCardLayout data={row.emails} title="可折叠" />,
  },

  {
    title: '电话列表',
    dataIndex: 'phones',
    valueType: 'textarea',
    hideInTable: true,
    hideInSearch: true,
    render: (_, row) => <ShowArrayProCardLayout data={row.phones} title="可折叠" />,
  },

  {
    title: 'popo群列表',
    dataIndex: 'popo_groups',
    valueType: 'textarea',
    hideInTable: true,
    hideInSearch: true,
    render: (_, row) => <ShowArrayProCardLayout data={row.popo_groups} title="可折叠" />,
  },

  // {
  //   title: '通过popo通知SA',
  //   dataIndex: 'saNotifyPopo',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  //   hideInSearch: true,
  //   hideInTable: true,
  // },

  // {
  //   title: '系统策略',
  //   dataIndex: 'system',
  //   valueType: 'select',
  //   valueEnum: valueEnum,
  //   hideInSearch: true,
  // },
  ...createTimeColumns,
  ...updateTimeColumns,
  ...remarkColumns,
];
