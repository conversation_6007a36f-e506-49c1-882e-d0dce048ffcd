import ShowBitString from "@/layouts/show/ShowBitString";
import { defenceLevelEnum, defenceModeEnum } from "@/utils/util";
import { ProColumns } from "@ant-design/pro-components";
import { cloudCommonColumns, cloudCommonTimeColumns } from "./CloudCommon";
export const cloudAlertColumns: ProColumns<API.CloudAlert>[] = [
    ...cloudCommonColumns,
    {
        title: '最大pps',
        dataIndex: 'max_pps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.max_pps} />
    },
    {
        title: '最大攻击pps',
        dataIndex: 'max_attack_pps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.max_attack_pps} />
    },
    {
        title: '防护水平',
        dataIndex: 'defence_level',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        valueEnum: defenceLevelEnum,
    },
    {
        title: '清洗模式',
        dataIndex: 'defence_mode',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        valueEnum: defenceModeEnum,
    },
    {
        title: '流匹配模式',
        dataIndex: 'flow_mode',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },

    {
        title: '超出阈值的报文总数',
        dataIndex: 'overlimit_pkt_count',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'TCP报文中的ACK值',
        dataIndex: 'tcp_ack_num',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'TCP报文中的SEQ值',
        dataIndex: 'tcp_seq_num',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    ...cloudCommonTimeColumns,
]
