import ShowBitString from "@/layouts/show/ShowBitString";
import { Bit2Str } from "@/utils/util";
import { ProColumns } from "@ant-design/pro-components";
import { createTimeColumns, projectColumns, singelTimeColumns } from "./Comon";
export const cleanDataColumns: ProColumns<API.CleanData>[] = [
    ...projectColumns,
    {
        title: 'IP',
        dataIndex: 'ip',
        valueType: 'text',
    },
    {
        title: 'in_bps',
        dataIndex: 'in_bps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.in_bps} />
    },
    {
        title: 'out_bps',
        dataIndex: 'out_bps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.out_bps} />
    },
    {
        title: 'in_pps',
        dataIndex: 'in_pps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.in_pps} />
    },
    {
        title: 'out_pps',
        dataIndex: 'out_pps',
        valueType: 'text',
        hideInSearch: true,
        sorter: true,
        render: (_, record) => <ShowBitString data={record.out_pps} />
    },
    {
        title: 'attack_flags',
        dataIndex: 'attack_flags',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'c_filter',
        dataIndex: 'c_filter',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'c_filter_id',
        dataIndex: 'c_filter_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'count',
        dataIndex: 'count',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'host',
        dataIndex: 'host',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'in_ack_bps',
        dataIndex: 'in_ack_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_ack_bps} />
    },
    {
        title: 'in_ack_pps',
        dataIndex: 'in_ack_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_ack_pps} />
    },

    {
        title: 'in_dns_bps',
        dataIndex: 'in_dns_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_dns_bps} />
    },
    {
        title: 'in_dns_pps',
        dataIndex: 'in_dns_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_dns_pps} />
    },
    {
        title: 'in_icmp_bps',
        dataIndex: 'in_icmp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_icmp_bps} />
    },
    {
        title: 'in_icmp_pps',
        dataIndex: 'in_icmp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_icmp_pps} />
    },

    {
        title: 'in_syn_pps',
        dataIndex: 'in_syn_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_syn_pps} />
    },
    {
        title: 'in_udp_bps',
        dataIndex: 'in_udp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_udp_bps} />
    },
    {
        title: 'in_udp_pps',
        dataIndex: 'in_udp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.in_udp_pps} />
    },

    {
        title: 'ip_type',
        dataIndex: 'ip_type',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    {
        title: 'out_ack_bps',
        dataIndex: 'out_ack_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_ack_bps} />
    },
    {
        title: 'out_ack_pps',
        dataIndex: 'out_ack_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_ack_pps} />
    },

    {
        title: 'out_dns_bps',
        dataIndex: 'out_dns_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_dns_bps} />
    },
    {
        title: 'out_dns_pps',
        dataIndex: 'out_dns_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_dns_pps} />
    },
    {
        title: 'out_icmp_bps',
        dataIndex: 'out_icmp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_icmp_bps} />
    },
    {
        title: 'out_icmp_pps',
        dataIndex: 'out_icmp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_icmp_pps} />
    },
    {
        title: 'out_syn_pps',
        dataIndex: 'out_syn_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_syn_pps} />
    },
    {
        title: 'out_udp_bps',
        dataIndex: 'out_udp_bps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_udp_bps} />
    },
    {
        title: 'out_udp_pps',
        dataIndex: 'out_udp_pps',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.out_udp_pps} />
    },
    {
        title: 'spectrum_alert_id',
        dataIndex: 'spectrum_alert_id',
        valueType: 'text',
        hideInTable: true,
        hideInSearch: true,
    },
    ...singelTimeColumns,
    ...createTimeColumns,
]
