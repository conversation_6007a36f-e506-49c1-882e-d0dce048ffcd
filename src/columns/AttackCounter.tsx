import ShowBitString from "@/layouts/show/ShowBitString";
import { ProColumns } from "@ant-design/pro-components";
export const attackCounterColumns: ProColumns<API.AttackCounter>[] = [
    {
        title: 'Name',
        dataIndex: 'Name',
        valueType: 'text',
        hideInSearch: true,
    },
    {
        title: 'Average',
        dataIndex: 'Average',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.Average} />
    },
    {
        title: 'Max',
        dataIndex: 'Max',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.Max} />
    },
    {
        title: 'N',
        dataIndex: 'N',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.N} />
    },

    {
        title: 'Sum',
        dataIndex: 'Sum',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.Sum} />
    },
    {
        title: 'Unit',
        dataIndex: 'Unit',
        valueType: 'text',
        hideInSearch: true,
    },
]
