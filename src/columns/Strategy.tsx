import ShowBitString from '@/layouts/show/ShowBitString';
import { ispEnum, protectEnum, protectEnum2, valueEnum } from '@/utils/util';
import { ProColumns } from '@ant-design/pro-components';
import { Tag } from 'antd';
import { projectColumns, remarkColumns, searchColumns, timeColumns } from './Comon';

export const strategyColumns: ProColumns<API.Strategy>[] = [
  ...searchColumns,
  ...projectColumns,
  {
    title: '名称',
    dataIndex: 'name',
    valueType: 'text',
    hideInSearch: true,
    render: (_, row) => {
      if (row.system) {
        return (
          <>
            {row.name} <Tag>系统策略</Tag>
          </>
        );
      }
      return <> {row.name}</>;
    },
  },
  {
    title: '启用',
    dataIndex: 'enabled',
    valueType: 'select',
    valueEnum: valueEnum,
    hideInSearch: true,
  },
  {
    title: 'IP类型',
    dataIndex: 'isp_code',
    valueType: 'select',
    valueEnum: ispEnum,
    hideInSearch: true,
  },
  {
    title: '防护类型',
    dataIndex: 'type',
    valueType: 'select',
    valueEnum: protectEnum2,
    hideInSearch: true,
  },
  {
    title: 'bps阈值',
    dataIndex: 'bps',
    valueType: 'text',
    hideInSearch: true,
    render: (_, record) => <ShowBitString data={record.bps} />
  },
  // {
  //   title: 'bps次数',
  //   dataIndex: 'bps_count',
  //   valueType: 'text',
  //   hideInSearch: true,
  // },
  {
    title: 'pps阈值',
    dataIndex: 'pps',
    valueType: 'text',
    hideInSearch: true,
    render: (_, record) => <ShowBitString data={record.pps} />
  },
  // {
  //   title: 'pps次数',
  //   dataIndex: 'pps_count',
  //   valueType: 'text',
  //   hideInSearch: true,
  // },
  ...timeColumns,
  ...remarkColumns,
];
