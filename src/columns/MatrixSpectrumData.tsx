import { ProColumns } from "@ant-design/pro-components";
import { createTimeColumns, matrixCommonColumns, projectColumns, searchColumns } from "./Comon";
import { singelTimeColumns } from "./Comon";
import ShowBitString from "@/layouts/show/ShowBitString";
export const matrixSpectrumDataColumns: ProColumns<API.MatrixSpectrumData>[] = [
    ...searchColumns,
    ...matrixCommonColumns,
    {
        title: 'bps',
        dataIndex: 'bps',
        valueType: 'text',
        hideInSearch: true,
        render: (_, record) => <ShowBitString data={record.bps} />
    },
    // {
    //     title: 'pps',
    //     dataIndex: 'pps',
    //     valueType: 'text',
    //     hideInSearch: true,
    //     render: (_, record) => <ShowBitString data={record.pps} />
    // },
    // {
    //     title: 'ratio',
    //     dataIndex: 'ratio',
    //     valueType: 'text',
    //     hideInSearch: true,
    //     render: (_, record) => {
    //         if (record.ratio) {
    //             return <>
    //                 {record.ratio * 100}%
    //             </>
    //         }
    //         return <>-</>
    //     }
    // },
    ...singelTimeColumns,
    ...createTimeColumns,
]
