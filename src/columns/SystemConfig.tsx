import { ProColumns } from "@ant-design/pro-components";
import { createTimeColumns, remarkColumns, searchColumns, updateTimeColumns } from "./Comon";
import ShowArrayProCardLayout from "@/layouts/show/ArrayProCardLayout";
export const systemConfigColumns: ProColumns<API.SystemConfig>[] = [
    ...searchColumns,
    {
        title: '沃防牵引接口测试IP',
        dataIndex: 'wofang_test_ip',
        valueType: 'text',
    },
    {
        title: '通过电话和POPO通知的场景',
        dataIndex: 'notify_scenes',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.notify_scenes} noCollapsible />
    },
    {
        title: '电话列表',
        dataIndex: 'notify_phones',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.notify_phones} noCollapsible />
    },
    {
        title: 'POPO',
        dataIndex: 'notify_emails',
        valueType: 'textarea',
        render: (_, row) => <ShowArrayProCardLayout data={row.notify_emails} noCollapsible />
    },
    ...createTimeColumns,
    ...updateTimeColumns,
    ...remarkColumns,

]
