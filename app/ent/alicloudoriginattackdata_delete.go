// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataDelete is the builder for deleting a AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataDelete struct {
	config
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// Where appends a list predicates to the AliCloudOriginAttackDataDelete builder.
func (acoadd *AliCloudOriginAttackDataDelete) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataDelete {
	acoadd.mutation.Where(ps...)
	return acoadd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (acoadd *AliCloudOriginAttackDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, acoadd.sqlExec, acoadd.mutation, acoadd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadd *AliCloudOriginAttackDataDelete) ExecX(ctx context.Context) int {
	n, err := acoadd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (acoadd *AliCloudOriginAttackDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(alicloudoriginattackdata.Table, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	if ps := acoadd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, acoadd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	acoadd.mutation.done = true
	return affected, err
}

// AliCloudOriginAttackDataDeleteOne is the builder for deleting a single AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataDeleteOne struct {
	acoadd *AliCloudOriginAttackDataDelete
}

// Where appends a list predicates to the AliCloudOriginAttackDataDelete builder.
func (acoaddo *AliCloudOriginAttackDataDeleteOne) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataDeleteOne {
	acoaddo.acoadd.mutation.Where(ps...)
	return acoaddo
}

// Exec executes the deletion query.
func (acoaddo *AliCloudOriginAttackDataDeleteOne) Exec(ctx context.Context) error {
	n, err := acoaddo.acoadd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{alicloudoriginattackdata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (acoaddo *AliCloudOriginAttackDataDeleteOne) ExecX(ctx context.Context) {
	if err := acoaddo.Exec(ctx); err != nil {
		panic(err)
	}
}
