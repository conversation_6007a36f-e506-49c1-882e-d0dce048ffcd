// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// AliCloudOriginAttackData is the model entity for the AliCloudOriginAttackData schema.
type AliCloudOriginAttackData struct {
	config
	// ID of the ent.
	ID           int `json:"id,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AliCloudOriginAttackData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case alicloudoriginattackdata.FieldID:
			values[i] = new(sql.NullInt64)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AliCloudOriginAttackData fields.
func (acoad *AliCloudOriginAttackData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case alicloudoriginattackdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			acoad.ID = int(value.Int64)
		default:
			acoad.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AliCloudOriginAttackData.
// This includes values selected through modifiers, order, etc.
func (acoad *AliCloudOriginAttackData) Value(name string) (ent.Value, error) {
	return acoad.selectValues.Get(name)
}

// Update returns a builder for updating this AliCloudOriginAttackData.
// Note that you need to call AliCloudOriginAttackData.Unwrap() before calling this method if this AliCloudOriginAttackData
// was returned from a transaction, and the transaction was committed or rolled back.
func (acoad *AliCloudOriginAttackData) Update() *AliCloudOriginAttackDataUpdateOne {
	return NewAliCloudOriginAttackDataClient(acoad.config).UpdateOne(acoad)
}

// Unwrap unwraps the AliCloudOriginAttackData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (acoad *AliCloudOriginAttackData) Unwrap() *AliCloudOriginAttackData {
	_tx, ok := acoad.config.driver.(*txDriver)
	if !ok {
		panic("ent: AliCloudOriginAttackData is not a transactional entity")
	}
	acoad.config.driver = _tx.drv
	return acoad
}

// String implements the fmt.Stringer.
func (acoad *AliCloudOriginAttackData) String() string {
	var builder strings.Builder
	builder.WriteString("AliCloudOriginAttackData(")
	builder.WriteString(fmt.Sprintf("id=%v", acoad.ID))
	builder.WriteByte(')')
	return builder.String()
}

// AliCloudOriginAttackDataSlice is a parsable slice of AliCloudOriginAttackData.
type AliCloudOriginAttackDataSlice []*AliCloudOriginAttackData
