// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataUpdate is the builder for updating AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataUpdate struct {
	config
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.Where(ps...)
	return acoadu
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Mutation() *AliCloudOriginAttackDataMutation {
	return acoadu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (acoadu *AliCloudOriginAttackDataUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, acoadu.sqlSave, acoadu.mutation, acoadu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) SaveX(ctx context.Context) int {
	affected, err := acoadu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (acoadu *AliCloudOriginAttackDataUpdate) Exec(ctx context.Context) error {
	_, err := acoadu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) ExecX(ctx context.Context) {
	if err := acoadu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (acoadu *AliCloudOriginAttackDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	if ps := acoadu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if n, err = sqlgraph.UpdateNodes(ctx, acoadu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	acoadu.mutation.done = true
	return n, nil
}

// AliCloudOriginAttackDataUpdateOne is the builder for updating a single AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Mutation() *AliCloudOriginAttackDataMutation {
	return acoaduo.mutation
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.Where(ps...)
	return acoaduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Select(field string, fields ...string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.fields = append([]string{field}, fields...)
	return acoaduo
}

// Save executes the query and returns the updated AliCloudOriginAttackData entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Save(ctx context.Context) (*AliCloudOriginAttackData, error) {
	return withHooks(ctx, acoaduo.sqlSave, acoaduo.mutation, acoaduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SaveX(ctx context.Context) *AliCloudOriginAttackData {
	node, err := acoaduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Exec(ctx context.Context) error {
	_, err := acoaduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ExecX(ctx context.Context) {
	if err := acoaduo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (acoaduo *AliCloudOriginAttackDataUpdateOne) sqlSave(ctx context.Context) (_node *AliCloudOriginAttackData, err error) {
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	id, ok := acoaduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AliCloudOriginAttackData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := acoaduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, alicloudoriginattackdata.FieldID)
		for _, f := range fields {
			if !alicloudoriginattackdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != alicloudoriginattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := acoaduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	_node = &AliCloudOriginAttackData{config: acoaduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, acoaduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	acoaduo.mutation.done = true
	return _node, nil
}
