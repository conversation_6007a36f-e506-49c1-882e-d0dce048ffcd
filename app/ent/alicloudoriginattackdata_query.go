// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataQuery is the builder for querying AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataQuery struct {
	config
	ctx        *QueryContext
	order      []alicloudoriginattackdata.OrderOption
	inters     []Interceptor
	predicates []predicate.AliCloudOriginAttackData
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AliCloudOriginAttackDataQuery builder.
func (acoadq *AliCloudOriginAttackDataQuery) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataQuery {
	acoadq.predicates = append(acoadq.predicates, ps...)
	return acoadq
}

// Limit the number of records to be returned by this query.
func (acoadq *AliCloudOriginAttackDataQuery) Limit(limit int) *AliCloudOriginAttackDataQuery {
	acoadq.ctx.Limit = &limit
	return acoadq
}

// Offset to start from.
func (acoadq *AliCloudOriginAttackDataQuery) Offset(offset int) *AliCloudOriginAttackDataQuery {
	acoadq.ctx.Offset = &offset
	return acoadq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (acoadq *AliCloudOriginAttackDataQuery) Unique(unique bool) *AliCloudOriginAttackDataQuery {
	acoadq.ctx.Unique = &unique
	return acoadq
}

// Order specifies how the records should be ordered.
func (acoadq *AliCloudOriginAttackDataQuery) Order(o ...alicloudoriginattackdata.OrderOption) *AliCloudOriginAttackDataQuery {
	acoadq.order = append(acoadq.order, o...)
	return acoadq
}

// First returns the first AliCloudOriginAttackData entity from the query.
// Returns a *NotFoundError when no AliCloudOriginAttackData was found.
func (acoadq *AliCloudOriginAttackDataQuery) First(ctx context.Context) (*AliCloudOriginAttackData, error) {
	nodes, err := acoadq.Limit(1).All(setContextOp(ctx, acoadq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{alicloudoriginattackdata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) FirstX(ctx context.Context) *AliCloudOriginAttackData {
	node, err := acoadq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AliCloudOriginAttackData ID from the query.
// Returns a *NotFoundError when no AliCloudOriginAttackData ID was found.
func (acoadq *AliCloudOriginAttackDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = acoadq.Limit(1).IDs(setContextOp(ctx, acoadq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{alicloudoriginattackdata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) FirstIDX(ctx context.Context) int {
	id, err := acoadq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AliCloudOriginAttackData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AliCloudOriginAttackData entity is found.
// Returns a *NotFoundError when no AliCloudOriginAttackData entities are found.
func (acoadq *AliCloudOriginAttackDataQuery) Only(ctx context.Context) (*AliCloudOriginAttackData, error) {
	nodes, err := acoadq.Limit(2).All(setContextOp(ctx, acoadq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{alicloudoriginattackdata.Label}
	default:
		return nil, &NotSingularError{alicloudoriginattackdata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) OnlyX(ctx context.Context) *AliCloudOriginAttackData {
	node, err := acoadq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AliCloudOriginAttackData ID in the query.
// Returns a *NotSingularError when more than one AliCloudOriginAttackData ID is found.
// Returns a *NotFoundError when no entities are found.
func (acoadq *AliCloudOriginAttackDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = acoadq.Limit(2).IDs(setContextOp(ctx, acoadq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{alicloudoriginattackdata.Label}
	default:
		err = &NotSingularError{alicloudoriginattackdata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := acoadq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AliCloudOriginAttackDataSlice.
func (acoadq *AliCloudOriginAttackDataQuery) All(ctx context.Context) ([]*AliCloudOriginAttackData, error) {
	ctx = setContextOp(ctx, acoadq.ctx, "All")
	if err := acoadq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AliCloudOriginAttackData, *AliCloudOriginAttackDataQuery]()
	return withInterceptors[[]*AliCloudOriginAttackData](ctx, acoadq, qr, acoadq.inters)
}

// AllX is like All, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) AllX(ctx context.Context) []*AliCloudOriginAttackData {
	nodes, err := acoadq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AliCloudOriginAttackData IDs.
func (acoadq *AliCloudOriginAttackDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if acoadq.ctx.Unique == nil && acoadq.path != nil {
		acoadq.Unique(true)
	}
	ctx = setContextOp(ctx, acoadq.ctx, "IDs")
	if err = acoadq.Select(alicloudoriginattackdata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) IDsX(ctx context.Context) []int {
	ids, err := acoadq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (acoadq *AliCloudOriginAttackDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, acoadq.ctx, "Count")
	if err := acoadq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, acoadq, querierCount[*AliCloudOriginAttackDataQuery](), acoadq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) CountX(ctx context.Context) int {
	count, err := acoadq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (acoadq *AliCloudOriginAttackDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, acoadq.ctx, "Exist")
	switch _, err := acoadq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (acoadq *AliCloudOriginAttackDataQuery) ExistX(ctx context.Context) bool {
	exist, err := acoadq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AliCloudOriginAttackDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (acoadq *AliCloudOriginAttackDataQuery) Clone() *AliCloudOriginAttackDataQuery {
	if acoadq == nil {
		return nil
	}
	return &AliCloudOriginAttackDataQuery{
		config:     acoadq.config,
		ctx:        acoadq.ctx.Clone(),
		order:      append([]alicloudoriginattackdata.OrderOption{}, acoadq.order...),
		inters:     append([]Interceptor{}, acoadq.inters...),
		predicates: append([]predicate.AliCloudOriginAttackData{}, acoadq.predicates...),
		// clone intermediate query.
		sql:  acoadq.sql.Clone(),
		path: acoadq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
func (acoadq *AliCloudOriginAttackDataQuery) GroupBy(field string, fields ...string) *AliCloudOriginAttackDataGroupBy {
	acoadq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AliCloudOriginAttackDataGroupBy{build: acoadq}
	grbuild.flds = &acoadq.ctx.Fields
	grbuild.label = alicloudoriginattackdata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
func (acoadq *AliCloudOriginAttackDataQuery) Select(fields ...string) *AliCloudOriginAttackDataSelect {
	acoadq.ctx.Fields = append(acoadq.ctx.Fields, fields...)
	sbuild := &AliCloudOriginAttackDataSelect{AliCloudOriginAttackDataQuery: acoadq}
	sbuild.label = alicloudoriginattackdata.Label
	sbuild.flds, sbuild.scan = &acoadq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AliCloudOriginAttackDataSelect configured with the given aggregations.
func (acoadq *AliCloudOriginAttackDataQuery) Aggregate(fns ...AggregateFunc) *AliCloudOriginAttackDataSelect {
	return acoadq.Select().Aggregate(fns...)
}

func (acoadq *AliCloudOriginAttackDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range acoadq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, acoadq); err != nil {
				return err
			}
		}
	}
	for _, f := range acoadq.ctx.Fields {
		if !alicloudoriginattackdata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if acoadq.path != nil {
		prev, err := acoadq.path(ctx)
		if err != nil {
			return err
		}
		acoadq.sql = prev
	}
	return nil
}

func (acoadq *AliCloudOriginAttackDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AliCloudOriginAttackData, error) {
	var (
		nodes = []*AliCloudOriginAttackData{}
		_spec = acoadq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AliCloudOriginAttackData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AliCloudOriginAttackData{config: acoadq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, acoadq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (acoadq *AliCloudOriginAttackDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := acoadq.querySpec()
	_spec.Node.Columns = acoadq.ctx.Fields
	if len(acoadq.ctx.Fields) > 0 {
		_spec.Unique = acoadq.ctx.Unique != nil && *acoadq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, acoadq.driver, _spec)
}

func (acoadq *AliCloudOriginAttackDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	_spec.From = acoadq.sql
	if unique := acoadq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if acoadq.path != nil {
		_spec.Unique = true
	}
	if fields := acoadq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, alicloudoriginattackdata.FieldID)
		for i := range fields {
			if fields[i] != alicloudoriginattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := acoadq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := acoadq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := acoadq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := acoadq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (acoadq *AliCloudOriginAttackDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(acoadq.driver.Dialect())
	t1 := builder.Table(alicloudoriginattackdata.Table)
	columns := acoadq.ctx.Fields
	if len(columns) == 0 {
		columns = alicloudoriginattackdata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if acoadq.sql != nil {
		selector = acoadq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if acoadq.ctx.Unique != nil && *acoadq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range acoadq.predicates {
		p(selector)
	}
	for _, p := range acoadq.order {
		p(selector)
	}
	if offset := acoadq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := acoadq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// AliCloudOriginAttackDataGroupBy is the group-by builder for AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataGroupBy struct {
	selector
	build *AliCloudOriginAttackDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (acoadgb *AliCloudOriginAttackDataGroupBy) Aggregate(fns ...AggregateFunc) *AliCloudOriginAttackDataGroupBy {
	acoadgb.fns = append(acoadgb.fns, fns...)
	return acoadgb
}

// Scan applies the selector query and scans the result into the given value.
func (acoadgb *AliCloudOriginAttackDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acoadgb.build.ctx, "GroupBy")
	if err := acoadgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AliCloudOriginAttackDataQuery, *AliCloudOriginAttackDataGroupBy](ctx, acoadgb.build, acoadgb, acoadgb.build.inters, v)
}

func (acoadgb *AliCloudOriginAttackDataGroupBy) sqlScan(ctx context.Context, root *AliCloudOriginAttackDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(acoadgb.fns))
	for _, fn := range acoadgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*acoadgb.flds)+len(acoadgb.fns))
		for _, f := range *acoadgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*acoadgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acoadgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AliCloudOriginAttackDataSelect is the builder for selecting fields of AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataSelect struct {
	*AliCloudOriginAttackDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (acoads *AliCloudOriginAttackDataSelect) Aggregate(fns ...AggregateFunc) *AliCloudOriginAttackDataSelect {
	acoads.fns = append(acoads.fns, fns...)
	return acoads
}

// Scan applies the selector query and scans the result into the given value.
func (acoads *AliCloudOriginAttackDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acoads.ctx, "Select")
	if err := acoads.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AliCloudOriginAttackDataQuery, *AliCloudOriginAttackDataSelect](ctx, acoads.AliCloudOriginAttackDataQuery, acoads, acoads.inters, v)
}

func (acoads *AliCloudOriginAttackDataSelect) sqlScan(ctx context.Context, root *AliCloudOriginAttackDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(acoads.fns))
	for _, fn := range acoads.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*acoads.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acoads.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
