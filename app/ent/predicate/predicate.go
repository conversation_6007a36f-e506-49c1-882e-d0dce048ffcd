// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AliCloudOriginAttackData is the predicate function for alicloudoriginattackdata builders.
type AliCloudOriginAttackData func(*sql.Selector)

// CasbinRule is the predicate function for casbinrule builders.
type CasbinRule func(*sql.Selector)

// CleanData is the predicate function for cleandata builders.
type CleanData func(*sql.Selector)

// CloudAlert is the predicate function for cloudalert builders.
type CloudAlert func(*sql.Selector)

// CloudAttackData is the predicate function for cloudattackdata builders.
type CloudAttackData func(*sql.Selector)

// CloudFlowData is the predicate function for cloudflowdata builders.
type CloudFlowData func(*sql.Selector)

// DataSync is the predicate function for datasync builders.
type DataSync func(*sql.Selector)

// Group is the predicate function for group builders.
type Group func(*sql.Selector)

// MatrixSpectrumAlert is the predicate function for matrixspectrumalert builders.
type MatrixSpectrumAlert func(*sql.Selector)

// MatrixSpectrumData is the predicate function for matrixspectrumdata builders.
type MatrixSpectrumData func(*sql.Selector)

// MatrixStrategy is the predicate function for matrixstrategy builders.
type MatrixStrategy func(*sql.Selector)

// Notify is the predicate function for notify builders.
type Notify func(*sql.Selector)

// ProtectGroup is the predicate function for protectgroup builders.
type ProtectGroup func(*sql.Selector)

// SkylineDos is the predicate function for skylinedos builders.
type SkylineDos func(*sql.Selector)

// SocGroupTicket is the predicate function for socgroupticket builders.
type SocGroupTicket func(*sql.Selector)

// SpectrumAlert is the predicate function for spectrumalert builders.
type SpectrumAlert func(*sql.Selector)

// SpectrumData is the predicate function for spectrumdata builders.
type SpectrumData func(*sql.Selector)

// Strategy is the predicate function for strategy builders.
type Strategy func(*sql.Selector)

// SystemApi is the predicate function for systemapi builders.
type SystemApi func(*sql.Selector)

// SystemConfig is the predicate function for systemconfig builders.
type SystemConfig func(*sql.Selector)

// Tenant is the predicate function for tenant builders.
type Tenant func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// UserOperationLog is the predicate function for useroperationlog builders.
type UserOperationLog func(*sql.Selector)

// Wofang is the predicate function for wofang builders.
type Wofang func(*sql.Selector)

// WofangAlert is the predicate function for wofangalert builders.
type WofangAlert func(*sql.Selector)
