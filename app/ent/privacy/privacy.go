// Code generated by ent, DO NOT EDIT.

package privacy

import (
	"context"
	"meta/app/ent"

	"entgo.io/ent/entql"
	"entgo.io/ent/privacy"
)

var (
	// Allow may be returned by rules to indicate that the policy
	// evaluation should terminate with allow decision.
	Allow = privacy.Allow

	// Deny may be returned by rules to indicate that the policy
	// evaluation should terminate with deny decision.
	Deny = privacy.Deny

	// Skip may be returned by rules to indicate that the policy
	// evaluation should continue to the next rule.
	Skip = privacy.Skip
)

// Allowf returns a formatted wrapped Allow decision.
func Allowf(format string, a ...any) error {
	return privacy.Allowf(format, a...)
}

// Denyf returns a formatted wrapped Deny decision.
func Denyf(format string, a ...any) error {
	return privacy.Denyf(format, a...)
}

// Skipf returns a formatted wrapped Skip decision.
func Skipf(format string, a ...any) error {
	return privacy.Skipf(format, a...)
}

// DecisionContext creates a new context from the given parent context with
// a policy decision attach to it.
func DecisionContext(parent context.Context, decision error) context.Context {
	return privacy.DecisionContext(parent, decision)
}

// DecisionFromContext retrieves the policy decision from the context.
func DecisionFromContext(ctx context.Context) (error, bool) {
	return privacy.DecisionFromContext(ctx)
}

type (
	// Policy groups query and mutation policies.
	Policy = privacy.Policy

	// QueryRule defines the interface deciding whether a
	// query is allowed and optionally modify it.
	QueryRule = privacy.QueryRule
	// QueryPolicy combines multiple query rules into a single policy.
	QueryPolicy = privacy.QueryPolicy

	// MutationRule defines the interface which decides whether a
	// mutation is allowed and optionally modifies it.
	MutationRule = privacy.MutationRule
	// MutationPolicy combines multiple mutation rules into a single policy.
	MutationPolicy = privacy.MutationPolicy
	// MutationRuleFunc type is an adapter which allows the use of
	// ordinary functions as mutation rules.
	MutationRuleFunc = privacy.MutationRuleFunc

	// QueryMutationRule is an interface which groups query and mutation rules.
	QueryMutationRule = privacy.QueryMutationRule
)

// QueryRuleFunc type is an adapter to allow the use of
// ordinary functions as query rules.
type QueryRuleFunc func(context.Context, ent.Query) error

// Eval returns f(ctx, q).
func (f QueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	return f(ctx, q)
}

// AlwaysAllowRule returns a rule that returns an allow decision.
func AlwaysAllowRule() QueryMutationRule {
	return privacy.AlwaysAllowRule()
}

// AlwaysDenyRule returns a rule that returns a deny decision.
func AlwaysDenyRule() QueryMutationRule {
	return privacy.AlwaysDenyRule()
}

// ContextQueryMutationRule creates a query/mutation rule from a context eval func.
func ContextQueryMutationRule(eval func(context.Context) error) QueryMutationRule {
	return privacy.ContextQueryMutationRule(eval)
}

// OnMutationOperation evaluates the given rule only on a given mutation operation.
func OnMutationOperation(rule MutationRule, op ent.Op) MutationRule {
	return privacy.OnMutationOperation(rule, op)
}

// DenyMutationOperationRule returns a rule denying specified mutation operation.
func DenyMutationOperationRule(op ent.Op) MutationRule {
	rule := MutationRuleFunc(func(_ context.Context, m ent.Mutation) error {
		return Denyf("ent/privacy: operation %s is not allowed", m.Op())
	})
	return OnMutationOperation(rule, op)
}

// The AliCloudOriginAttackDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AliCloudOriginAttackDataQueryRuleFunc func(context.Context, *ent.AliCloudOriginAttackDataQuery) error

// EvalQuery return f(ctx, q).
func (f AliCloudOriginAttackDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AliCloudOriginAttackDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AliCloudOriginAttackDataQuery", q)
}

// The AliCloudOriginAttackDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AliCloudOriginAttackDataMutationRuleFunc func(context.Context, *ent.AliCloudOriginAttackDataMutation) error

// EvalMutation calls f(ctx, m).
func (f AliCloudOriginAttackDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AliCloudOriginAttackDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AliCloudOriginAttackDataMutation", m)
}

// The CasbinRuleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type CasbinRuleQueryRuleFunc func(context.Context, *ent.CasbinRuleQuery) error

// EvalQuery return f(ctx, q).
func (f CasbinRuleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CasbinRuleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.CasbinRuleQuery", q)
}

// The CasbinRuleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type CasbinRuleMutationRuleFunc func(context.Context, *ent.CasbinRuleMutation) error

// EvalMutation calls f(ctx, m).
func (f CasbinRuleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.CasbinRuleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.CasbinRuleMutation", m)
}

// The CleanDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type CleanDataQueryRuleFunc func(context.Context, *ent.CleanDataQuery) error

// EvalQuery return f(ctx, q).
func (f CleanDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CleanDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.CleanDataQuery", q)
}

// The CleanDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type CleanDataMutationRuleFunc func(context.Context, *ent.CleanDataMutation) error

// EvalMutation calls f(ctx, m).
func (f CleanDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.CleanDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.CleanDataMutation", m)
}

// The CloudAlertQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type CloudAlertQueryRuleFunc func(context.Context, *ent.CloudAlertQuery) error

// EvalQuery return f(ctx, q).
func (f CloudAlertQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CloudAlertQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.CloudAlertQuery", q)
}

// The CloudAlertMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type CloudAlertMutationRuleFunc func(context.Context, *ent.CloudAlertMutation) error

// EvalMutation calls f(ctx, m).
func (f CloudAlertMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.CloudAlertMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.CloudAlertMutation", m)
}

// The CloudAttackDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type CloudAttackDataQueryRuleFunc func(context.Context, *ent.CloudAttackDataQuery) error

// EvalQuery return f(ctx, q).
func (f CloudAttackDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CloudAttackDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.CloudAttackDataQuery", q)
}

// The CloudAttackDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type CloudAttackDataMutationRuleFunc func(context.Context, *ent.CloudAttackDataMutation) error

// EvalMutation calls f(ctx, m).
func (f CloudAttackDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.CloudAttackDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.CloudAttackDataMutation", m)
}

// The CloudFlowDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type CloudFlowDataQueryRuleFunc func(context.Context, *ent.CloudFlowDataQuery) error

// EvalQuery return f(ctx, q).
func (f CloudFlowDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CloudFlowDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.CloudFlowDataQuery", q)
}

// The CloudFlowDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type CloudFlowDataMutationRuleFunc func(context.Context, *ent.CloudFlowDataMutation) error

// EvalMutation calls f(ctx, m).
func (f CloudFlowDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.CloudFlowDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.CloudFlowDataMutation", m)
}

// The DataSyncQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type DataSyncQueryRuleFunc func(context.Context, *ent.DataSyncQuery) error

// EvalQuery return f(ctx, q).
func (f DataSyncQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DataSyncQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.DataSyncQuery", q)
}

// The DataSyncMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type DataSyncMutationRuleFunc func(context.Context, *ent.DataSyncMutation) error

// EvalMutation calls f(ctx, m).
func (f DataSyncMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.DataSyncMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.DataSyncMutation", m)
}

// The GroupQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type GroupQueryRuleFunc func(context.Context, *ent.GroupQuery) error

// EvalQuery return f(ctx, q).
func (f GroupQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.GroupQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.GroupQuery", q)
}

// The GroupMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type GroupMutationRuleFunc func(context.Context, *ent.GroupMutation) error

// EvalMutation calls f(ctx, m).
func (f GroupMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.GroupMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.GroupMutation", m)
}

// The MatrixSpectrumAlertQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type MatrixSpectrumAlertQueryRuleFunc func(context.Context, *ent.MatrixSpectrumAlertQuery) error

// EvalQuery return f(ctx, q).
func (f MatrixSpectrumAlertQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MatrixSpectrumAlertQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.MatrixSpectrumAlertQuery", q)
}

// The MatrixSpectrumAlertMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type MatrixSpectrumAlertMutationRuleFunc func(context.Context, *ent.MatrixSpectrumAlertMutation) error

// EvalMutation calls f(ctx, m).
func (f MatrixSpectrumAlertMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.MatrixSpectrumAlertMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.MatrixSpectrumAlertMutation", m)
}

// The MatrixSpectrumDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type MatrixSpectrumDataQueryRuleFunc func(context.Context, *ent.MatrixSpectrumDataQuery) error

// EvalQuery return f(ctx, q).
func (f MatrixSpectrumDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MatrixSpectrumDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.MatrixSpectrumDataQuery", q)
}

// The MatrixSpectrumDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type MatrixSpectrumDataMutationRuleFunc func(context.Context, *ent.MatrixSpectrumDataMutation) error

// EvalMutation calls f(ctx, m).
func (f MatrixSpectrumDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.MatrixSpectrumDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.MatrixSpectrumDataMutation", m)
}

// The MatrixStrategyQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type MatrixStrategyQueryRuleFunc func(context.Context, *ent.MatrixStrategyQuery) error

// EvalQuery return f(ctx, q).
func (f MatrixStrategyQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MatrixStrategyQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.MatrixStrategyQuery", q)
}

// The MatrixStrategyMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type MatrixStrategyMutationRuleFunc func(context.Context, *ent.MatrixStrategyMutation) error

// EvalMutation calls f(ctx, m).
func (f MatrixStrategyMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.MatrixStrategyMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.MatrixStrategyMutation", m)
}

// The NotifyQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type NotifyQueryRuleFunc func(context.Context, *ent.NotifyQuery) error

// EvalQuery return f(ctx, q).
func (f NotifyQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NotifyQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.NotifyQuery", q)
}

// The NotifyMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type NotifyMutationRuleFunc func(context.Context, *ent.NotifyMutation) error

// EvalMutation calls f(ctx, m).
func (f NotifyMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.NotifyMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.NotifyMutation", m)
}

// The ProtectGroupQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type ProtectGroupQueryRuleFunc func(context.Context, *ent.ProtectGroupQuery) error

// EvalQuery return f(ctx, q).
func (f ProtectGroupQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ProtectGroupQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.ProtectGroupQuery", q)
}

// The ProtectGroupMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type ProtectGroupMutationRuleFunc func(context.Context, *ent.ProtectGroupMutation) error

// EvalMutation calls f(ctx, m).
func (f ProtectGroupMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.ProtectGroupMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.ProtectGroupMutation", m)
}

// The SkylineDosQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SkylineDosQueryRuleFunc func(context.Context, *ent.SkylineDosQuery) error

// EvalQuery return f(ctx, q).
func (f SkylineDosQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SkylineDosQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SkylineDosQuery", q)
}

// The SkylineDosMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SkylineDosMutationRuleFunc func(context.Context, *ent.SkylineDosMutation) error

// EvalMutation calls f(ctx, m).
func (f SkylineDosMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SkylineDosMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SkylineDosMutation", m)
}

// The SocGroupTicketQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SocGroupTicketQueryRuleFunc func(context.Context, *ent.SocGroupTicketQuery) error

// EvalQuery return f(ctx, q).
func (f SocGroupTicketQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SocGroupTicketQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SocGroupTicketQuery", q)
}

// The SocGroupTicketMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SocGroupTicketMutationRuleFunc func(context.Context, *ent.SocGroupTicketMutation) error

// EvalMutation calls f(ctx, m).
func (f SocGroupTicketMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SocGroupTicketMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SocGroupTicketMutation", m)
}

// The SpectrumAlertQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SpectrumAlertQueryRuleFunc func(context.Context, *ent.SpectrumAlertQuery) error

// EvalQuery return f(ctx, q).
func (f SpectrumAlertQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SpectrumAlertQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SpectrumAlertQuery", q)
}

// The SpectrumAlertMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SpectrumAlertMutationRuleFunc func(context.Context, *ent.SpectrumAlertMutation) error

// EvalMutation calls f(ctx, m).
func (f SpectrumAlertMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SpectrumAlertMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SpectrumAlertMutation", m)
}

// The SpectrumDataQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SpectrumDataQueryRuleFunc func(context.Context, *ent.SpectrumDataQuery) error

// EvalQuery return f(ctx, q).
func (f SpectrumDataQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SpectrumDataQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SpectrumDataQuery", q)
}

// The SpectrumDataMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SpectrumDataMutationRuleFunc func(context.Context, *ent.SpectrumDataMutation) error

// EvalMutation calls f(ctx, m).
func (f SpectrumDataMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SpectrumDataMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SpectrumDataMutation", m)
}

// The StrategyQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type StrategyQueryRuleFunc func(context.Context, *ent.StrategyQuery) error

// EvalQuery return f(ctx, q).
func (f StrategyQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.StrategyQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.StrategyQuery", q)
}

// The StrategyMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type StrategyMutationRuleFunc func(context.Context, *ent.StrategyMutation) error

// EvalMutation calls f(ctx, m).
func (f StrategyMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.StrategyMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.StrategyMutation", m)
}

// The SystemApiQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SystemApiQueryRuleFunc func(context.Context, *ent.SystemApiQuery) error

// EvalQuery return f(ctx, q).
func (f SystemApiQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SystemApiQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SystemApiQuery", q)
}

// The SystemApiMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SystemApiMutationRuleFunc func(context.Context, *ent.SystemApiMutation) error

// EvalMutation calls f(ctx, m).
func (f SystemApiMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SystemApiMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SystemApiMutation", m)
}

// The SystemConfigQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type SystemConfigQueryRuleFunc func(context.Context, *ent.SystemConfigQuery) error

// EvalQuery return f(ctx, q).
func (f SystemConfigQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SystemConfigQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.SystemConfigQuery", q)
}

// The SystemConfigMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type SystemConfigMutationRuleFunc func(context.Context, *ent.SystemConfigMutation) error

// EvalMutation calls f(ctx, m).
func (f SystemConfigMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.SystemConfigMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.SystemConfigMutation", m)
}

// The TenantQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type TenantQueryRuleFunc func(context.Context, *ent.TenantQuery) error

// EvalQuery return f(ctx, q).
func (f TenantQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TenantQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.TenantQuery", q)
}

// The TenantMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type TenantMutationRuleFunc func(context.Context, *ent.TenantMutation) error

// EvalMutation calls f(ctx, m).
func (f TenantMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.TenantMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.TenantMutation", m)
}

// The UserQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type UserQueryRuleFunc func(context.Context, *ent.UserQuery) error

// EvalQuery return f(ctx, q).
func (f UserQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.UserQuery", q)
}

// The UserMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type UserMutationRuleFunc func(context.Context, *ent.UserMutation) error

// EvalMutation calls f(ctx, m).
func (f UserMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.UserMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.UserMutation", m)
}

// The UserOperationLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type UserOperationLogQueryRuleFunc func(context.Context, *ent.UserOperationLogQuery) error

// EvalQuery return f(ctx, q).
func (f UserOperationLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserOperationLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.UserOperationLogQuery", q)
}

// The UserOperationLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type UserOperationLogMutationRuleFunc func(context.Context, *ent.UserOperationLogMutation) error

// EvalMutation calls f(ctx, m).
func (f UserOperationLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.UserOperationLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.UserOperationLogMutation", m)
}

// The WofangQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WofangQueryRuleFunc func(context.Context, *ent.WofangQuery) error

// EvalQuery return f(ctx, q).
func (f WofangQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WofangQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WofangQuery", q)
}

// The WofangMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WofangMutationRuleFunc func(context.Context, *ent.WofangMutation) error

// EvalMutation calls f(ctx, m).
func (f WofangMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WofangMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WofangMutation", m)
}

// The WofangAlertQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type WofangAlertQueryRuleFunc func(context.Context, *ent.WofangAlertQuery) error

// EvalQuery return f(ctx, q).
func (f WofangAlertQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.WofangAlertQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.WofangAlertQuery", q)
}

// The WofangAlertMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type WofangAlertMutationRuleFunc func(context.Context, *ent.WofangAlertMutation) error

// EvalMutation calls f(ctx, m).
func (f WofangAlertMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.WofangAlertMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.WofangAlertMutation", m)
}

type (
	// Filter is the interface that wraps the Where function
	// for filtering nodes in queries and mutations.
	Filter interface {
		// Where applies a filter on the executed query/mutation.
		Where(entql.P)
	}

	// The FilterFunc type is an adapter that allows the use of ordinary
	// functions as filters for query and mutation types.
	FilterFunc func(context.Context, Filter) error
)

// EvalQuery calls f(ctx, q) if the query implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	fr, err := queryFilter(q)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

// EvalMutation calls f(ctx, q) if the mutation implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	fr, err := mutationFilter(m)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

var _ QueryMutationRule = FilterFunc(nil)

func queryFilter(q ent.Query) (Filter, error) {
	switch q := q.(type) {
	case *ent.AliCloudOriginAttackDataQuery:
		return q.Filter(), nil
	case *ent.CasbinRuleQuery:
		return q.Filter(), nil
	case *ent.CleanDataQuery:
		return q.Filter(), nil
	case *ent.CloudAlertQuery:
		return q.Filter(), nil
	case *ent.CloudAttackDataQuery:
		return q.Filter(), nil
	case *ent.CloudFlowDataQuery:
		return q.Filter(), nil
	case *ent.DataSyncQuery:
		return q.Filter(), nil
	case *ent.GroupQuery:
		return q.Filter(), nil
	case *ent.MatrixSpectrumAlertQuery:
		return q.Filter(), nil
	case *ent.MatrixSpectrumDataQuery:
		return q.Filter(), nil
	case *ent.MatrixStrategyQuery:
		return q.Filter(), nil
	case *ent.NotifyQuery:
		return q.Filter(), nil
	case *ent.ProtectGroupQuery:
		return q.Filter(), nil
	case *ent.SkylineDosQuery:
		return q.Filter(), nil
	case *ent.SocGroupTicketQuery:
		return q.Filter(), nil
	case *ent.SpectrumAlertQuery:
		return q.Filter(), nil
	case *ent.SpectrumDataQuery:
		return q.Filter(), nil
	case *ent.StrategyQuery:
		return q.Filter(), nil
	case *ent.SystemApiQuery:
		return q.Filter(), nil
	case *ent.SystemConfigQuery:
		return q.Filter(), nil
	case *ent.TenantQuery:
		return q.Filter(), nil
	case *ent.UserQuery:
		return q.Filter(), nil
	case *ent.UserOperationLogQuery:
		return q.Filter(), nil
	case *ent.WofangQuery:
		return q.Filter(), nil
	case *ent.WofangAlertQuery:
		return q.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected query type %T for query filter", q)
	}
}

func mutationFilter(m ent.Mutation) (Filter, error) {
	switch m := m.(type) {
	case *ent.AliCloudOriginAttackDataMutation:
		return m.Filter(), nil
	case *ent.CasbinRuleMutation:
		return m.Filter(), nil
	case *ent.CleanDataMutation:
		return m.Filter(), nil
	case *ent.CloudAlertMutation:
		return m.Filter(), nil
	case *ent.CloudAttackDataMutation:
		return m.Filter(), nil
	case *ent.CloudFlowDataMutation:
		return m.Filter(), nil
	case *ent.DataSyncMutation:
		return m.Filter(), nil
	case *ent.GroupMutation:
		return m.Filter(), nil
	case *ent.MatrixSpectrumAlertMutation:
		return m.Filter(), nil
	case *ent.MatrixSpectrumDataMutation:
		return m.Filter(), nil
	case *ent.MatrixStrategyMutation:
		return m.Filter(), nil
	case *ent.NotifyMutation:
		return m.Filter(), nil
	case *ent.ProtectGroupMutation:
		return m.Filter(), nil
	case *ent.SkylineDosMutation:
		return m.Filter(), nil
	case *ent.SocGroupTicketMutation:
		return m.Filter(), nil
	case *ent.SpectrumAlertMutation:
		return m.Filter(), nil
	case *ent.SpectrumDataMutation:
		return m.Filter(), nil
	case *ent.StrategyMutation:
		return m.Filter(), nil
	case *ent.SystemApiMutation:
		return m.Filter(), nil
	case *ent.SystemConfigMutation:
		return m.Filter(), nil
	case *ent.TenantMutation:
		return m.Filter(), nil
	case *ent.UserMutation:
		return m.Filter(), nil
	case *ent.UserOperationLogMutation:
		return m.Filter(), nil
	case *ent.WofangMutation:
		return m.Filter(), nil
	case *ent.WofangAlertMutation:
		return m.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected mutation type %T for mutation filter", m)
	}
}
