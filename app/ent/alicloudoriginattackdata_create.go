// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataCreate is the builder for creating a AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataCreate struct {
	config
	mutation *AliCloudOriginAttackDataMutation
	hooks    []Hook
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoadc *AliCloudOriginAttackDataCreate) Mutation() *AliCloudOriginAttackDataMutation {
	return acoadc.mutation
}

// Save creates the AliCloudOriginAttackData in the database.
func (acoadc *AliCloudOriginAttackDataCreate) Save(ctx context.Context) (*AliCloudOriginAttackData, error) {
	return withHooks(ctx, acoadc.sqlSave, acoadc.mutation, acoadc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (acoadc *AliCloudOriginAttackDataCreate) SaveX(ctx context.Context) *AliCloudOriginAttackData {
	v, err := acoadc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acoadc *AliCloudOriginAttackDataCreate) Exec(ctx context.Context) error {
	_, err := acoadc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadc *AliCloudOriginAttackDataCreate) ExecX(ctx context.Context) {
	if err := acoadc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (acoadc *AliCloudOriginAttackDataCreate) check() error {
	return nil
}

func (acoadc *AliCloudOriginAttackDataCreate) sqlSave(ctx context.Context) (*AliCloudOriginAttackData, error) {
	if err := acoadc.check(); err != nil {
		return nil, err
	}
	_node, _spec := acoadc.createSpec()
	if err := sqlgraph.CreateNode(ctx, acoadc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	acoadc.mutation.id = &_node.ID
	acoadc.mutation.done = true
	return _node, nil
}

func (acoadc *AliCloudOriginAttackDataCreate) createSpec() (*AliCloudOriginAttackData, *sqlgraph.CreateSpec) {
	var (
		_node = &AliCloudOriginAttackData{config: acoadc.config}
		_spec = sqlgraph.NewCreateSpec(alicloudoriginattackdata.Table, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	)
	return _node, _spec
}

// AliCloudOriginAttackDataCreateBulk is the builder for creating many AliCloudOriginAttackData entities in bulk.
type AliCloudOriginAttackDataCreateBulk struct {
	config
	err      error
	builders []*AliCloudOriginAttackDataCreate
}

// Save creates the AliCloudOriginAttackData entities in the database.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) Save(ctx context.Context) ([]*AliCloudOriginAttackData, error) {
	if acoadcb.err != nil {
		return nil, acoadcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acoadcb.builders))
	nodes := make([]*AliCloudOriginAttackData, len(acoadcb.builders))
	mutators := make([]Mutator, len(acoadcb.builders))
	for i := range acoadcb.builders {
		func(i int, root context.Context) {
			builder := acoadcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AliCloudOriginAttackDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acoadcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acoadcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acoadcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) SaveX(ctx context.Context) []*AliCloudOriginAttackData {
	v, err := acoadcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) Exec(ctx context.Context) error {
	_, err := acoadcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) ExecX(ctx context.Context) {
	if err := acoadcb.Exec(ctx); err != nil {
		panic(err)
	}
}
