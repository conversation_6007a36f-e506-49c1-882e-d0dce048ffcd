package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackData holds the schema definition for the AliCloudOriginAttackData entity.
type AliCloudOriginAttackData struct {
	ent.Schema
}

// Mixin of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Fields() []ent.Field {
	return []ent.Field{
		field.String("project").Comment("IP 所属项目").StructTag(`query:"project,omitempty"`),
		field.String("cloud_type").Comment("云类型").StructTag(`query:"cloud_type,omitempty"`),
		field.String("ip").Comment("被攻击IP").StructTag(`query:"ip,omitempty"`),

		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("结束时间").Optional(),
		field.Time("duration").Comment("攻击持续时间").Optional(),
		field.String("ddos_type").Comment("攻击事件的类型").Optional(),
		field.Int64("pps").Comment("攻击开始时刻的报文数量大小").StructTag(`query:"pps,omitempty"`),
		field.Int64("mbps").Comment("攻击开始时刻的请求流量大小").StructTag(`query:"mbps,omitempty"`),
		field.String("status").Comment("攻击事件的当前状态"),

		field.Time("delay_time").Comment("延迟时间").Optional(),
		field.Time("blackhole_end_time").Comment("黑洞解除时间。只有 ddos_type 是 blackhole 才会返回该值").Optional(),
	}
}

// Edges of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Edges() []ent.Edge {
	return nil
}
