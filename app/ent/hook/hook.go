// Code generated by ent, DO NOT EDIT.

package hook

import (
	"context"
	"fmt"
	"meta/app/ent"
)

// The AliCloudOriginAttackDataFunc type is an adapter to allow the use of ordinary
// function as AliCloudOriginAttackData mutator.
type AliCloudOriginAttackDataFunc func(context.Context, *ent.AliCloudOriginAttackDataMutation) (ent.Value, error)

// Mu<PERSON> calls f(ctx, m).
func (f AliCloudOriginAttackDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AliCloudOriginAttackDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AliCloudOriginAttackDataMutation", m)
}

// The CasbinRuleFunc type is an adapter to allow the use of ordinary
// function as CasbinRule mutator.
type CasbinRuleFunc func(context.Context, *ent.CasbinRuleMutation) (ent.Value, error)

// Mu<PERSON> calls f(ctx, m).
func (f CasbinRuleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CasbinRuleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CasbinRuleMutation", m)
}

// The CleanDataFunc type is an adapter to allow the use of ordinary
// function as CleanData mutator.
type CleanDataFunc func(context.Context, *ent.CleanDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CleanDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CleanDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CleanDataMutation", m)
}

// The CloudAlertFunc type is an adapter to allow the use of ordinary
// function as CloudAlert mutator.
type CloudAlertFunc func(context.Context, *ent.CloudAlertMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CloudAlertFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CloudAlertMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CloudAlertMutation", m)
}

// The CloudAttackDataFunc type is an adapter to allow the use of ordinary
// function as CloudAttackData mutator.
type CloudAttackDataFunc func(context.Context, *ent.CloudAttackDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CloudAttackDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CloudAttackDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CloudAttackDataMutation", m)
}

// The CloudFlowDataFunc type is an adapter to allow the use of ordinary
// function as CloudFlowData mutator.
type CloudFlowDataFunc func(context.Context, *ent.CloudFlowDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CloudFlowDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CloudFlowDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CloudFlowDataMutation", m)
}

// The DataSyncFunc type is an adapter to allow the use of ordinary
// function as DataSync mutator.
type DataSyncFunc func(context.Context, *ent.DataSyncMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DataSyncFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DataSyncMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DataSyncMutation", m)
}

// The GroupFunc type is an adapter to allow the use of ordinary
// function as Group mutator.
type GroupFunc func(context.Context, *ent.GroupMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f GroupFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.GroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.GroupMutation", m)
}

// The MatrixSpectrumAlertFunc type is an adapter to allow the use of ordinary
// function as MatrixSpectrumAlert mutator.
type MatrixSpectrumAlertFunc func(context.Context, *ent.MatrixSpectrumAlertMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f MatrixSpectrumAlertFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.MatrixSpectrumAlertMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.MatrixSpectrumAlertMutation", m)
}

// The MatrixSpectrumDataFunc type is an adapter to allow the use of ordinary
// function as MatrixSpectrumData mutator.
type MatrixSpectrumDataFunc func(context.Context, *ent.MatrixSpectrumDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f MatrixSpectrumDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.MatrixSpectrumDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.MatrixSpectrumDataMutation", m)
}

// The MatrixStrategyFunc type is an adapter to allow the use of ordinary
// function as MatrixStrategy mutator.
type MatrixStrategyFunc func(context.Context, *ent.MatrixStrategyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f MatrixStrategyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.MatrixStrategyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.MatrixStrategyMutation", m)
}

// The NotifyFunc type is an adapter to allow the use of ordinary
// function as Notify mutator.
type NotifyFunc func(context.Context, *ent.NotifyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f NotifyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.NotifyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.NotifyMutation", m)
}

// The ProtectGroupFunc type is an adapter to allow the use of ordinary
// function as ProtectGroup mutator.
type ProtectGroupFunc func(context.Context, *ent.ProtectGroupMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ProtectGroupFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ProtectGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ProtectGroupMutation", m)
}

// The SkylineDosFunc type is an adapter to allow the use of ordinary
// function as SkylineDos mutator.
type SkylineDosFunc func(context.Context, *ent.SkylineDosMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SkylineDosFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SkylineDosMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SkylineDosMutation", m)
}

// The SocGroupTicketFunc type is an adapter to allow the use of ordinary
// function as SocGroupTicket mutator.
type SocGroupTicketFunc func(context.Context, *ent.SocGroupTicketMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SocGroupTicketFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SocGroupTicketMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SocGroupTicketMutation", m)
}

// The SpectrumAlertFunc type is an adapter to allow the use of ordinary
// function as SpectrumAlert mutator.
type SpectrumAlertFunc func(context.Context, *ent.SpectrumAlertMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SpectrumAlertFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SpectrumAlertMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SpectrumAlertMutation", m)
}

// The SpectrumDataFunc type is an adapter to allow the use of ordinary
// function as SpectrumData mutator.
type SpectrumDataFunc func(context.Context, *ent.SpectrumDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SpectrumDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SpectrumDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SpectrumDataMutation", m)
}

// The StrategyFunc type is an adapter to allow the use of ordinary
// function as Strategy mutator.
type StrategyFunc func(context.Context, *ent.StrategyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f StrategyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.StrategyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.StrategyMutation", m)
}

// The SystemApiFunc type is an adapter to allow the use of ordinary
// function as SystemApi mutator.
type SystemApiFunc func(context.Context, *ent.SystemApiMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SystemApiFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SystemApiMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SystemApiMutation", m)
}

// The SystemConfigFunc type is an adapter to allow the use of ordinary
// function as SystemConfig mutator.
type SystemConfigFunc func(context.Context, *ent.SystemConfigMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SystemConfigFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SystemConfigMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SystemConfigMutation", m)
}

// The TenantFunc type is an adapter to allow the use of ordinary
// function as Tenant mutator.
type TenantFunc func(context.Context, *ent.TenantMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TenantFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TenantMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TenantMutation", m)
}

// The UserFunc type is an adapter to allow the use of ordinary
// function as User mutator.
type UserFunc func(context.Context, *ent.UserMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f UserFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.UserMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.UserMutation", m)
}

// The UserOperationLogFunc type is an adapter to allow the use of ordinary
// function as UserOperationLog mutator.
type UserOperationLogFunc func(context.Context, *ent.UserOperationLogMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f UserOperationLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.UserOperationLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.UserOperationLogMutation", m)
}

// The WofangFunc type is an adapter to allow the use of ordinary
// function as Wofang mutator.
type WofangFunc func(context.Context, *ent.WofangMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WofangFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WofangMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WofangMutation", m)
}

// The WofangAlertFunc type is an adapter to allow the use of ordinary
// function as WofangAlert mutator.
type WofangAlertFunc func(context.Context, *ent.WofangAlertMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f WofangAlertFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.WofangAlertMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.WofangAlertMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, ent.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op ent.Op) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk ent.Hook, cond Condition) ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, ent.Delete|ent.Create)
func On(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, ent.Update|ent.UpdateOne)
func Unless(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) ent.Hook {
	return func(ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(context.Context, ent.Mutation) (ent.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []ent.Hook {
//		return []ent.Hook{
//			Reject(ent.Delete|ent.Update),
//		}
//	}
func Reject(op ent.Op) ent.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []ent.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...ent.Hook) Chain {
	return Chain{append([]ent.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() ent.Hook {
	return func(mutator ent.Mutator) ent.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...ent.Hook) Chain {
	newHooks := make([]ent.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}
