package controller

import (
	"context"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type HelloUserController struct {
	HelloUserService *service.HelloUserService
	Logger           *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 HelloUser
//
//	@Description Query 根据指定字段、时间范围查询或搜索 HelloUser
//	@Summary Query 根据指定字段、时间范围查询或搜索 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param age query integer false "age"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.HelloUser}
//	@Router /api/v1/hellouser [get]
func (huc *HelloUserController) Query(c *fiber.Ctx) error {
	hu := &ent.HelloUser{}
	qp, err := common.QueryParser(c, hu)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := huc.HelloUserService.Query(ctx, hu, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 HelloUser
//
//	@Description QueryByID 根据 ID 查询 HelloUser
//	@Summary QueryByID 根据 ID 查询 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param id path int true "HelloUser ID"
//	@Success 200 {object} common.Result{data=ent.HelloUser}
//	@Router /api/v1/hellouser/{id} [get]
func (huc *HelloUserController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := huc.HelloUserService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 HelloUser
//
//	@Description Create 创建 HelloUser
//	@Summary Create 创建 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param hellouser body ent.HelloUser true "HelloUser"
//	@Success 200 {object} common.Result{data=ent.HelloUser}
//	@Router /api/v1/hellouser [post]
func (huc *HelloUserController) Create(c *fiber.Ctx) error {
	hu := &ent.HelloUser{Name: "unknown"}
	err := common.BodyParser(c, hu)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := huc.HelloUserService.Create(ctx, hu)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 HelloUser
//
//	@Description CreateBulk 批量创建 HelloUser
//	@Summary CreateBulk 批量创建 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param hellouser body []ent.HelloUser true "HelloUser"
//	@Success 200 {object} common.Result{data=[]ent.HelloUser}
//	@Router /api/v1/hellouser/bulk [post]
func (huc *HelloUserController) CreateBulk(c *fiber.Ctx) error {
	hu := make([]*ent.HelloUser, 10)
	err := common.RequestBodyParser(c, &hu)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := huc.HelloUserService.CreateBulk(ctx, hu)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 HelloUser
//
//	@Description UpdateByID 根据 ID 修改 HelloUser
//	@Summary UpdateByID 根据 ID 修改 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param id path int true "HelloUser ID"
//
// @Param hellouser body ent.HelloUser true "HelloUser"
//
//	@Success 200 {object} common.Result{data=ent.HelloUser}
//	@Router /api/v1/hellouser/{id} [put]
func (huc *HelloUserController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	hu, err := huc.HelloUserService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, hu)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := huc.HelloUserService.UpdateByID(ctx, hu, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 HelloUser
//
//	@Description DeleteByID 根据 ID 删除 HelloUser
//	@Summary DeleteByID 根据 ID 删除 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param id path int true "HelloUser ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/hellouser/{id} [delete]
func (huc *HelloUserController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := huc.HelloUserService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 HelloUser
//
//	@Description DeleteBulk 根据 IDs 批量删除 HelloUser
//	@Summary DeleteBulk 根据 IDs 批量删除 HelloUser
//	@Tags HelloUser
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/hellouser/bulk/delete [post]
func (huc *HelloUserController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = huc.HelloUserService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
