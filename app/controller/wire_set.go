package controller

import (
	"github.com/google/wire"
)

// Controller wire set
var (
	Set = wire.NewSet(GroupSet, NotifySet, NdsSet, CasbinRuleSet, CleanDataSet,
		ProtectGroupSet, SpectrumAlertSet, SpectrumDataSet, StrategySet, TenantSet, UserSet,
		WoFangApiSet, SocGroupSet, WoFangSet, SocGroupTicketSet, SystemApiSet, CloudAlertSet,
		WoFangApi2Set, WoFangAlertSet, CloudFlowDataSet, ProjectSet, SkylineDosSet, CloudAttackDataSet,
		MatrixStrategySet, MatrixSpectrumDataSet, MatrixSpectrumAlertSet, ChartSet, UserOperationLogSet,
		SystemConfigSet, DataSyncSet, AliCloudOriginAttackDataSet,
	)
	GroupSet                    = wire.NewSet(wire.Struct(new(GroupController), "*"))
	NotifySet                   = wire.NewSet(wire.Struct(new(NotifyController), "*"))
	NdsSet                      = wire.NewSet(wire.Struct(new(NdsController), "*"))
	CasbinRuleSet               = wire.NewSet(wire.Struct(new(CasbinRuleController), "*"))
	CleanDataSet                = wire.NewSet(wire.Struct(new(CleanDataController), "*"))
	ProtectGroupSet             = wire.NewSet(wire.Struct(new(ProtectGroupController), "*"))
	SpectrumAlertSet            = wire.NewSet(wire.Struct(new(SpectrumAlertController), "*"))
	SpectrumDataSet             = wire.NewSet(wire.Struct(new(SpectrumDataController), "*"))
	StrategySet                 = wire.NewSet(wire.Struct(new(StrategyController), "*"))
	TenantSet                   = wire.NewSet(wire.Struct(new(TenantController), "*"))
	UserSet                     = wire.NewSet(wire.Struct(new(UserController), "*"))
	WoFangApiSet                = wire.NewSet(wire.Struct(new(WoFangApi), "*"))
	WoFangApi2Set               = wire.NewSet(wire.Struct(new(WoFangApi2), "*"))
	SocGroupSet                 = wire.NewSet(wire.Struct(new(SocGroupController), "*"))
	WoFangSet                   = wire.NewSet(wire.Struct(new(WofangController), "*"))
	SocGroupTicketSet           = wire.NewSet(wire.Struct(new(SocGroupTicketController), "*"))
	SystemApiSet                = wire.NewSet(wire.Struct(new(SystemApiController), "*"))
	CloudAlertSet               = wire.NewSet(wire.Struct(new(CloudAlertController), "*"))
	CloudFlowDataSet            = wire.NewSet(wire.Struct(new(CloudFlowDataController), "*"))
	CloudAttackDataSet          = wire.NewSet(wire.Struct(new(CloudAttackDataController), "*"))
	WoFangAlertSet              = wire.NewSet(wire.Struct(new(WofangAlertController), "*"))
	ProjectSet                  = wire.NewSet(wire.Struct(new(ProjectController), "*"))
	SkylineDosSet               = wire.NewSet(wire.Struct(new(SkylineDosController), "*"))
	MatrixStrategySet           = wire.NewSet(wire.Struct(new(MatrixStrategyController), "*"))
	MatrixSpectrumDataSet       = wire.NewSet(wire.Struct(new(MatrixSpectrumDataController), "*"))
	MatrixSpectrumAlertSet      = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertController), "*"))
	ChartSet                    = wire.NewSet(wire.Struct(new(ChartController), "*"))
	UserOperationLogSet         = wire.NewSet(wire.Struct(new(UserOperationLogController), "*"))
	SystemConfigSet             = wire.NewSet(wire.Struct(new(SystemConfigController), "*"))
	DataSyncSet                 = wire.NewSet(wire.Struct(new(DataSyncController), "*"))
	AliCloudOriginAttackDataSet = wire.NewSet(wire.Struct(new(AliCloudOriginAttackDataController), "*"))
)
