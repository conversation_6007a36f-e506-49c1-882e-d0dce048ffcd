package service

import (
	"meta/app/ent"

	"github.com/google/wire"
)

// Dao 所有的Dao都是ent Client
type Dao = ent.Client

var (
	Set = wire.NewSet(
		ProtectGroupSet, SpectrumAlertSet, SpectrumDataSet, StrategySet, TenantSet, UserSet,
		CasbinRuleSet, CleanDataSet, GroupSet, NotifySet, WoFangSet, SocGroupTicketSet, SystemApiSet,
		CloudAlertSet, WoFangAlertSet, CloudFlowDataSet, SkylineDosSet, CloudAttackDataSet, MatrixStrategySet,
		MatrixSpectrumDataSet, MatrixSpectrumAlertSet, UserOperationLogSet, SystemConfigSet, DataSyncSet,
		AliCloudOriginAttackDataSet,
	)
	ProtectGroupSet             = wire.NewSet(wire.Struct(new(ProtectGroupService), "*"))
	SpectrumAlertSet            = wire.NewSet(wire.Struct(new(SpectrumAlertService), "*"))
	SpectrumDataSet             = wire.NewSet(wire.Struct(new(SpectrumDataService), "*"))
	StrategySet                 = wire.NewSet(wire.Struct(new(StrategyService), "*"))
	TenantSet                   = wire.NewSet(wire.Struct(new(TenantService), "*"))
	UserSet                     = wire.NewSet(wire.Struct(new(UserService), "*"))
	CasbinRuleSet               = wire.NewSet(wire.Struct(new(CasbinRuleService), "*"))
	CleanDataSet                = wire.NewSet(wire.Struct(new(CleanDataService), "*"))
	GroupSet                    = wire.NewSet(wire.Struct(new(GroupService), "*"))
	NotifySet                   = wire.NewSet(wire.Struct(new(NotifyService), "*"))
	WoFangSet                   = wire.NewSet(wire.Struct(new(WofangService), "*"))
	SocGroupTicketSet           = wire.NewSet(wire.Struct(new(SocGroupTicketService), "*"))
	SystemApiSet                = wire.NewSet(wire.Struct(new(SystemApiService), "*"))
	CloudAlertSet               = wire.NewSet(wire.Struct(new(CloudAlertService), "*"))
	CloudFlowDataSet            = wire.NewSet(wire.Struct(new(CloudFlowDataService), "*"))
	CloudAttackDataSet          = wire.NewSet(wire.Struct(new(CloudAttackDataService), "*"))
	WoFangAlertSet              = wire.NewSet(wire.Struct(new(WofangAlertService), "*"))
	SkylineDosSet               = wire.NewSet(wire.Struct(new(SkylineDosService), "*"))
	MatrixStrategySet           = wire.NewSet(wire.Struct(new(MatrixStrategyService), "*"))
	MatrixSpectrumDataSet       = wire.NewSet(wire.Struct(new(MatrixSpectrumDataService), "*"))
	MatrixSpectrumAlertSet      = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertService), "*"))
	UserOperationLogSet         = wire.NewSet(wire.Struct(new(UserOperationLogService), "*"))
	SystemConfigSet             = wire.NewSet(wire.Struct(new(SystemConfigService), "*"))
	DataSyncSet                 = wire.NewSet(wire.Struct(new(DataSyncService), "*"))
	AliCloudOriginAttackDataSet = wire.NewSet(wire.Struct(new(AliCloudOriginAttackDataService), "*"))
)
