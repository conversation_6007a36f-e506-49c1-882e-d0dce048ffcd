package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/entity"
)

type AliCloudOriginAttackDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) Query(ctx context.Context, acoad *ent.AliCloudOriginAttackData, qp *entity.QueryParam) (int, []*ent.AliCloudOriginAttackData, error) {
	if len(qp.Search) == 0 {
		return acoads.QueryPage(ctx, acoad, qp)
	} else {
		return acoads.QuerySearch(ctx, acoad, qp)
	}
}

// QueryByID 根据 ID 查询 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) QueryByID(ctx context.Context, id int) (*ent.AliCloudOriginAttackData, error) {
	return acoads.Dao.AliCloudOriginAttackData.Query().Where(alicloudoriginattackdata.ID(id)).Only(ctx)
}

// Create 创建 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) Create(ctx context.Context, acoad *ent.AliCloudOriginAttackData) (*ent.AliCloudOriginAttackData, error) {
	return acoads.Dao.AliCloudOriginAttackData.Create().SetItemAliCloudOriginAttackData(acoad).Save(ctx)
}

// CreateBulk 批量创建 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) CreateBulk(ctx context.Context, acoad []*ent.AliCloudOriginAttackData) ([]*ent.AliCloudOriginAttackData, error) {
	bulks := make([]*ent.AliCloudOriginAttackDataCreate, len(acoad))
	for i, v := range acoad {
		bulks[i] = acoads.Dao.AliCloudOriginAttackData.Create().SetItemAliCloudOriginAttackData(v)
	}
	return acoads.Dao.AliCloudOriginAttackData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) UpdateByID(ctx context.Context, acoad *ent.AliCloudOriginAttackData, id int) (*ent.AliCloudOriginAttackData, error) {
	return acoads.Dao.AliCloudOriginAttackData.UpdateOneID(id).SetItemAliCloudOriginAttackData(acoad).Save(ctx)
}

// DeleteByID 根据 ID 删除 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) DeleteByID(ctx context.Context, id int) error {
	return acoads.Dao.AliCloudOriginAttackData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := acoads.Dao.AliCloudOriginAttackData.Delete().Where(alicloudoriginattackdata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) QueryPage(ctx context.Context, acoad *ent.AliCloudOriginAttackData, qp *entity.QueryParam) (int, []*ent.AliCloudOriginAttackData, error) {
	count, err := acoads.Dao.AliCloudOriginAttackData.Query().QueryItemAliCloudOriginAttackData(acoad, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := acoads.Dao.AliCloudOriginAttackData.Query().QueryItemAliCloudOriginAttackData(acoad, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 AliCloudOriginAttackData
func (acoads *AliCloudOriginAttackDataService) QuerySearch(ctx context.Context, acoad *ent.AliCloudOriginAttackData, qp *entity.QueryParam) (int, []*ent.AliCloudOriginAttackData, error) {
	count, err := acoads.Dao.AliCloudOriginAttackData.Query().SearchAliCloudOriginAttackData(acoad, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := acoads.Dao.AliCloudOriginAttackData.Query().SearchAliCloudOriginAttackData(acoad, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
