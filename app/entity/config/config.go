/**
* <AUTHOR>
* @date 2022-06-27 22:15
* @description
 */

package config

import (
	"fmt"
	"log"
	"meta/app/entity/netease"
	"os"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/viper"
)

var CFG = &Config{}

// Config 配置文件结构体
type Config struct {
	Stage            Stage                    `json:"stage"`
	Fiber            Fiber                    `json:"fiber"`
	Auth             Auth                     `json:"auth"`
	Ent              Ent                      `json:"ent"`
	Cache            Cache                    `json:"cache"`
	Swagger          Swagger                  `json:"swagger"`
	Log              Log                      `json:"log"`
	XAuth            netease.XAuth            `json:"xAuth"`
	External         netease.External         `json:"external"`
	MatrixStrategies []netease.MatrixStrategy `json:"matrixStrategies"`
}
type Stage struct {
	Status     string `json:"status"`
	User       string `json:"user"`
	Password   string `json:"password"`
	Api        Api    `json:"api"`
	HttpSchema string `json:"httpSchema"`
	Domain     string `json:"domain"`
}
type Api struct {
	PublicGetPath []string `json:"publicGetPath,omitempty"`
	SaPathPrefix  []string `json:"saPathPrefix,omitempty"`
}
type Fiber struct {
	Host         string `json:"host"`
	Port         int16  `json:"port"`
	ReadTimeout  int    `json:"readTimeout"`
	JsonCoder    string `json:"jsonCoder"`
	TimeLocation string `json:"timeLocation"`
}

type Ent struct {
	AutoMigrate        bool   `json:"autoMigrate"`
	DebugMode          bool   `json:"debugMode"`
	WithDropIndex      bool   `json:"withDropIndex"`
	WithDropColumn     bool   `json:"withDropColumn"`
	WithGlobalUniqueID bool   `json:"withGlobalUniqueID"`
	Backend            string `json:"backend"`
	DB                 DB     `json:"DB"`
	ConnectionRetry    bool   `json:"connectionRetry"`
}
type Swagger struct {
	Enable bool `json:"enable"`
}

func LoadConfig(path string) error {
	// 1. 从环境变量获取配置文件路径
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		log.Fatal("CONFIG_PATH environment variable is not set")
	}

	// 2. 直接指定配置文件路径（无需设置 ConfigName 和 AddConfigPath）
	viper.SetConfigFile(configPath) // 完整路径，如 "/path/to/config_local.toml"

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatalf("read config failed: %v", err)
	}
	err = viper.Unmarshal(&CFG)
	return err
}

// NewConfig Fiber 配置
// https://docs.gofiber.io/api/fiber#config
func (f *Fiber) NewConfig() fiber.Config {
	config := fiber.Config{
		ReadTimeout:    time.Second * time.Duration(f.ReadTimeout),
		ReadBufferSize: 10240,
	}
	jsonCoder := f.JsonCoder
	if jsonCoder == "sonic" {
		config.JSONEncoder = sonic.Marshal
		config.JSONDecoder = sonic.Unmarshal
	}
	return config
}

func (f *Fiber) Url() string {
	return fmt.Sprintf("%s:%d", f.Host, f.Port)
}
