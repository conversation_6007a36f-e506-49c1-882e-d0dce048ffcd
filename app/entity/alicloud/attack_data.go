/**
* <AUTHOR>
* @date 2023-12-14 14:58
* @description AliCloud Origin Attack Data structures
 */

package alicloud

import "time"

// AliCloudOriginAttackDataMessage 阿里云原生DDoS攻击数据消息结构
type AliCloudOriginAttackDataMessage struct {
	Project    string    `json:"project"`     // IP 所属项目
	CloudType  string    `json:"cloud_type"`  // 云类型
	IP         string    `json:"ip"`          // 被攻击的 IP
	StartTime  time.Time `json:"start_time"`  // 攻击事件开始时间
	EndTime    *time.Time `json:"end_time"`   // 攻击事件结束时间
	Duration   *int      `json:"duration"`    // 攻击事件持续时间。 单位: s
	Pps        int64     `json:"pps"`         // 攻击开始时刻的报文数量大小。单位：pps
	Mbps       int64     `json:"mbps"`        // 攻击开始时刻的请求流量大小。单位：Mbps
	Status     string    `json:"status"`      // 攻击事件的当前状态
}

// KafkaMessage Kafka消息结构
type KafkaMessage struct {
	Data []AliCloudOriginAttackDataMessage `json:"data"`
	Type string                            `json:"type"`
}
