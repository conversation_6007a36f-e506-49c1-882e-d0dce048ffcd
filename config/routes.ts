/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'welcome',
    hideInMenu: true,
    icon: 'smile',
    component: './Welcome',
  },

  // {
  //   path: '/test',
  //   name: 'test',
  //   icon: 'smile',
  //   component: './Test',
  // },

  // {
  //   path: '/detail/:id',
  //   hideInMenu: true,
  //   name: 'detail',
  //   component: './Test/Detail',
  // },
  // {
  //   path: '/admin',
  //   name: 'admin',
  //   icon: 'table',
  //   access: 'canAdmin',
  //   routes: [
  //     {
  //       path: '/admin',
  //       redirect: '/admin/sub-page',
  //     },
  //     {
  //       path: '/admin/sub-page',
  //       name: 'sub-page',
  //       component: './Admin',
  //     },
  //   ],
  // },
  {
    path: '/dashboard',
    name: 'Dashboard',
    icon: 'DashboardOutlined',
    access: 'canAdmin',
    component: './Dashboard',
  },
  {
    path: '/attack',
    name: 'attack',
    icon: 'AlertOutlined',
    routes: [
      {
        path: '/attack/alert',
        name: 'NDS-alert',
        component: './Attack/SpectrumAlert',
      },
      {
        path: '/attack/cloud',
        name: 'netcloud-alert',
        icon: 'table',
        component: './Attack/CloudAlert',
      },
      {
        path: '/attack/wofang',
        name: 'wofang-alert',
        icon: 'table',
        component: './Attack/WofangAlert',
      },
      {
        path: '/attack/awsdos',
        name: 'AWS-Dos',
        icon: 'table',
        component: './Attack/SkylineDos',
      },
      {
        path: '/attack/matrix',
        name: 'matrix-alert',
        access: 'canAdmin',
        component: './Attack/MatrixSpectrumAlert',
      },
    ],
  },
  {
    path: '/process',
    name: 'process',
    icon: 'AimOutlined',
    routes: [
      {
        path: '/process/ticket',
        name: 'NDS',
        component: './SocGroupTicket',
      },
      {
        path: '/process/wofang',
        name: 'chinaunicom',
        component: './Wofang',
      },
    ],
  },
  {
    path: '/protect',
    name: 'protect',
    icon: 'HomeOutlined',
    routes: [
      {
        path: '/protect/group',
        name: 'protect-group',
        component: './ProtectGroup',
      },
      {
        path: '/protect/strategy',
        name: 'strategy',
        component: './Strategy',
      },
      {
        path: '/protect/matrix',
        name: 'matrix',
        access: 'canAdmin',
        component: './MatrixStrategy',
      },
    ],
  },
  {
    path: '/data',
    name: 'data',
    icon: 'BlockOutlined',
    routes: [
      {
        path: '/data/spectrum',
        name: 'NDS-spectrum',
        component: './Data/SpectrumData',
      },
      {
        path: '/data/clean',
        name: 'NDS-clean',
        component: './Data/CleanData',
      },
      {
        path: '/data/flow',
        name: 'netcloud-flow',
        component: './Data/CloudFlowData',
      },
      {
        path: '/data/attack',
        name: 'netcloud-attack',
        component: './Data/CloudAttackData',
      },
      {
        path: '/data/matrix',
        name: 'matrix-spectrum',
        access: 'canAdmin',
        component: './Data/MatrixSpectrumData',
      },
      {
        path: '/data/sync',
        name: 'data-sync',
        access: 'canAdmin',
        component: './Data/DataSync',
      },
    ],
  },
  {
    path: '/notify',
    name: 'notify',
    icon: 'NotificationOutlined',
    component: './Notify',
  },
  {
    path: '/notify/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Notify/Detail',
  },

  {
    path: '/setting',
    name: 'setting',
    icon: 'SettingOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/setting/api',
        name: 'api',
        component: './SystemApi',
      },
      {
        path: '/setting/rbac',
        name: 'rbac',
        component: './CasbinRule',
      },
      {
        path: '/setting/project',
        name: 'project',
        component: './Tenant',
      },
      {
        path: '/setting/user',
        name: 'user',
        component: './User',
      },
      {
        path: '/setting/config',
        name: 'config',
        component: './SystemConfig',
      },
    ],
  },

  {
    path: '/setting/project/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Tenant/Detail',
  },
  {
    path: '/setting/user/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './User/Detail',
  },


  {
    path: '/setting/rbac/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './CasbinRule/Detail',
  },

  {
    path: '/setting/api/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './SystemApi/Detail',
  },
  {
    path: '/process/wofang/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Wofang/Detail',
  },
  {
    path: '/setting/config/detail/:id',
    hideInMenu: true,
    name: 'Detail',
    component: './SystemConfig/Detail',
  },

  {
    path: '/process/ticket/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './SocGroupTicket/Detail',
  },
  {
    path: '/process/ticket/new',
    hideInMenu: true,
    name: 'new',
    access: 'canNew',
    component: './SocGroupTicket/AddTicket',
  },

  //详情
  //分光告警
  {
    path: '/attack/alert/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Attack/SpectrumAlert/Detail',
  },
  {
    path: '/attack/cloud/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Attack/CloudAlert/Detail',
  },
  {
    path: '/attack/wofang/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Attack/WofangAlert/Detail',
  },
  {
    path: '/attack/awsdos/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Attack/SkylineDos/Detail',
  },
  {
    path: '/attack/matrix/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Attack/MatrixSpectrumAlert/Detail',
  },
  //防护群组
  {
    path: '/protect/group/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './ProtectGroup/Detail',
  },
  //策略
  {
    path: '/protect/strategy/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Strategy/Detail',
  },
  {
    path: '/protect/matrix/detail/:id',
    hideInMenu: true,
    access: 'canAdmin',
    name: 'detail',
    component: './MatrixStrategy/Detail',
  },
  //分光数据
  {
    path: '/data/spectrum/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Data/SpectrumData/Detail',
  },
  //清洗数据
  {
    path: '/data/clean/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Data/CleanData/Detail',
  },
  //流数据
  {
    path: '/data/flow/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Data/CloudFlowData/Detail',
  },
  //云网络攻击pps数据
  {
    path: '/data/attack/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Data/CloudAttackData/Detail',
  },
  //机房出口分光数据
  {
    path: '/data/matrix/detail/:id',
    hideInMenu: true,
    name: 'detail',
    access: 'canAdmin',
    component: './Data/MatrixSpectrumData/Detail',
  },
  //数据同步
  {
    path: '/data/sync/detail/:id',
    hideInMenu: true,
    name: 'detail',
    component: './Data/DataSync/Detail',
  },
  //用户操作日志
  {
    path: '/opdata/log/detail/:id',
    hideInMenu: true,
    access: 'canAdmin',
    name: 'detail',
    component: './OPData/UserOperationLog/Detail',
  },

  //详情end


  //图表
  {
    path: '/chart',
    name: 'chart',
    icon: 'LineChartOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/chart/line',
        name: 'line',
        component: './Chart/Spectrum',
      },]
  },

  //操作数据
  {
    path: '/opdata',
    name: 'opdata',
    icon: 'DatabaseOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/opdata/log',
        name: 'log',
        component: './OPData/UserOperationLog',
      },]
  },


  {
    path: '/',
    redirect: '/attack/alert',

  },
  {
    path: '/HelloUser',
    name: 'HelloUser',
    icon: 'table',
    component: './HelloUser',
  },

  {
    path: '/HelloUser/detail/:id',
    hideInMenu: true,
    name: 'Detail',
    component: './HelloUser/Detail',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
