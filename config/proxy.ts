/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
//import { createProxyMiddleware } from 'http-proxy-middleware';
import { Application } from 'express';
import { createProxyMiddleware, Options } from 'http-proxy-middleware';
import { ClientRequest } from 'http';
import { Request, Response } from 'express';
export default {
  // 如果需要自定义本地开发服务器  请取消注释按需调整
  // dev: {
  //   // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
  //   '/api/': {
  //     // 要代理的地址
  //     target: 'https://preview.pro.ant.design',
  //     // 配置了这个可以从 http 代理到 https
  //     // 依赖 origin 的功能可能需要这个，比如 cookie
  //     changeOrigin: true,
  //   },
  // },

  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  // dev: (app: Application) => {
  //   app.use(
  //     '/api',
  //     createProxyMiddleware({
  //       target: 'http://127.0.0.1:9001',
  //       changeOrigin: true,
  //       pathRewrite: { '^': '' },
  //       onProxyReq: (proxyReq:ClientRequest, req:Request, res:Response) => {
  //         // Add custom headers
  //         proxyReq.setHeader('Authorization', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6Imd1b2thaWx1bjAxIiwiZXhwIjoxNzI5MjM5MDkzLCJuYmYiOjE3Mjg2MzQyOTMsImlhdCI6MTcyODYzNDI5M30.rePBLcfj86TUacBSH7M6z_WJxHb8zNaHoFLNUJ7HurQ');
  //       },
  //     } as Partial<Parameters<typeof createProxyMiddleware>[0]>)
  //   );
  // },
  dev: {  
    '/api/v1/': {
      target: 'http://127.0.0.1:9001',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      onProxyReq: (proxyReq:ClientRequest, req:Request, res:Response) => {
        // Add custom headers
        // proxyReq.setHeader('Authorization', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6Imd1b2thaWx1biIsImV4cCI6MTczMDczMjc5MiwibmJmIjoxNzMwMTI3OTkyLCJpYXQiOjE3MzAxMjc5OTJ9.ZPTop8u6cTyxNCurMDfWLkVBhz-DUsY4zqKpyR22Mkk');
      },
    },
  },
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
