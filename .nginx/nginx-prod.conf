server {
    listen 80;

    # logs
    #access_log /usr/share/nginx/logs/access.log;
    #error_log  /usr/share/nginx/logs/error.log;

    #keepalive_timeout  0;
    keepalive_timeout 60;

    #hide nginx version info and forbid scan the website's index
    server_tokens off;
    autoindex off;

    # gzip config
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    root /usr/share/nginx/html;

    location / {
        # 用于配合 browserHistory使用
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://*************:9001;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
    }

    resolver *******:53 *******:53 valid=180s ipv6=off;
    # 如果实在需要使用外网 dns(办公网场景) ，请使用：
    # resolver ************:53 valid=180s ipv6=off;
    resolver_timeout 10s;
    set $x_auth_domain "int.auth.nie.netease.com";
    set $x_scheme "http";

    #测试
    # resolver ************:53 valid=180s ipv6=off;
    # resolver_timeout 10s;
    # set $x_scheme "http";
    # set $x_auth_domain "auth.nie.netease.com";

    location /login {
        proxy_set_header Host $x_auth_domain;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $x_scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #proxy_cache_valid any 30s;
        proxy_pass http://$x_auth_domain$uri$is_args$args;
    }
    location /logout {
        proxy_set_header Host $x_auth_domain;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $x_scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #proxy_cache_valid any 30s;
        proxy_pass http://$x_auth_domain$uri$is_args$args;
    }
}
