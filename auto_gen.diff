diff --git a/api/test/casbin_rule_test.go b/api/test/casbin_rule_test.go
index 224cb8f..487e4c3 100644
--- a/api/test/casbin_rule_test.go
+++ b/api/test/casbin_rule_test.go
@@ -1,15 +1,15 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/casbinrule"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/casbinrule"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 )
 
 var casbinRuleApi = baseApi + "/casbinrule"
-var testCasbinRule = &ent.CasbinRule{Type: "phCErprzrh", Sub: "YGznbbPcHb", Dom: "fkEoFQVhED", Obj: "hOINpYDMTb", Act: "jytzkVSGhD", V4: "mIkBhLVrvL", V5: "YlcYGKjqfE"}
+var testCasbinRule = &ent.CasbinRule{Type: "ibAjIEeniF", Sub: "AUJCtPICRy", Dom: "wZJvekaWZX", Obj: "AjtzrTLbLc", Act: "XLZnAPsBNk", V4: "gWCSgWvutW", V5: "xcCiaSpdxG"}
 var casbinRuleIDs = []int{}
 
 // Create CasbinRule test case
@@ -24,11 +24,11 @@ func TestCreateCasbinRule(t *testing.T) {
 // CreateBulk CasbinRule test case
 // 批量创建
 func TestCreateBulkCasbinRule(t *testing.T) {
-	bulkData1 := &ent.CasbinRule{Type: "RmRwQyuaNH", Sub: "tKBKqxgkdO", Dom: "mCnFQKWCki", Obj: "IDBEyJAXus", Act: "pnVOCANmHt", V4: "PYOxsUnGBx", V5: "pQZuNLybwB"}
-	bulkData2 := &ent.CasbinRule{Type: "AyzVbexLzW", Sub: "eyJByHTMiG", Dom: "wDGoeHnhEG", Obj: "iyakzZVOle", Act: "ncBYVlNjow", V4: "ubJnZFbTBf", V5: "yUUpESPAOD"}
-	bulkData3 := &ent.CasbinRule{Type: "kAZUZATRpD", Sub: "LNwJPNEFzg", Dom: "ZssFZrkCUD", Obj: "rjJaiCzikS", Act: "KbZBvSQuNO", V4: "YdsUCeesaG", V5: "YtuXlRZKpb"}
-	bulkData4 := &ent.CasbinRule{Type: "tTYZLEuUAj", Sub: "HdclBzyeNo", Dom: "YTiLskkZQZ", Obj: "BtsXRLhRDu", Act: "kgTzXNTJCf", V4: "iqntCiKXpc", V5: "DGOiysoqgf"}
-	bulkData5 := &ent.CasbinRule{Type: "ZkzujaaSNF", Sub: "sNsnblojvg", Dom: "gkFnPnIfgf", Obj: "ZovBnXOVHk", Act: "OSswBcpQIO", V4: "zzFJSNndqi", V5: "dfLzleZNTI"}
+	bulkData1 := &ent.CasbinRule{Type: "VXJDMZywaN", Sub: "gesHyAZfDs", Dom: "ElbOMgzsgX", Obj: "rNpobEmcSA", Act: "uqRnbqtwuX", V4: "ZbuJynPcoI", V5: "VPRpHddhLp"}
+	bulkData2 := &ent.CasbinRule{Type: "pivIsKdWfw", Sub: "jpNuZjcZzV", Dom: "TzplCdsZcj", Obj: "SdyjrCylQg", Act: "CTWPCkCVrf", V4: "xlcMLqDoGO", V5: "brasloWgta"}
+	bulkData3 := &ent.CasbinRule{Type: "gimgZPcBob", Sub: "egdTBnGsHj", Dom: "KmOJIZRhvr", Obj: "UERKacWfTe", Act: "pnCtUUJFDI", V4: "PSwHyMTGbW", V5: "ZkpgUANbIr"}
+	bulkData4 := &ent.CasbinRule{Type: "UWMZvYkySa", Sub: "AkqZrdYAEg", Dom: "OeoRgZgxTv", Obj: "ZRxwCfndzY", Act: "SzTSWIGTeO", V4: "DsqYVmLbFe", V5: "PvUruDmGsv"}
+	bulkData5 := &ent.CasbinRule{Type: "EWHJkDAKbt", Sub: "GGWnupNKHI", Dom: "gDdAbMXYMc", Obj: "jQcRelQQxt", Act: "irXjOxEIwE", V4: "mhbqraeRGb", V5: "FItlAhBwMU"}
 	bulkDatas := [...]ent.CasbinRule{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Type
 	testCase := &CaseRule{Api: casbinRuleApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -63,7 +63,7 @@ func TestQueryByIDCasbinRuleNotExist(t *testing.T) {
 	runTest(t, testCase)
 }
 
-// Query CasbinRule by NetType test case
+// Query CasbinRule by Type test case
 // 根据指定字段、时间范围查询或搜索
 func TestQueryCasbinRuleByType(t *testing.T) {
 	successExpectedResult.ResponseData = testCasbinRule.Type
@@ -71,7 +71,7 @@ func TestQueryCasbinRuleByType(t *testing.T) {
 	runTest(t, testCase)
 }
 
-// QuerySearch CasbinRule search by NetType test case
+// QuerySearch CasbinRule search by Type test case
 // 分页搜索
 func TestQuerySearchCasbinRuleType(t *testing.T) {
 	successExpectedResult.ResponseData = testCasbinRule.Type
@@ -82,7 +82,7 @@ func TestQuerySearchCasbinRuleType(t *testing.T) {
 // UpdateByID CasbinRule test case
 // 根据 ID 修改
 func TestUpdateByIDCasbinRule(t *testing.T) {
-	updateData := &ent.CasbinRule{Type: "bLMjbQWupR", Sub: "BavnxpHmXL", Dom: "yafeGMEdjx", Obj: "IDFjRDpIMK", Act: "HkXSBGTdRw", V4: "XTCjiLDGuL", V5: "KdCyuFpwTe"}
+	updateData := &ent.CasbinRule{Type: "HXTIlKlxfr", Sub: "ggcMxCkDcu", Dom: "HagjQSmNoI", Obj: "mjnhlkJqPN", Act: "hgDiHOZsJC", V4: "ERqqffqDDW", V5: "IAfyRTYSkM"}
 	successExpectedResult.ResponseData = updateData.Type
 	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "PUT", UrlData: strconv.Itoa(casbinRuleIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/clean_data_test.go b/api/test/clean_data_test.go
index 6a4d684..5b69222 100644
--- a/api/test/clean_data_test.go
+++ b/api/test/clean_data_test.go
@@ -1,18 +1,18 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/cleandata"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cleandata"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var cleanDataApi = baseApi + "/cleandata"
-var cfilter14 = "DAYoiTItHr"
-var host14 = "wmbsnNRlJz"
-var testCleanData = &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "GUQhnGzZkN", Time: time.Now().AddDate(0, 0, -1), InBps: 366, OutBps: 393, InPps: 374, OutPps: 316, InAckPps: 344, OutAckPps: 342, InAckBps: 342, OutAckBps: 362, InSynPps: 387, OutSynPps: 390, InUDPPps: 310, OutUDPPps: 383, InUDPBps: 310, OutUDPBps: 366, InIcmpPps: 338, InIcmpBps: 358, OutIcmpBps: 312, OutIcmpPps: 370, InDNSPps: 352, OutDNSPps: 332, InDNSBps: 329, OutDNSBps: 342, AttackFlags: 396, Count: 382, IPType: 369, CFilter: &cfilter14, Host: &host14}
+var cfilter26 = "DiqAUsFCQN"
+var host26 = "jlBINzQnJU"
+var testCleanData = &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "JebudpgPvE", Time: time.Now().AddDate(0, 0, -1), InBps: 344, OutBps: 31, InPps: 390, OutPps: 339, InAckPps: 380, OutAckPps: 329, InAckBps: 38, OutAckBps: 320, InSynPps: 362, OutSynPps: 355, InUDPPps: 366, OutUDPPps: 391, InUDPBps: 395, OutUDPBps: 344, InIcmpPps: 391, InIcmpBps: 32, OutIcmpBps: 363, OutIcmpPps: 310, InDNSPps: 387, OutDNSPps: 339, InDNSBps: 380, OutDNSBps: 390, AttackFlags: 330, Count: 337, IPType: 341, CFilter: &cfilter26, Host: &host26}
 var cleanDataIDs = []int{}
 
 // Create CleanData test case
@@ -27,21 +27,21 @@ func TestCreateCleanData(t *testing.T) {
 // CreateBulk CleanData test case
 // 批量创建
 func TestCreateBulkCleanData(t *testing.T) {
-	cfilter1 := "CMrUwwWNpH"
-	host1 := "imgAUZBcQE"
-	bulkData1 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "fJhhzIHKnP", Time: time.Now().AddDate(0, 0, -1), InBps: 393, OutBps: 368, InPps: 329, OutPps: 35, InAckPps: 33, OutAckPps: 373, InAckBps: 368, OutAckBps: 385, InSynPps: 392, OutSynPps: 392, InUDPPps: 396, OutUDPPps: 383, InUDPBps: 347, OutUDPBps: 328, InIcmpPps: 376, InIcmpBps: 387, OutIcmpBps: 395, OutIcmpPps: 37, InDNSPps: 34, OutDNSPps: 387, InDNSBps: 332, OutDNSBps: 358, AttackFlags: 36, Count: 398, IPType: 327, CFilter: &cfilter1, Host: &host1}
-	cfilter2 := "SoIrvLfuHW"
-	host2 := "wBEJidZTze"
-	bulkData2 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "oKkNMpRygy", Time: time.Now().AddDate(0, 0, -1), InBps: 374, OutBps: 386, InPps: 385, OutPps: 378, InAckPps: 323, OutAckPps: 379, InAckBps: 314, OutAckBps: 338, InSynPps: 36, OutSynPps: 374, InUDPPps: 393, OutUDPPps: 358, InUDPBps: 318, OutUDPBps: 374, InIcmpPps: 322, InIcmpBps: 366, OutIcmpBps: 385, OutIcmpPps: 357, InDNSPps: 390, OutDNSPps: 372, InDNSBps: 337, OutDNSBps: 329, AttackFlags: 353, Count: 357, IPType: 341, CFilter: &cfilter2, Host: &host2}
-	cfilter3 := "CWsrhngkIT"
-	host3 := "qlYBUWsfQF"
-	bulkData3 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "xfsmdpfXeE", Time: time.Now().AddDate(0, 0, -1), InBps: 341, OutBps: 369, InPps: 331, OutPps: 384, InAckPps: 373, OutAckPps: 352, InAckBps: 317, OutAckBps: 341, InSynPps: 372, OutSynPps: 348, InUDPPps: 34, OutUDPPps: 34, InUDPBps: 396, OutUDPBps: 397, InIcmpPps: 347, InIcmpBps: 392, OutIcmpBps: 340, OutIcmpPps: 334, InDNSPps: 33, OutDNSPps: 332, InDNSBps: 396, OutDNSBps: 347, AttackFlags: 395, Count: 398, IPType: 390, CFilter: &cfilter3, Host: &host3}
-	cfilter4 := "OCTCxWsVzV"
-	host4 := "nSutPYLQrz"
-	bulkData4 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "XXsGUGsBiN", Time: time.Now().AddDate(0, 0, -1), InBps: 330, OutBps: 343, InPps: 378, OutPps: 366, InAckPps: 378, OutAckPps: 329, InAckBps: 341, OutAckBps: 375, InSynPps: 375, OutSynPps: 337, InUDPPps: 388, OutUDPPps: 357, InUDPBps: 365, OutUDPBps: 339, InIcmpPps: 377, InIcmpBps: 357, OutIcmpBps: 358, OutIcmpPps: 373, InDNSPps: 340, OutDNSPps: 338, InDNSBps: 376, OutDNSBps: 320, AttackFlags: 338, Count: 314, IPType: 34, CFilter: &cfilter4, Host: &host4}
-	cfilter5 := "rTROudTUgP"
-	host5 := "wKGgEKGnOM"
-	bulkData5 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "MPzqQBJsMb", Time: time.Now().AddDate(0, 0, -1), InBps: 38, OutBps: 364, InPps: 337, OutPps: 316, InAckPps: 384, OutAckPps: 312, InAckBps: 38, OutAckBps: 340, InSynPps: 364, OutSynPps: 316, InUDPPps: 340, OutUDPPps: 311, InUDPBps: 340, OutUDPBps: 367, InIcmpPps: 395, InIcmpBps: 340, OutIcmpBps: 338, OutIcmpPps: 311, InDNSPps: 395, OutDNSPps: 394, InDNSBps: 326, OutDNSBps: 371, AttackFlags: 323, Count: 318, IPType: 371, CFilter: &cfilter5, Host: &host5}
+	cfilter1 := "GsvRWWOpFD"
+	host1 := "BIZjgXKiiA"
+	bulkData1 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "FoVBJIHgdl", Time: time.Now().AddDate(0, 0, -1), InBps: 349, OutBps: 374, InPps: 388, OutPps: 333, InAckPps: 36, OutAckPps: 372, InAckBps: 385, OutAckBps: 32, InSynPps: 323, OutSynPps: 345, InUDPPps: 366, OutUDPPps: 378, InUDPBps: 333, OutUDPBps: 322, InIcmpPps: 392, InIcmpBps: 321, OutIcmpBps: 344, OutIcmpPps: 372, InDNSPps: 320, OutDNSPps: 363, InDNSBps: 387, OutDNSBps: 331, AttackFlags: 388, Count: 341, IPType: 342, CFilter: &cfilter1, Host: &host1}
+	cfilter2 := "TcniqKNrzD"
+	host2 := "IQMyFStjgw"
+	bulkData2 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "EzAyIqZGZV", Time: time.Now().AddDate(0, 0, -1), InBps: 372, OutBps: 343, InPps: 36, OutPps: 30, InAckPps: 377, OutAckPps: 376, InAckBps: 383, OutAckBps: 387, InSynPps: 358, OutSynPps: 346, InUDPPps: 367, OutUDPPps: 310, InUDPBps: 322, OutUDPBps: 313, InIcmpPps: 378, InIcmpBps: 397, OutIcmpBps: 346, OutIcmpPps: 36, InDNSPps: 391, OutDNSPps: 361, InDNSBps: 333, OutDNSBps: 393, AttackFlags: 389, Count: 382, IPType: 310, CFilter: &cfilter2, Host: &host2}
+	cfilter3 := "yQkjswzcTN"
+	host3 := "GdwiYMpOBU"
+	bulkData3 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "egfysTmadp", Time: time.Now().AddDate(0, 0, -1), InBps: 330, OutBps: 396, InPps: 34, OutPps: 345, InAckPps: 316, OutAckPps: 353, InAckBps: 348, OutAckBps: 393, InSynPps: 331, OutSynPps: 389, InUDPPps: 363, OutUDPPps: 372, InUDPBps: 36, OutUDPBps: 321, InIcmpPps: 344, InIcmpBps: 340, OutIcmpBps: 38, OutIcmpPps: 319, InDNSPps: 34, OutDNSPps: 336, InDNSBps: 376, OutDNSBps: 319, AttackFlags: 323, Count: 33, IPType: 311, CFilter: &cfilter3, Host: &host3}
+	cfilter4 := "NGNEfrJADc"
+	host4 := "LolfSzdTHc"
+	bulkData4 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "aglvDmfydO", Time: time.Now().AddDate(0, 0, -1), InBps: 342, OutBps: 322, InPps: 371, OutPps: 311, InAckPps: 332, OutAckPps: 398, InAckBps: 311, OutAckBps: 363, InSynPps: 351, OutSynPps: 341, InUDPPps: 37, OutUDPPps: 322, InUDPBps: 346, OutUDPBps: 322, InIcmpPps: 351, InIcmpBps: 336, OutIcmpBps: 330, OutIcmpPps: 362, InDNSPps: 367, OutDNSPps: 381, InDNSBps: 365, OutDNSBps: 322, AttackFlags: 344, Count: 384, IPType: 368, CFilter: &cfilter4, Host: &host4}
+	cfilter5 := "lyhXkCBGsU"
+	host5 := "PpmHibVbAJ"
+	bulkData5 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "VztrNKDsEY", Time: time.Now().AddDate(0, 0, -1), InBps: 387, OutBps: 329, InPps: 337, OutPps: 361, InAckPps: 356, OutAckPps: 389, InAckBps: 392, OutAckBps: 360, InSynPps: 368, OutSynPps: 370, InUDPPps: 34, OutUDPPps: 315, InUDPBps: 384, OutUDPBps: 360, InIcmpPps: 31, InIcmpBps: 323, OutIcmpBps: 347, OutIcmpPps: 320, InDNSPps: 331, OutDNSPps: 335, InDNSBps: 343, OutDNSBps: 323, AttackFlags: 314, Count: 323, IPType: 378, CFilter: &cfilter5, Host: &host5}
 	bulkDatas := [...]ent.CleanData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.IP
 	testCase := &CaseRule{Api: cleanDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -95,9 +95,9 @@ func TestQuerySearchCleanDataIP(t *testing.T) {
 // UpdateByID CleanData test case
 // 根据 ID 修改
 func TestUpdateByIDCleanData(t *testing.T) {
-	cfilter14 := "HautAnVxgg"
-	host14 := "IfEDxnYhTi"
-	updateData := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "eTIqSlRydl", Time: time.Now().AddDate(0, 0, -1), InBps: 398, OutBps: 35, InPps: 36, OutPps: 351, InAckPps: 35, OutAckPps: 333, InAckBps: 339, OutAckBps: 398, InSynPps: 369, OutSynPps: 342, InUDPPps: 366, OutUDPPps: 394, InUDPBps: 31, OutUDPBps: 334, InIcmpPps: 382, InIcmpBps: 357, OutIcmpBps: 393, OutIcmpPps: 349, InDNSPps: 369, OutDNSPps: 34, InDNSBps: 350, OutDNSBps: 372, AttackFlags: 314, Count: 320, IPType: 357, CFilter: &cfilter14, Host: &host14}
+	cfilter26 := "RlumnzglWy"
+	host26 := "fzytxYevqL"
+	updateData := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "MFfluWZEpA", Time: time.Now().AddDate(0, 0, -1), InBps: 390, OutBps: 326, InPps: 358, OutPps: 314, InAckPps: 362, OutAckPps: 356, InAckBps: 355, OutAckBps: 319, InSynPps: 349, OutSynPps: 370, InUDPPps: 382, OutUDPPps: 36, InUDPBps: 315, OutUDPBps: 360, InIcmpPps: 365, InIcmpBps: 39, OutIcmpBps: 31, OutIcmpPps: 373, InDNSPps: 329, OutDNSPps: 341, InDNSBps: 362, OutDNSBps: 364, AttackFlags: 368, Count: 38, IPType: 392, CFilter: &cfilter26, Host: &host26}
 	successExpectedResult.ResponseData = updateData.IP
 	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cleanDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/cloud_alert_test.go b/api/test/cloud_alert_test.go
index 70b0b74..d0e64b3 100644
--- a/api/test/cloud_alert_test.go
+++ b/api/test/cloud_alert_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/cloudalert"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudalert"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var cloudAlertApi = baseApi + "/cloudalert"
-var remark28 = "UqeGIdscen"
-var testCloudAlert = &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark28, SrcIP: "RsZvqaNEUl", SrcPort: 386, DstIP: "bNAtZeskAA", DstPort: 314, DefenceMode: 333, FlowMode: 323, TCPAckNum: "QBhSNvOuqQ", TCPSeqNum: "EKjHVFTXRL", Protocol: 363, DefenceLevel: 373, MaxPps: 397, MaxAttackPps: 36, OverlimitPktCount: 319, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+var remark12 = "cuMZMXvlxq"
+var testCloudAlert = &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, SrcIP: "VZtCDdybes", SrcPort: 389, DstIP: "vwYVeTXKOT", DstPort: 340, DefenceMode: 351, FlowMode: 395, TCPAckNum: "KwLIgcShck", TCPSeqNum: "aLdyHunkpg", Protocol: 352, DefenceLevel: 385, MaxPps: 339, MaxAttackPps: 339, OverlimitPktCount: 324, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 var cloudAlertIDs = []int{}
 
 // Create CloudAlert test case
@@ -26,16 +26,16 @@ func TestCreateCloudAlert(t *testing.T) {
 // CreateBulk CloudAlert test case
 // 批量创建
 func TestCreateBulkCloudAlert(t *testing.T) {
-	remark1 := "FDcATBrMZi"
-	bulkData1 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "SoIFFsDcaF", SrcPort: 341, DstIP: "wKkblIHbtH", DstPort: 313, DefenceMode: 325, FlowMode: 322, TCPAckNum: "URLAXVBucl", TCPSeqNum: "GtnEJvxaPJ", Protocol: 357, DefenceLevel: 393, MaxPps: 381, MaxAttackPps: 360, OverlimitPktCount: 310, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark2 := "AwgivkPYwQ"
-	bulkData2 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "IVbARFUhFI", SrcPort: 35, DstIP: "qplKfwBBNC", DstPort: 385, DefenceMode: 312, FlowMode: 374, TCPAckNum: "vGGiVaHUkO", TCPSeqNum: "oJvYzmktTs", Protocol: 313, DefenceLevel: 380, MaxPps: 389, MaxAttackPps: 364, OverlimitPktCount: 372, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark3 := "tflKMlEoMa"
-	bulkData3 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "uQYubANbTG", SrcPort: 360, DstIP: "UliRYBPxue", DstPort: 356, DefenceMode: 352, FlowMode: 348, TCPAckNum: "hIUKPMnHMI", TCPSeqNum: "cmlREeONaT", Protocol: 387, DefenceLevel: 388, MaxPps: 316, MaxAttackPps: 396, OverlimitPktCount: 375, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark4 := "wzwlVfzjTC"
-	bulkData4 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "mGPeBBmpPb", SrcPort: 371, DstIP: "FxgJrtotdh", DstPort: 354, DefenceMode: 315, FlowMode: 375, TCPAckNum: "HCrhlqvJpq", TCPSeqNum: "ZtPkSCwVTo", Protocol: 310, DefenceLevel: 39, MaxPps: 314, MaxAttackPps: 384, OverlimitPktCount: 315, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark5 := "bnEheERYDC"
-	bulkData5 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "ulbyTNGxsy", SrcPort: 320, DstIP: "JdqyonUgaY", DstPort: 351, DefenceMode: 336, FlowMode: 327, TCPAckNum: "hhIHvVXJCd", TCPSeqNum: "ZXQerkylLL", Protocol: 390, DefenceLevel: 331, MaxPps: 358, MaxAttackPps: 390, OverlimitPktCount: 370, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark1 := "zlReYVckhF"
+	bulkData1 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "TtlNnJqSqI", SrcPort: 328, DstIP: "PcEEDOfEGY", DstPort: 327, DefenceMode: 355, FlowMode: 387, TCPAckNum: "bmJMZYnelc", TCPSeqNum: "nPpyyoMiGG", Protocol: 316, DefenceLevel: 332, MaxPps: 379, MaxAttackPps: 379, OverlimitPktCount: 365, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark2 := "LimLDPBbYA"
+	bulkData2 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "cfljHLUgxt", SrcPort: 349, DstIP: "BolAirWPYM", DstPort: 362, DefenceMode: 313, FlowMode: 327, TCPAckNum: "Pwuuxebkfw", TCPSeqNum: "azoIxXRrnQ", Protocol: 322, DefenceLevel: 31, MaxPps: 325, MaxAttackPps: 359, OverlimitPktCount: 382, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark3 := "CyRNVqBUGg"
+	bulkData3 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "GRXSGUJgnS", SrcPort: 317, DstIP: "erGOInaVfF", DstPort: 347, DefenceMode: 370, FlowMode: 355, TCPAckNum: "sywFMxNDcY", TCPSeqNum: "bONLfxrEjA", Protocol: 361, DefenceLevel: 327, MaxPps: 322, MaxAttackPps: 354, OverlimitPktCount: 353, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark4 := "RZmBClyIsD"
+	bulkData4 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "XWzpvjkZpv", SrcPort: 349, DstIP: "SfcXxDGbcy", DstPort: 388, DefenceMode: 340, FlowMode: 354, TCPAckNum: "BtdTYygqnP", TCPSeqNum: "mcXEjwAKVC", Protocol: 325, DefenceLevel: 369, MaxPps: 389, MaxAttackPps: 357, OverlimitPktCount: 324, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark5 := "GrMbsLqLNA"
+	bulkData5 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "GZVbnUSNdy", SrcPort: 397, DstIP: "rSmyDHVWFd", DstPort: 389, DefenceMode: 396, FlowMode: 324, TCPAckNum: "LUhLUXEhee", TCPSeqNum: "sUmNfUPuub", Protocol: 380, DefenceLevel: 364, MaxPps: 37, MaxAttackPps: 396, OverlimitPktCount: 399, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	bulkDatas := [...]ent.CloudAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.SrcIP
 	testCase := &CaseRule{Api: cloudAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchCloudAlertSrcIP(t *testing.T) {
 // UpdateByID CloudAlert test case
 // 根据 ID 修改
 func TestUpdateByIDCloudAlert(t *testing.T) {
-	remark28 := "YWVRKDSaJa"
-	updateData := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark28, SrcIP: "cEldeSZIwt", SrcPort: 394, DstIP: "SzyEjHifXp", DstPort: 337, DefenceMode: 39, FlowMode: 330, TCPAckNum: "skfNNYrakF", TCPSeqNum: "pryaQstTjb", Protocol: 366, DefenceLevel: 366, MaxPps: 384, MaxAttackPps: 377, OverlimitPktCount: 369, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark12 := "oTUAncamoj"
+	updateData := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, SrcIP: "eYwfhSoYYl", SrcPort: 347, DstIP: "svWPxsyuqX", DstPort: 315, DefenceMode: 347, FlowMode: 394, TCPAckNum: "lPetnDLRzu", TCPSeqNum: "cAlTVjdDEu", Protocol: 396, DefenceLevel: 352, MaxPps: 344, MaxAttackPps: 372, OverlimitPktCount: 320, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	successExpectedResult.ResponseData = updateData.SrcIP
 	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/cloud_attack_data_test.go b/api/test/cloud_attack_data_test.go
index 218f2a9..6f17a05 100644
--- a/api/test/cloud_attack_data_test.go
+++ b/api/test/cloud_attack_data_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/cloudattackdata"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudattackdata"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var cloudAttackDataApi = baseApi + "/cloudattackdata"
-var remark26 = "GkRNPkMZGI"
-var testCloudAttackData = &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark26, SrcIP: "RqXcKBoCbK", SrcPort: 359, DstIP: "QOutmFoRWE", DstPort: 382, Protocol: 355, CurrentAttackPps: 375, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+var remark18 = "luDEnVbOcO"
+var testCloudAttackData = &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark18, SrcIP: "KYzGCdlFaf", SrcPort: 38, DstIP: "KpJMBaAgrp", DstPort: 345, Protocol: 332, CurrentAttackPps: 399, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 var cloudAttackDataIDs = []int{}
 
 // Create CloudAttackData test case
@@ -26,16 +26,16 @@ func TestCreateCloudAttackData(t *testing.T) {
 // CreateBulk CloudAttackData test case
 // 批量创建
 func TestCreateBulkCloudAttackData(t *testing.T) {
-	remark1 := "lcOPYAarzC"
-	bulkData1 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "zFhgETuqdm", SrcPort: 347, DstIP: "cyeYpnhKed", DstPort: 319, Protocol: 386, CurrentAttackPps: 383, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark2 := "nuKSwnnQkg"
-	bulkData2 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "uAuIYHRugj", SrcPort: 345, DstIP: "foHCunPRBO", DstPort: 382, Protocol: 397, CurrentAttackPps: 314, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark3 := "GlbcyTktcp"
-	bulkData3 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "HzkSwVxlda", SrcPort: 391, DstIP: "iOYltnbcQP", DstPort: 398, Protocol: 321, CurrentAttackPps: 371, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark4 := "nGPwWlNdgK"
-	bulkData4 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "lATojNRggy", SrcPort: 326, DstIP: "JpgHWJsxXH", DstPort: 365, Protocol: 317, CurrentAttackPps: 362, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark5 := "GKgjKoRWDq"
-	bulkData5 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "uJthPzSHcs", SrcPort: 389, DstIP: "CzyZkQnXcg", DstPort: 369, Protocol: 359, CurrentAttackPps: 349, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark1 := "bVjFKXbHwz"
+	bulkData1 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "dXvxghmGAJ", SrcPort: 355, DstIP: "HKakknoDSL", DstPort: 348, Protocol: 371, CurrentAttackPps: 314, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark2 := "BJkhMAkUpS"
+	bulkData2 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "lWTwteEmOB", SrcPort: 347, DstIP: "ornWyaGfuv", DstPort: 327, Protocol: 351, CurrentAttackPps: 395, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark3 := "UPjzkHKeIr"
+	bulkData3 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "wTbswcethk", SrcPort: 343, DstIP: "VVagrSuHRu", DstPort: 340, Protocol: 348, CurrentAttackPps: 335, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark4 := "ofkCVnIlzZ"
+	bulkData4 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "TaLmWVAULw", SrcPort: 335, DstIP: "cFFTzKwaGG", DstPort: 355, Protocol: 391, CurrentAttackPps: 382, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark5 := "osbsOQBBnk"
+	bulkData5 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "mwcwVxIcIx", SrcPort: 358, DstIP: "jBrdqObBlU", DstPort: 378, Protocol: 334, CurrentAttackPps: 330, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	bulkDatas := [...]ent.CloudAttackData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.SrcIP
 	testCase := &CaseRule{Api: cloudAttackDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchCloudAttackDataSrcIP(t *testing.T) {
 // UpdateByID CloudAttackData test case
 // 根据 ID 修改
 func TestUpdateByIDCloudAttackData(t *testing.T) {
-	remark26 := "EzvBOlMcLs"
-	updateData := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark26, SrcIP: "QItOMTGiAZ", SrcPort: 345, DstIP: "soymgRtWDL", DstPort: 348, Protocol: 375, CurrentAttackPps: 38, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark18 := "NBBqXFFliw"
+	updateData := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark18, SrcIP: "ILgBUEJKSF", SrcPort: 335, DstIP: "itIUYZZDol", DstPort: 317, Protocol: 376, CurrentAttackPps: 314, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	successExpectedResult.ResponseData = updateData.SrcIP
 	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudAttackDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/cloud_flow_data_test.go b/api/test/cloud_flow_data_test.go
index 293c7ef..55d0e67 100644
--- a/api/test/cloud_flow_data_test.go
+++ b/api/test/cloud_flow_data_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/cloudflowdata"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudflowdata"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var cloudFlowDataApi = baseApi + "/cloudflowdata"
-var remark11 = "pNgXBneSqK"
-var testCloudFlowData = &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, SrcIP: "eaEebLYUTb", SrcPort: 313, DstIP: "LBbTxURLpL", DstPort: 376, Protocol: 315, MaxAttackPps: 396, FlowOverMaxPpsCount: 372, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+var remark19 = "RcJjvQsGpH"
+var testCloudFlowData = &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark19, SrcIP: "HzKTfPRhhO", SrcPort: 377, DstIP: "LDWJpXsMac", DstPort: 380, Protocol: 352, MaxAttackPps: 381, FlowOverMaxPpsCount: 366, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 var cloudFlowDataIDs = []int{}
 
 // Create CloudFlowData test case
@@ -26,16 +26,16 @@ func TestCreateCloudFlowData(t *testing.T) {
 // CreateBulk CloudFlowData test case
 // 批量创建
 func TestCreateBulkCloudFlowData(t *testing.T) {
-	remark1 := "ufeLJoaShc"
-	bulkData1 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "IqRsHHOLEK", SrcPort: 331, DstIP: "XcPHATBZBe", DstPort: 383, Protocol: 35, MaxAttackPps: 314, FlowOverMaxPpsCount: 320, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark2 := "zwgvkYQkGx"
-	bulkData2 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "gtVCivhkmZ", SrcPort: 358, DstIP: "TgZnJTiLmH", DstPort: 360, Protocol: 322, MaxAttackPps: 386, FlowOverMaxPpsCount: 398, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark3 := "fhUirQYmMq"
-	bulkData3 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "SylEwUnFUN", SrcPort: 343, DstIP: "rvaXMytUsO", DstPort: 367, Protocol: 345, MaxAttackPps: 363, FlowOverMaxPpsCount: 379, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark4 := "GZxpzpswYc"
-	bulkData4 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "AZhGToLEMC", SrcPort: 318, DstIP: "CumGXFxmsJ", DstPort: 362, Protocol: 333, MaxAttackPps: 31, FlowOverMaxPpsCount: 320, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
-	remark5 := "DjINMqDsSH"
-	bulkData5 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "zvUHLxYGes", SrcPort: 330, DstIP: "spqwZyATRH", DstPort: 384, Protocol: 320, MaxAttackPps: 310, FlowOverMaxPpsCount: 396, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark1 := "kcczapLhtb"
+	bulkData1 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "OZHTZLnDrz", SrcPort: 376, DstIP: "dvFRPTEpEE", DstPort: 350, Protocol: 317, MaxAttackPps: 394, FlowOverMaxPpsCount: 356, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark2 := "NecLLMEMHA"
+	bulkData2 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "nHKRHJxdpS", SrcPort: 355, DstIP: "YZXJINhhOq", DstPort: 323, Protocol: 376, MaxAttackPps: 370, FlowOverMaxPpsCount: 342, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark3 := "KdIwNSETeL"
+	bulkData3 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "rZOKcmjxSM", SrcPort: 320, DstIP: "tGmJKlcckv", DstPort: 398, Protocol: 331, MaxAttackPps: 31, FlowOverMaxPpsCount: 387, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark4 := "XuFnwmDYJy"
+	bulkData4 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "zDekqyxtqS", SrcPort: 381, DstIP: "aFTQVzUzcw", DstPort: 32, Protocol: 363, MaxAttackPps: 373, FlowOverMaxPpsCount: 337, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark5 := "XFWbwtbBeI"
+	bulkData5 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "JkUXKUMitR", SrcPort: 346, DstIP: "QRDNlpQIOU", DstPort: 389, Protocol: 330, MaxAttackPps: 317, FlowOverMaxPpsCount: 386, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	bulkDatas := [...]ent.CloudFlowData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.SrcIP
 	testCase := &CaseRule{Api: cloudFlowDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchCloudFlowDataSrcIP(t *testing.T) {
 // UpdateByID CloudFlowData test case
 // 根据 ID 修改
 func TestUpdateByIDCloudFlowData(t *testing.T) {
-	remark11 := "nPCbyRAEeT"
-	updateData := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, SrcIP: "afAKKpNyzI", SrcPort: 364, DstIP: "plIHhsXNhF", DstPort: 337, Protocol: 332, MaxAttackPps: 377, FlowOverMaxPpsCount: 338, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
+	remark19 := "rpmhXUBDRJ"
+	updateData := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark19, SrcIP: "NRMoNWteDD", SrcPort: 380, DstIP: "rKVEaTPKbn", DstPort: 332, Protocol: 30, MaxAttackPps: 343, FlowOverMaxPpsCount: 376, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
 	successExpectedResult.ResponseData = updateData.SrcIP
 	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudFlowDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/data_sync_test.go b/api/test/data_sync_test.go
index 85220b2..da2b4e9 100644
--- a/api/test/data_sync_test.go
+++ b/api/test/data_sync_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/datasync"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/datasync"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var dataSyncApi = baseApi + "/datasync"
-var remark29 = "kabILiihRq"
-var testDataSync = &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, DataType: "azdyBeuAcv", Type: "LBULAJqcua"}
+var remark13 = "dvSXOzYYgQ"
+var testDataSync = &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark13, DataType: "KLmAADyrtb", Type: "IoUcFftXyc"}
 var dataSyncIDs = []int{}
 
 // Create DataSync test case
@@ -26,16 +26,16 @@ func TestCreateDataSync(t *testing.T) {
 // CreateBulk DataSync test case
 // 批量创建
 func TestCreateBulkDataSync(t *testing.T) {
-	remark1 := "NptGKMcwFI"
-	bulkData1 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, DataType: "VKxHRQNYeV", Type: "PnjEZHcjoO"}
-	remark2 := "GtGaqtveWh"
-	bulkData2 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, DataType: "rmzLHcHfjJ", Type: "iRDJCdVCdl"}
-	remark3 := "nHbeAhIvNt"
-	bulkData3 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, DataType: "CWUWaNJVAL", Type: "jAeShYWFmz"}
-	remark4 := "OkzxMkzmas"
-	bulkData4 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, DataType: "QrtOyLqGso", Type: "ABbkGtfsEn"}
-	remark5 := "ppBrGHTKMv"
-	bulkData5 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, DataType: "nrcNBkjCUz", Type: "QyfyhgGusV"}
+	remark1 := "aROvtUfBWK"
+	bulkData1 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, DataType: "MtEebPOEEq", Type: "uiEjCekzUc"}
+	remark2 := "xJRURLRRKH"
+	bulkData2 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, DataType: "uysNzCsxvu", Type: "UEnlFNEChK"}
+	remark3 := "qjOFnNRBKe"
+	bulkData3 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, DataType: "JNdttCcYzE", Type: "GprdMoBziV"}
+	remark4 := "NOBFVXQWBw"
+	bulkData4 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, DataType: "tCQSJIyAUE", Type: "tnJSeewxsM"}
+	remark5 := "iBDsLzZKRq"
+	bulkData5 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, DataType: "OdFpnzpeZm", Type: "vUXEiFNjVL"}
 	bulkDatas := [...]ent.DataSync{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.DataType
 	testCase := &CaseRule{Api: dataSyncApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchDataSyncDataType(t *testing.T) {
 // UpdateByID DataSync test case
 // 根据 ID 修改
 func TestUpdateByIDDataSync(t *testing.T) {
-	remark29 := "WtVNUmQJNC"
-	updateData := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, DataType: "kTBluTNXTP", Type: "cpTDMSNdpg"}
+	remark13 := "DXZqpzoNtL"
+	updateData := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark13, DataType: "YKLyPGNjzb", Type: "AcrlaQqExP"}
 	successExpectedResult.ResponseData = updateData.DataType
 	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "PUT", UrlData: strconv.Itoa(dataSyncIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/group_test.go b/api/test/group_test.go
index b4449b6..5036504 100644
--- a/api/test/group_test.go
+++ b/api/test/group_test.go
@@ -1,15 +1,15 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/group"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/group"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 )
 
 var groupApi = baseApi + "/group"
-var testGroup = &ent.Group{Name: "WjKdDnuedk"}
+var testGroup = &ent.Group{Name: "SqvkdRpCOs"}
 var groupIDs = []int{}
 
 // Create Group test case
@@ -24,11 +24,11 @@ func TestCreateGroup(t *testing.T) {
 // CreateBulk Group test case
 // 批量创建
 func TestCreateBulkGroup(t *testing.T) {
-	bulkData1 := &ent.Group{Name: "uCXOpcSpBa"}
-	bulkData2 := &ent.Group{Name: "TpajQapJgS"}
-	bulkData3 := &ent.Group{Name: "hbJJBryxwR"}
-	bulkData4 := &ent.Group{Name: "lEdbdkncZV"}
-	bulkData5 := &ent.Group{Name: "vFUwaDPwsp"}
+	bulkData1 := &ent.Group{Name: "iWqNRjHupq"}
+	bulkData2 := &ent.Group{Name: "gtXGVsAWwt"}
+	bulkData3 := &ent.Group{Name: "YezWoNKcwK"}
+	bulkData4 := &ent.Group{Name: "diBTUeJKkb"}
+	bulkData5 := &ent.Group{Name: "BuSuiyHNHk"}
 	bulkDatas := [...]ent.Group{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: groupApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -82,7 +82,7 @@ func TestQuerySearchGroupName(t *testing.T) {
 // UpdateByID Group test case
 // 根据 ID 修改
 func TestUpdateByIDGroup(t *testing.T) {
-	updateData := &ent.Group{Name: "uLZwVhenJd"}
+	updateData := &ent.Group{Name: "ysTjTaSJdn"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: groupApi, HttpMethod: "PUT", UrlData: strconv.Itoa(groupIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/matrix_spectrum_alert_test.go b/api/test/matrix_spectrum_alert_test.go
index de9fd62..cedd58f 100644
--- a/api/test/matrix_spectrum_alert_test.go
+++ b/api/test/matrix_spectrum_alert_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/matrixspectrumalert"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixspectrumalert"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var matrixSpectrumAlertApi = baseApi + "/matrixspectrumalert"
-var remark12 = "vHoKKyHBPd"
-var testMatrixSpectrumAlert = &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Region: "hfWCxhgVTe", NetType: "JskzSasnWx", Isp: "xzKKHeZgbS", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "HKxSoZIYUB", Bps: 371}
+var remark29 = "BXgldJLGNA"
+var testMatrixSpectrumAlert = &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, Region: "IyZPikxtnM", NetType: "VKYFmgoCtK", Isp: "YUhRPTzlmC", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "eXoHWlsFBK", Bps: 345}
 var matrixSpectrumAlertIDs = []int{}
 
 // Create MatrixSpectrumAlert test case
@@ -26,16 +26,16 @@ func TestCreateMatrixSpectrumAlert(t *testing.T) {
 // CreateBulk MatrixSpectrumAlert test case
 // 批量创建
 func TestCreateBulkMatrixSpectrumAlert(t *testing.T) {
-	remark1 := "qmKkIdqsou"
-	bulkData1 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Region: "UmqPotKTdU", NetType: "hMSQJLOUvP", Isp: "NyNvvoYNmK", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "UyqBnYxnFd", Bps: 36}
-	remark2 := "vMCHyRVBYj"
-	bulkData2 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Region: "SZgmTDxCfr", NetType: "nNwVWIhPFG", Isp: "wlpYWCymqz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "kguJDwqTjP", Bps: 36}
-	remark3 := "usIVzvvtCr"
-	bulkData3 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Region: "oCemjffIpD", NetType: "AAZFkeAgpo", Isp: "dqIipYVbeQ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "GtxuCCEFRH", Bps: 347}
-	remark4 := "gortdMqZkn"
-	bulkData4 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Region: "plCHPmCuxU", NetType: "hcDCjiOkpU", Isp: "oxqyclcaKc", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "UEdgqwccOM", Bps: 342}
-	remark5 := "MHZpcnFrps"
-	bulkData5 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Region: "PkrFuYfWSv", NetType: "CNluEwNzha", Isp: "jzrrksBhXi", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "KlIgrQZdUA", Bps: 358}
+	remark1 := "TFUUjdOsra"
+	bulkData1 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Region: "ZrQuwwvyEC", NetType: "cfkfHVFZNk", Isp: "zkZLGkmkjf", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "rezzUsFOfF", Bps: 334}
+	remark2 := "ebPhpvhLzJ"
+	bulkData2 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Region: "tLhcpvKZoX", NetType: "pNEJPEEUXy", Isp: "RjMODWEdcU", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "OSDVeDtKDm", Bps: 367}
+	remark3 := "EnExexUZhq"
+	bulkData3 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Region: "PVtszBJGHz", NetType: "OoyFoqxcmL", Isp: "lLGGCOlmJg", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "lhnYrutoLd", Bps: 326}
+	remark4 := "DXWmVtpnlQ"
+	bulkData4 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Region: "rxmOckACpd", NetType: "NKQpWaxMOb", Isp: "feYefGmUOO", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "QrccBxlMKp", Bps: 34}
+	remark5 := "nQJsXbNMSe"
+	bulkData5 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Region: "YxrFLJerDy", NetType: "UtZgccUfUw", Isp: "pJWpBEuGeD", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "iQKbAncToG", Bps: 328}
 	bulkDatas := [...]ent.MatrixSpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Region
 	testCase := &CaseRule{Api: matrixSpectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchMatrixSpectrumAlertRegion(t *testing.T) {
 // UpdateByID MatrixSpectrumAlert test case
 // 根据 ID 修改
 func TestUpdateByIDMatrixSpectrumAlert(t *testing.T) {
-	remark12 := "DXzbjKNNoT"
-	updateData := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Region: "lKiHbeZVCO", NetType: "SuoNydzhvu", Isp: "fXkwlcXRsS", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "GxqEFsEXgA", Bps: 351}
+	remark29 := "MwjMzcWbnY"
+	updateData := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, Region: "ImYhuYVUPw", NetType: "SLqbpLLoJX", Isp: "IzedYkmCxo", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "lubjoYDufN", Bps: 316}
 	successExpectedResult.ResponseData = updateData.Region
 	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/matrix_spectrum_data_test.go b/api/test/matrix_spectrum_data_test.go
index 4b6a1f8..36178d7 100644
--- a/api/test/matrix_spectrum_data_test.go
+++ b/api/test/matrix_spectrum_data_test.go
@@ -1,16 +1,16 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/matrixspectrumdata"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixspectrumdata"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var matrixSpectrumDataApi = baseApi + "/matrixspectrumdata"
-var testMatrixSpectrumData = &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "ablcNnlmIe", NetType: "uOJbnZKbwq", Isp: "vMWoXnytFu", Bps: 345, Time: time.Now().AddDate(0, 0, -1)}
+var testMatrixSpectrumData = &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "IMjAlZPWHu", NetType: "laeEFrqtoe", Isp: "tttMxoNKVB", Bps: 347, Time: time.Now().AddDate(0, 0, -1)}
 var matrixSpectrumDataIDs = []int{}
 
 // Create MatrixSpectrumData test case
@@ -25,11 +25,11 @@ func TestCreateMatrixSpectrumData(t *testing.T) {
 // CreateBulk MatrixSpectrumData test case
 // 批量创建
 func TestCreateBulkMatrixSpectrumData(t *testing.T) {
-	bulkData1 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "qqiCvYzIgq", NetType: "KIQZxNuuqe", Isp: "ADFWQoAXSK", Bps: 376, Time: time.Now().AddDate(0, 0, -1)}
-	bulkData2 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "VPUrcDpfSt", NetType: "TnGZkeWzXH", Isp: "agMpCDvLPn", Bps: 373, Time: time.Now().AddDate(0, 0, -1)}
-	bulkData3 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "kBxdgHKNPn", NetType: "PQMcCdNULW", Isp: "jsqUaxTsTH", Bps: 370, Time: time.Now().AddDate(0, 0, -1)}
-	bulkData4 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "MeAvXDRJLU", NetType: "PYvevjbvoI", Isp: "zRYBBjKxOz", Bps: 361, Time: time.Now().AddDate(0, 0, -1)}
-	bulkData5 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "qGdgPxTrMQ", NetType: "QBfYfRUPuU", Isp: "ZYOBsCyLtS", Bps: 325, Time: time.Now().AddDate(0, 0, -1)}
+	bulkData1 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "tHoddKheAV", NetType: "MrMXakIzpd", Isp: "fAvUngiNzE", Bps: 381, Time: time.Now().AddDate(0, 0, -1)}
+	bulkData2 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "LknURnbMOI", NetType: "oVzBRDpyjW", Isp: "VGNjZAgvfC", Bps: 381, Time: time.Now().AddDate(0, 0, -1)}
+	bulkData3 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "lgTpenxvqf", NetType: "SqtGhjjcrb", Isp: "sPyUxRtgUU", Bps: 334, Time: time.Now().AddDate(0, 0, -1)}
+	bulkData4 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "HHAgYpSzTr", NetType: "IXwvjumOid", Isp: "OQEWCWOQhP", Bps: 317, Time: time.Now().AddDate(0, 0, -1)}
+	bulkData5 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "eWQqGBgIUs", NetType: "QmJkGlZEPh", Isp: "DIQoWNBHFJ", Bps: 346, Time: time.Now().AddDate(0, 0, -1)}
 	bulkDatas := [...]ent.MatrixSpectrumData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Region
 	testCase := &CaseRule{Api: matrixSpectrumDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -83,7 +83,7 @@ func TestQuerySearchMatrixSpectrumDataRegion(t *testing.T) {
 // UpdateByID MatrixSpectrumData test case
 // 根据 ID 修改
 func TestUpdateByIDMatrixSpectrumData(t *testing.T) {
-	updateData := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "INGeLKzjsQ", NetType: "zBfFClXIuV", Isp: "NWcCOOSLxs", Bps: 316, Time: time.Now().AddDate(0, 0, -1)}
+	updateData := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "BEdIfxSTlM", NetType: "dLjmiKgVNz", Isp: "cUfyConRHG", Bps: 360, Time: time.Now().AddDate(0, 0, -1)}
 	successExpectedResult.ResponseData = updateData.Region
 	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixSpectrumDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/matrix_strategy_test.go b/api/test/matrix_strategy_test.go
index ec0ed92..5c11149 100644
--- a/api/test/matrix_strategy_test.go
+++ b/api/test/matrix_strategy_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/matrixstrategy"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixstrategy"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var matrixStrategyApi = baseApi + "/matrixstrategy"
-var remark22 = "eiXUTjSdih"
-var testMatrixStrategy = &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, Name: "vKEVvIhqBL", Region: "yEGlPyXalt", NetType: "rmUuZPpcWG", Isp: "bAQWQqXCrD", MonitorBps: 374, DragBps: 334, DragType: 389}
+var remark20 = "EqGZXfnKtG"
+var testMatrixStrategy = &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "UvimlhAcWe", Region: "jGfXRkxEAm", NetType: "LcNaGzklZV", Isp: "uyPPUhdASX", MonitorBps: 377, DragBps: 378, DragType: 341}
 var matrixStrategyIDs = []int{}
 
 // Create MatrixStrategy test case
@@ -26,16 +26,16 @@ func TestCreateMatrixStrategy(t *testing.T) {
 // CreateBulk MatrixStrategy test case
 // 批量创建
 func TestCreateBulkMatrixStrategy(t *testing.T) {
-	remark1 := "AkTDsLgOaM"
-	bulkData1 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "xSFIwDqzMK", Region: "KpKmmouUFu", NetType: "CHOyKuPcuc", Isp: "UufjiNmoVG", MonitorBps: 323, DragBps: 366, DragType: 32}
-	remark2 := "DyGjADtIhb"
-	bulkData2 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "YVaWPjTTBW", Region: "ITRqwOLsmt", NetType: "DYDLPsZWOZ", Isp: "nIYZigADDU", MonitorBps: 359, DragBps: 322, DragType: 377}
-	remark3 := "yyeMXgOuHQ"
-	bulkData3 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "LWNIRnGcJf", Region: "oibbTPskKB", NetType: "UezciamqoC", Isp: "IeYTMpIBQu", MonitorBps: 397, DragBps: 393, DragType: 373}
-	remark4 := "kPYGkHZvXB"
-	bulkData4 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "qKWCtlyTDI", Region: "vEvSnEiEWA", NetType: "ONJiJEZNzZ", Isp: "fkhmftHUZN", MonitorBps: 333, DragBps: 365, DragType: 373}
-	remark5 := "QYpkFYnIDZ"
-	bulkData5 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "VBGTYWoKXY", Region: "TGjEdrWLnd", NetType: "svKzJtBRIP", Isp: "QIVPdNpqxV", MonitorBps: 39, DragBps: 393, DragType: 358}
+	remark1 := "XFKsqmKevy"
+	bulkData1 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "tURaWwGnAP", Region: "gAgDvrtyvT", NetType: "eMTbOYoKyK", Isp: "spiBfRvCUZ", MonitorBps: 317, DragBps: 345, DragType: 390}
+	remark2 := "YXprZvqMGZ"
+	bulkData2 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "HPCjMCthig", Region: "mesaBQqCjv", NetType: "ymvRWJkjml", Isp: "rHlZMCOAqL", MonitorBps: 399, DragBps: 39, DragType: 31}
+	remark3 := "rAHOZYTYHQ"
+	bulkData3 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "uFGkVXTJZy", Region: "HSfTYSwHJC", NetType: "ZXTfGbgyEu", Isp: "dFTOkUxITL", MonitorBps: 313, DragBps: 377, DragType: 346}
+	remark4 := "lOMsaxMmGZ"
+	bulkData4 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "WswPunbodj", Region: "ATeoUMBHdB", NetType: "VFNXAvZojx", Isp: "erkrwfPFTr", MonitorBps: 354, DragBps: 373, DragType: 377}
+	remark5 := "ySHKQCPVsC"
+	bulkData5 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "TmfCfiHHXW", Region: "lmzkVEfeBA", NetType: "bPCZTkeMkQ", Isp: "ayBLbxAinw", MonitorBps: 312, DragBps: 351, DragType: 359}
 	bulkDatas := [...]ent.MatrixStrategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: matrixStrategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchMatrixStrategyName(t *testing.T) {
 // UpdateByID MatrixStrategy test case
 // 根据 ID 修改
 func TestUpdateByIDMatrixStrategy(t *testing.T) {
-	remark22 := "AdPeiVYacl"
-	updateData := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, Name: "ziulvOQkmD", Region: "OLnXuenLMi", NetType: "DTEnKlWvWF", Isp: "jZNHDCKcVI", MonitorBps: 373, DragBps: 376, DragType: 384}
+	remark20 := "jansrCIrbg"
+	updateData := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "dqMXseoQIu", Region: "YLNuKXOwFc", NetType: "xiGArEVgCS", Isp: "CyviARrTTh", MonitorBps: 376, DragBps: 367, DragType: 340}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixStrategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/notify_test.go b/api/test/notify_test.go
index 3bc67a8..ced2081 100644
--- a/api/test/notify_test.go
+++ b/api/test/notify_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/notify"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/notify"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var notifyApi = baseApi + "/notify"
-var remark10 = "iuIDIggaGv"
-var testNotify = &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, Name: "BypXauiJFo"}
+var remark21 = "thEIqPbTOo"
+var testNotify = &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "gyCtLTxWQb"}
 var notifyIDs = []int{}
 
 // Create Notify test case
@@ -26,16 +26,16 @@ func TestCreateNotify(t *testing.T) {
 // CreateBulk Notify test case
 // 批量创建
 func TestCreateBulkNotify(t *testing.T) {
-	remark1 := "eUsDsbLvsE"
-	bulkData1 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "WIMDMnngSH"}
-	remark2 := "tpsDwGgXSp"
-	bulkData2 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "JmNVKGmPgK"}
-	remark3 := "QPdYWnVMmC"
-	bulkData3 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "wshhqycoZu"}
-	remark4 := "FCXlrFGviE"
-	bulkData4 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "wGWPBzKgqN"}
-	remark5 := "TVoqFDdIWN"
-	bulkData5 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "MHQADCqbtH"}
+	remark1 := "qMeYCbiqBX"
+	bulkData1 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "ioZTLbbSSC"}
+	remark2 := "dARyYEIFPO"
+	bulkData2 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "DnqJejBxiG"}
+	remark3 := "OtmPLHiWDm"
+	bulkData3 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "lQRRBDbJFn"}
+	remark4 := "TVIqXZkpfW"
+	bulkData4 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "IPVIybchqx"}
+	remark5 := "gwPkUZGVZM"
+	bulkData5 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "LSMwVcBwWn"}
 	bulkDatas := [...]ent.Notify{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: notifyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchNotifyName(t *testing.T) {
 // UpdateByID Notify test case
 // 根据 ID 修改
 func TestUpdateByIDNotify(t *testing.T) {
-	remark10 := "ILEKuCXTtd"
-	updateData := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, Name: "zPrPsEUUsF"}
+	remark21 := "wPKATrsKvn"
+	updateData := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "zkHfiZsEpq"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: notifyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(notifyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/protect_group_test.go b/api/test/protect_group_test.go
index 20d8404..5f07359 100644
--- a/api/test/protect_group_test.go
+++ b/api/test/protect_group_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/protectgroup"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/protectgroup"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var protectGroupApi = baseApi + "/protectgroup"
-var remark23 = "quBRmIMsUW"
-var testProtectGroup = &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, GroupName: "LQmrjaMflL", Type: 379, ExpandIP: "pyVDYcEhVq"}
+var remark15 = "JwistDAsMT"
+var testProtectGroup = &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, GroupName: "viuWNEYQQS", Type: 339, ExpandIP: "uURVqtlwuw"}
 var protectGroupIDs = []int{}
 
 // Create ProtectGroup test case
@@ -26,16 +26,16 @@ func TestCreateProtectGroup(t *testing.T) {
 // CreateBulk ProtectGroup test case
 // 批量创建
 func TestCreateBulkProtectGroup(t *testing.T) {
-	remark1 := "PyZITfrOUS"
-	bulkData1 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, GroupName: "LxAfWbFOwW", Type: 387, ExpandIP: "VwfPFfFQNH"}
-	remark2 := "aqfQQRbqqe"
-	bulkData2 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, GroupName: "KQWrzUUNIc", Type: 34, ExpandIP: "vnBtvIjnYG"}
-	remark3 := "lNhvegVoOK"
-	bulkData3 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, GroupName: "ftndFWmFHh", Type: 395, ExpandIP: "guMQDhWgaX"}
-	remark4 := "RDzpthuAZy"
-	bulkData4 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, GroupName: "EgmzDHBOzw", Type: 382, ExpandIP: "YhoxGtfYuW"}
-	remark5 := "swPFQYlRob"
-	bulkData5 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, GroupName: "ZWPaeFwwsN", Type: 311, ExpandIP: "ltcssXReZj"}
+	remark1 := "ZAfLrRpbno"
+	bulkData1 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, GroupName: "aBVDyUiwIV", Type: 338, ExpandIP: "NRBxIFISjz"}
+	remark2 := "hjJBGpajly"
+	bulkData2 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, GroupName: "YAGSHRrRGL", Type: 34, ExpandIP: "jvfbCyjlOL"}
+	remark3 := "UNPOGYgXHw"
+	bulkData3 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, GroupName: "EyRNnOHoYa", Type: 362, ExpandIP: "zFjNTmuCTz"}
+	remark4 := "mDZxcoOjQz"
+	bulkData4 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, GroupName: "MxYgEHkWmz", Type: 313, ExpandIP: "sadKgRqlnc"}
+	remark5 := "ChsSlXJAig"
+	bulkData5 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, GroupName: "CcZNaiZmUm", Type: 386, ExpandIP: "ytqKioIPCm"}
 	bulkDatas := [...]ent.ProtectGroup{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.GroupName
 	testCase := &CaseRule{Api: protectGroupApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchProtectGroupGroupName(t *testing.T) {
 // UpdateByID ProtectGroup test case
 // 根据 ID 修改
 func TestUpdateByIDProtectGroup(t *testing.T) {
-	remark23 := "FosRxAXBvh"
-	updateData := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, GroupName: "UXQMuYSrMN", Type: 398, ExpandIP: "XRbgskiOSU"}
+	remark15 := "LqsDBnLVLu"
+	updateData := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, GroupName: "ZfzoBhdzeT", Type: 337, ExpandIP: "UHhVgewmtv"}
 	successExpectedResult.ResponseData = updateData.GroupName
 	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "PUT", UrlData: strconv.Itoa(protectGroupIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/skyline_dos_test.go b/api/test/skyline_dos_test.go
index 45af58a..4900e8f 100644
--- a/api/test/skyline_dos_test.go
+++ b/api/test/skyline_dos_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/skylinedos"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/skylinedos"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var skylineDosApi = baseApi + "/skylinedos"
-var remark14 = "kyXyhKCwuc"
-var testSkylineDos = &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "YJSRxflgkO", Resource: "bvEtKIykWI", ResourceType: "pvxWMklozs", Status: "huIsBMtfgF", Project: "pljYCUefuY", DurationTime: 371}
+var remark23 = "SPZGtAPtNl"
+var testSkylineDos = &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "vHEoNtjZWI", Resource: "oIJaOygCEy", ResourceType: "zbdQszmZJP", Status: "gacbvyuBCr", Project: "ywHVtilJoL", DurationTime: 318}
 var skylineDosIDs = []int{}
 
 // Create SkylineDos test case
@@ -26,16 +26,16 @@ func TestCreateSkylineDos(t *testing.T) {
 // CreateBulk SkylineDos test case
 // 批量创建
 func TestCreateBulkSkylineDos(t *testing.T) {
-	remark1 := "PECAzpKfzR"
-	bulkData1 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "PMNvFCaqer", Resource: "ouXcqTnBQd", ResourceType: "AcxNivycEK", Status: "JFXjaKAYUn", Project: "CJxgSvPdux", DurationTime: 377}
-	remark2 := "rkzKUmmxVJ"
-	bulkData2 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "OKkCNZVXmg", Resource: "PEuuXXIMQP", ResourceType: "eIJuciWvQR", Status: "jAYXAYVByi", Project: "BluRiHvHar", DurationTime: 382}
-	remark3 := "qMiSciXlcy"
-	bulkData3 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "fubuVPLnDh", Resource: "kRQNlleVsJ", ResourceType: "ITpBYqrZJL", Status: "PjLuAJAwbp", Project: "FGfoJhnagt", DurationTime: 321}
-	remark4 := "DfZshfOwSD"
-	bulkData4 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "tRnpnrbbUP", Resource: "OnZVsISGod", ResourceType: "NxdWNMxxej", Status: "JFydpLKlBj", Project: "DGKHlrPbli", DurationTime: 344}
-	remark5 := "xSUwWKqZHz"
-	bulkData5 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "YHIZuayVHF", Resource: "JBQmQYDael", ResourceType: "jtGpJxXaXu", Status: "vduzoUyCMy", Project: "DqXRMTPSxd", DurationTime: 311}
+	remark1 := "RmHtJOoFqg"
+	bulkData1 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "XnIySQHtvj", Resource: "MgfDFUggeS", ResourceType: "YjgZVzMyRJ", Status: "KzOzeUtUnQ", Project: "VRHhZwwxso", DurationTime: 34}
+	remark2 := "cYXrnSXZeV"
+	bulkData2 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "MIfhCUujDV", Resource: "QsxarKfpoo", ResourceType: "oysoVFDiSA", Status: "aYfWSgsaMX", Project: "FdmeruMnNH", DurationTime: 316}
+	remark3 := "JjlxIeYFMW"
+	bulkData3 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "HDkZfKWYdH", Resource: "LWqvDqwrGd", ResourceType: "ojaukdGNVY", Status: "nhMfsONFVR", Project: "HllgneUWcH", DurationTime: 385}
+	remark4 := "SxUbkgVrxW"
+	bulkData4 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "ZdFIAHiryr", Resource: "pruzePDQCf", ResourceType: "NzHXIHksYU", Status: "MoeWyDsBxU", Project: "DQbwEGZznL", DurationTime: 396}
+	remark5 := "lMyzspSwna"
+	bulkData5 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "OwCafXVyAp", Resource: "MxVqMLyIdy", ResourceType: "vBmclQfdrV", Status: "vhPoTxSGqD", Project: "QNTNRBakVO", DurationTime: 321}
 	bulkDatas := [...]ent.SkylineDos{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Region
 	testCase := &CaseRule{Api: skylineDosApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchSkylineDosRegion(t *testing.T) {
 // UpdateByID SkylineDos test case
 // 根据 ID 修改
 func TestUpdateByIDSkylineDos(t *testing.T) {
-	remark14 := "sJzdjhBMTn"
-	updateData := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "VcSaXweZUy", Resource: "KWBIhEdmUY", ResourceType: "GMSdDLgggq", Status: "XgNZyrNnMq", Project: "oxWDexgqMk", DurationTime: 313}
+	remark23 := "jEXNsZgoat"
+	updateData := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "zzGlcUCOVB", Resource: "TRCJVvvLeX", ResourceType: "stWPBGjoRP", Status: "tVBffFzqcx", Project: "ceApLRrRfX", DurationTime: 313}
 	successExpectedResult.ResponseData = updateData.Region
 	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "PUT", UrlData: strconv.Itoa(skylineDosIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/soc_group_ticket_test.go b/api/test/soc_group_ticket_test.go
index eecf8e6..f674b6a 100644
--- a/api/test/soc_group_ticket_test.go
+++ b/api/test/soc_group_ticket_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/socgroupticket"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/socgroupticket"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var socGroupTicketApi = baseApi + "/socgroupticket"
-var remark122 = "lQhMXPJwfm"
-var testSocGroupTicket = &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark122, Name: "UxVDbkGlEc", Type: "HjiTZapAJu", Description: "wGaSjpEvAm", MinBandwidth: 0.9034219, DivertType: 36, OpType: 333, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 324, ConfigArgs: "YnHNCizbOR", ProductName: "CzvyOvRTbe", ProductCode: "BKUuPBEUJx", ErrorInfo: "JMzBYJSWfI"}
+var remark16 = "pPqFUfaavR"
+var testSocGroupTicket = &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark16, Name: "VZbqwNEkNa", Type: "MqHhrSxpqz", Description: "kBYfCxMzbC", MinBandwidth: 0.5466404, DivertType: 321, OpType: 331, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 368, ConfigArgs: "GVQKSuWmWd", ProductName: "cdXhkhTNmY", ProductCode: "tAxIezXOgT", ErrorInfo: "CzbhiKjLZq"}
 var socGroupTicketIDs = []int{}
 
 // Create SocGroupTicket test case
@@ -26,16 +26,16 @@ func TestCreateSocGroupTicket(t *testing.T) {
 // CreateBulk SocGroupTicket test case
 // 批量创建
 func TestCreateBulkSocGroupTicket(t *testing.T) {
-	remark1 := "NYFACxpZtS"
-	bulkData1 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "wUnbWiKlnn", Type: "RmEHEpxfAZ", Description: "aZhQbiuTVi", MinBandwidth: 0.3679356, DivertType: 325, OpType: 340, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 324, ConfigArgs: "GIheSIcSDJ", ProductName: "zHOwldfbAT", ProductCode: "xKnMhzPqtP", ErrorInfo: "dLDmKdOOvq"}
-	remark2 := "mWeFKZKcXf"
-	bulkData2 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "QGJQALBzau", Type: "nXBYThhgcv", Description: "eLPIXNfipo", MinBandwidth: 0.03303192, DivertType: 376, OpType: 37, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 335, ConfigArgs: "ZmgnPcHOUL", ProductName: "rRHdONFRZl", ProductCode: "cwsqMAkibt", ErrorInfo: "XKTIweQlSR"}
-	remark3 := "yPdSftxUim"
-	bulkData3 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "sOWvRbFVfR", Type: "NfrTTlXpxf", Description: "NtFAicIKIj", MinBandwidth: 0.18723436, DivertType: 319, OpType: 33, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 386, ConfigArgs: "RwjUepHccP", ProductName: "cYFjaLPgQV", ProductCode: "kTUtGdTgbY", ErrorInfo: "QvCpEARvck"}
-	remark4 := "kZUfkkeilP"
-	bulkData4 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "kqSKKMXrKI", Type: "XyISNsxceG", Description: "bziTUgapSK", MinBandwidth: 0.24749172, DivertType: 350, OpType: 314, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 315, ConfigArgs: "RJZIDAkbRt", ProductName: "QrzGIUjCoY", ProductCode: "jlrbvGlPWV", ErrorInfo: "aimDUDqXGR"}
-	remark5 := "oMIdJqHOpL"
-	bulkData5 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "VEGuBKpLvE", Type: "jghTnaHgCr", Description: "OSlfcwFpob", MinBandwidth: 0.8719464, DivertType: 385, OpType: 385, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 367, ConfigArgs: "TNdtEvcRPE", ProductName: "eZSjHKdAYq", ProductCode: "bqhsExkbvM", ErrorInfo: "MOqBltrlsg"}
+	remark1 := "wxDLCRsgqQ"
+	bulkData1 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "MKJawgCmeh", Type: "MUIguGLZLt", Description: "MLEoyvTmWo", MinBandwidth: 0.62209743, DivertType: 338, OpType: 382, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 375, ConfigArgs: "PygRkEbREK", ProductName: "AWZtzJqVYw", ProductCode: "gRhiJjcSrL", ErrorInfo: "FESwnQnicG"}
+	remark2 := "FSdiiGdMMV"
+	bulkData2 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "ZJjereLuvq", Type: "WJONxlbSHT", Description: "PHLDufQSDZ", MinBandwidth: 0.11622431, DivertType: 338, OpType: 346, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 350, ConfigArgs: "xynVRMqgNt", ProductName: "WiXbOKhQVc", ProductCode: "npUTSjnRRN", ErrorInfo: "CYKwXdJnql"}
+	remark3 := "CBHUuDbpZn"
+	bulkData3 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "kVTbPzmAGe", Type: "QNyxxDpwiX", Description: "yCQsrCSbNk", MinBandwidth: 0.5058889, DivertType: 358, OpType: 317, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 334, ConfigArgs: "aJFXJRNTNZ", ProductName: "tArRckqOvJ", ProductCode: "EgUmtEPFxS", ErrorInfo: "KbzrORAUMv"}
+	remark4 := "AWspdydblf"
+	bulkData4 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "ZlGktIPGBr", Type: "uirLDTZLXH", Description: "ifIhNEsmiM", MinBandwidth: 0.7028483, DivertType: 357, OpType: 329, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 333, ConfigArgs: "ZlBIfRrXIN", ProductName: "aOJWcNsgpZ", ProductCode: "AOXOoIhaWE", ErrorInfo: "eIZovPgXXh"}
+	remark5 := "faCmZyaeep"
+	bulkData5 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "MolPvetSrG", Type: "DhvBFzbdvp", Description: "mPKQyjGMPm", MinBandwidth: 0.16450997, DivertType: 328, OpType: 357, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 348, ConfigArgs: "MMMbRqtGWy", ProductName: "SrHSVvsmCZ", ProductCode: "AlwVyeeqOd", ErrorInfo: "XhfKVfJWXV"}
 	bulkDatas := [...]ent.SocGroupTicket{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: socGroupTicketApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchSocGroupTicketName(t *testing.T) {
 // UpdateByID SocGroupTicket test case
 // 根据 ID 修改
 func TestUpdateByIDSocGroupTicket(t *testing.T) {
-	remark122 := "nbNcvrmAUH"
-	updateData := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark122, Name: "xJNdJuOpTE", Type: "iAYmZXDCkI", Description: "BHYGczUJxg", MinBandwidth: 0.45922306, DivertType: 398, OpType: 312, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 349, ConfigArgs: "fLucAYqyvk", ProductName: "GgWMDOstWq", ProductCode: "SmXnfUAXks", ErrorInfo: "qAZlRrEMeg"}
+	remark16 := "ebfcuBkokC"
+	updateData := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark16, Name: "wIueHPIYRE", Type: "LWMIIZqCgs", Description: "mVwPyjYRrI", MinBandwidth: 0.80582404, DivertType: 348, OpType: 363, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 344, ConfigArgs: "ZWUrfJIMKP", ProductName: "wMBTvDycHy", ProductCode: "BjcEXFwVCC", ErrorInfo: "usXcRkQDXt"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "PUT", UrlData: strconv.Itoa(socGroupTicketIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/spectrum_alert_test.go b/api/test/spectrum_alert_test.go
index cfcf8cb..9639f94 100644
--- a/api/test/spectrum_alert_test.go
+++ b/api/test/spectrum_alert_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/spectrumalert"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/spectrumalert"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var spectrumAlertApi = baseApi + "/spectrumalert"
-var remark15 = "ZJxzvagFpy"
-var testSpectrumAlert = &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, IP: "JLkGkHygKk", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "bDLvwgpIzo", MaxPps: 319, MaxBps: 33, IspCode: 326}
+var remark22 = "yuaiFfoETm"
+var testSpectrumAlert = &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, IP: "UeRnhzUbjB", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "Uxwarolhaa", MaxPps: 390, MaxBps: 395, IspCode: 355}
 var spectrumAlertIDs = []int{}
 
 // Create SpectrumAlert test case
@@ -26,16 +26,16 @@ func TestCreateSpectrumAlert(t *testing.T) {
 // CreateBulk SpectrumAlert test case
 // 批量创建
 func TestCreateBulkSpectrumAlert(t *testing.T) {
-	remark1 := "OzJIEEnICJ"
-	bulkData1 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, IP: "xbfTexiWvz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "fVoNnDVBKY", MaxPps: 346, MaxBps: 338, IspCode: 399}
-	remark2 := "OXacJAugWQ"
-	bulkData2 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, IP: "VGwKnwdzZy", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "MCjepABHsk", MaxPps: 390, MaxBps: 399, IspCode: 310}
-	remark3 := "PXrUHncdEO"
-	bulkData3 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, IP: "ruMfErNSBL", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "anaWSPefuz", MaxPps: 331, MaxBps: 350, IspCode: 378}
-	remark4 := "hAXQtsiIDA"
-	bulkData4 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, IP: "TXmjeccWmA", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "vUPsfaBofn", MaxPps: 388, MaxBps: 393, IspCode: 377}
-	remark5 := "tZsahruGRB"
-	bulkData5 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, IP: "PIhwLgtZVu", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "sfRqrkiFHQ", MaxPps: 373, MaxBps: 392, IspCode: 380}
+	remark1 := "pPUJWdocJH"
+	bulkData1 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, IP: "CZfNxnBFXa", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "IifsWoubis", MaxPps: 311, MaxBps: 377, IspCode: 395}
+	remark2 := "KVkFKSKJgm"
+	bulkData2 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, IP: "xiIhKCPkoS", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "dpXdfsJKpl", MaxPps: 346, MaxBps: 342, IspCode: 353}
+	remark3 := "zoQbaCYsSP"
+	bulkData3 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, IP: "rKRCVgADVz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "kzlSEzszbL", MaxPps: 31, MaxBps: 348, IspCode: 39}
+	remark4 := "pQZyFEjegK"
+	bulkData4 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, IP: "JrAQIsBnHv", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "ANoNDMOXQU", MaxPps: 345, MaxBps: 380, IspCode: 329}
+	remark5 := "YXPZZsokUe"
+	bulkData5 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, IP: "TPAqUwjlsz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "OczNJiGDQq", MaxPps: 376, MaxBps: 360, IspCode: 324}
 	bulkDatas := [...]ent.SpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.IP
 	testCase := &CaseRule{Api: spectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchSpectrumAlertIP(t *testing.T) {
 // UpdateByID SpectrumAlert test case
 // 根据 ID 修改
 func TestUpdateByIDSpectrumAlert(t *testing.T) {
-	remark15 := "SpaboZcjAG"
-	updateData := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, IP: "aDMJRUnEQZ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "TcPcaZCVOv", MaxPps: 311, MaxBps: 331, IspCode: 338}
+	remark22 := "IrZoVcNbSa"
+	updateData := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, IP: "nQTIqqVDOC", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "ZaSiGyUqXH", MaxPps: 38, MaxBps: 319, IspCode: 395}
 	successExpectedResult.ResponseData = updateData.IP
 	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(spectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/spectrum_data_test.go b/api/test/spectrum_data_test.go
index b14386b..7ddd301 100644
--- a/api/test/spectrum_data_test.go
+++ b/api/test/spectrum_data_test.go
@@ -1,19 +1,19 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/spectrumdata"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/spectrumdata"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var spectrumDataApi = baseApi + "/spectrumdata"
-var monitor19 = "zFUjkqSprQ"
-var product19 = "dXMvtLjyFq"
-var host19 = "jlmgQnyTDI"
-var testSpectrumData = &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "zMrGPurmyV", Time: time.Now().AddDate(0, 0, -1), DataType: 318, Bps: 32, Pps: 313, SynBps: 373, SynPps: 322, AckBps: 355, AckPps: 393, SynAckBps: 347, SynAckPps: 377, IcmpBps: 326, IcmpPps: 346, SmallPps: 361, NtpPps: 31, NtpBps: 314, DNSQueryPps: 386, DNSQueryBps: 377, DNSAnswerPps: 381, DNSAnswerBps: 360, SsdpBps: 337, SsdpPps: 365, UDPPps: 313, UDPBps: 365, QPS: 397, ReceiveCount: 392, IPType: 365, Monitor: &monitor19, Product: &product19, Host: &host19}
+var monitor30 = "EBFlzmzcVY"
+var product30 = "NUrRSTTdZZ"
+var host30 = "sjidnrhrex"
+var testSpectrumData = &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "PzvjUKEFbZ", Time: time.Now().AddDate(0, 0, -1), DataType: 335, Bps: 346, Pps: 312, SynBps: 323, SynPps: 315, AckBps: 319, AckPps: 310, SynAckBps: 319, SynAckPps: 312, IcmpBps: 367, IcmpPps: 386, SmallPps: 378, NtpPps: 36, NtpBps: 378, DNSQueryPps: 371, DNSQueryBps: 369, DNSAnswerPps: 314, DNSAnswerBps: 38, SsdpBps: 398, SsdpPps: 381, UDPPps: 325, UDPBps: 314, QPS: 369, ReceiveCount: 31, IPType: 369, Monitor: &monitor30, Product: &product30, Host: &host30}
 var spectrumDataIDs = []int{}
 
 // Create SpectrumData test case
@@ -28,26 +28,26 @@ func TestCreateSpectrumData(t *testing.T) {
 // CreateBulk SpectrumData test case
 // 批量创建
 func TestCreateBulkSpectrumData(t *testing.T) {
-	monitor1 := "aMEQvdhudy"
-	product1 := "mncVnRxvue"
-	host1 := "WYbeUmWDKG"
-	bulkData1 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "bkRZsIqnbb", Time: time.Now().AddDate(0, 0, -1), DataType: 376, Bps: 323, Pps: 312, SynBps: 369, SynPps: 310, AckBps: 316, AckPps: 348, SynAckBps: 321, SynAckPps: 395, IcmpBps: 316, IcmpPps: 355, SmallPps: 327, NtpPps: 355, NtpBps: 378, DNSQueryPps: 30, DNSQueryBps: 319, DNSAnswerPps: 386, DNSAnswerBps: 33, SsdpBps: 331, SsdpPps: 322, UDPPps: 343, UDPBps: 347, QPS: 372, ReceiveCount: 318, IPType: 387, Monitor: &monitor1, Product: &product1, Host: &host1}
-	monitor2 := "hiLGqVpzXA"
-	product2 := "gQpoWgQqaP"
-	host2 := "LIEbgciYBV"
-	bulkData2 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "NkrxxPIjiP", Time: time.Now().AddDate(0, 0, -1), DataType: 347, Bps: 395, Pps: 354, SynBps: 318, SynPps: 350, AckBps: 354, AckPps: 399, SynAckBps: 310, SynAckPps: 31, IcmpBps: 354, IcmpPps: 317, SmallPps: 378, NtpPps: 343, NtpBps: 315, DNSQueryPps: 322, DNSQueryBps: 339, DNSAnswerPps: 319, DNSAnswerBps: 322, SsdpBps: 363, SsdpPps: 334, UDPPps: 331, UDPBps: 339, QPS: 32, ReceiveCount: 329, IPType: 382, Monitor: &monitor2, Product: &product2, Host: &host2}
-	monitor3 := "twatTEBgPb"
-	product3 := "aNAIIdboAI"
-	host3 := "mpGGTGQZYa"
-	bulkData3 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "rjRVNWgrTu", Time: time.Now().AddDate(0, 0, -1), DataType: 354, Bps: 311, Pps: 374, SynBps: 329, SynPps: 394, AckBps: 313, AckPps: 317, SynAckBps: 37, SynAckPps: 370, IcmpBps: 39, IcmpPps: 337, SmallPps: 359, NtpPps: 368, NtpBps: 382, DNSQueryPps: 392, DNSQueryBps: 348, DNSAnswerPps: 322, DNSAnswerBps: 352, SsdpBps: 38, SsdpPps: 361, UDPPps: 362, UDPBps: 354, QPS: 312, ReceiveCount: 362, IPType: 372, Monitor: &monitor3, Product: &product3, Host: &host3}
-	monitor4 := "uTAwjildLr"
-	product4 := "SDZjimjtZE"
-	host4 := "qMOsBtRyyL"
-	bulkData4 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "wiJEDPcmJW", Time: time.Now().AddDate(0, 0, -1), DataType: 399, Bps: 336, Pps: 327, SynBps: 37, SynPps: 38, AckBps: 34, AckPps: 395, SynAckBps: 388, SynAckPps: 383, IcmpBps: 336, IcmpPps: 371, SmallPps: 395, NtpPps: 328, NtpBps: 350, DNSQueryPps: 324, DNSQueryBps: 310, DNSAnswerPps: 355, DNSAnswerBps: 344, SsdpBps: 360, SsdpPps: 387, UDPPps: 311, UDPBps: 367, QPS: 311, ReceiveCount: 398, IPType: 315, Monitor: &monitor4, Product: &product4, Host: &host4}
-	monitor5 := "rNzLFMVXAq"
-	product5 := "GlrXepJDwW"
-	host5 := "epTGiLgaGf"
-	bulkData5 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "hxPxdEDppx", Time: time.Now().AddDate(0, 0, -1), DataType: 382, Bps: 322, Pps: 367, SynBps: 321, SynPps: 31, AckBps: 398, AckPps: 397, SynAckBps: 345, SynAckPps: 328, IcmpBps: 370, IcmpPps: 358, SmallPps: 354, NtpPps: 393, NtpBps: 389, DNSQueryPps: 36, DNSQueryBps: 317, DNSAnswerPps: 356, DNSAnswerBps: 341, SsdpBps: 385, SsdpPps: 358, UDPPps: 34, UDPBps: 345, QPS: 344, ReceiveCount: 348, IPType: 385, Monitor: &monitor5, Product: &product5, Host: &host5}
+	monitor1 := "GzxpycgLWh"
+	product1 := "ChoSnXXVMW"
+	host1 := "sgGLUDPxoz"
+	bulkData1 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "rwWSMTqryC", Time: time.Now().AddDate(0, 0, -1), DataType: 356, Bps: 373, Pps: 324, SynBps: 363, SynPps: 344, AckBps: 363, AckPps: 375, SynAckBps: 377, SynAckPps: 335, IcmpBps: 340, IcmpPps: 319, SmallPps: 335, NtpPps: 359, NtpBps: 324, DNSQueryPps: 347, DNSQueryBps: 379, DNSAnswerPps: 311, DNSAnswerBps: 343, SsdpBps: 398, SsdpPps: 390, UDPPps: 397, UDPBps: 353, QPS: 323, ReceiveCount: 391, IPType: 346, Monitor: &monitor1, Product: &product1, Host: &host1}
+	monitor2 := "JQtnWQNSCp"
+	product2 := "gZkEfGFRXX"
+	host2 := "kuhIgaACro"
+	bulkData2 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "pyNEqLcTBG", Time: time.Now().AddDate(0, 0, -1), DataType: 326, Bps: 314, Pps: 334, SynBps: 349, SynPps: 341, AckBps: 368, AckPps: 333, SynAckBps: 353, SynAckPps: 389, IcmpBps: 352, IcmpPps: 324, SmallPps: 395, NtpPps: 322, NtpBps: 39, DNSQueryPps: 340, DNSQueryBps: 397, DNSAnswerPps: 364, DNSAnswerBps: 384, SsdpBps: 356, SsdpPps: 327, UDPPps: 352, UDPBps: 323, QPS: 391, ReceiveCount: 354, IPType: 311, Monitor: &monitor2, Product: &product2, Host: &host2}
+	monitor3 := "VJVTuchHfn"
+	product3 := "RiHRVyFAiT"
+	host3 := "tzVWEtdvqM"
+	bulkData3 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "HKtZJihyfN", Time: time.Now().AddDate(0, 0, -1), DataType: 33, Bps: 34, Pps: 351, SynBps: 327, SynPps: 326, AckBps: 383, AckPps: 346, SynAckBps: 382, SynAckPps: 365, IcmpBps: 313, IcmpPps: 37, SmallPps: 354, NtpPps: 322, NtpBps: 349, DNSQueryPps: 325, DNSQueryBps: 345, DNSAnswerPps: 380, DNSAnswerBps: 318, SsdpBps: 348, SsdpPps: 393, UDPPps: 31, UDPBps: 340, QPS: 320, ReceiveCount: 349, IPType: 398, Monitor: &monitor3, Product: &product3, Host: &host3}
+	monitor4 := "nFRJFmoUkJ"
+	product4 := "BsStCQSYYR"
+	host4 := "nfCyZGXVHO"
+	bulkData4 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "htFPqLXZxi", Time: time.Now().AddDate(0, 0, -1), DataType: 324, Bps: 328, Pps: 320, SynBps: 376, SynPps: 355, AckBps: 351, AckPps: 338, SynAckBps: 382, SynAckPps: 30, IcmpBps: 376, IcmpPps: 328, SmallPps: 327, NtpPps: 396, NtpBps: 336, DNSQueryPps: 312, DNSQueryBps: 399, DNSAnswerPps: 394, DNSAnswerBps: 383, SsdpBps: 32, SsdpPps: 330, UDPPps: 32, UDPBps: 342, QPS: 338, ReceiveCount: 32, IPType: 341, Monitor: &monitor4, Product: &product4, Host: &host4}
+	monitor5 := "WZYeUTzEwa"
+	product5 := "jofwzpWXMl"
+	host5 := "jhDWKkHJRm"
+	bulkData5 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "fDdMJSBmMP", Time: time.Now().AddDate(0, 0, -1), DataType: 32, Bps: 363, Pps: 310, SynBps: 382, SynPps: 326, AckBps: 384, AckPps: 348, SynAckBps: 321, SynAckPps: 393, IcmpBps: 321, IcmpPps: 39, SmallPps: 393, NtpPps: 337, NtpBps: 341, DNSQueryPps: 343, DNSQueryBps: 34, DNSAnswerPps: 316, DNSAnswerBps: 380, SsdpBps: 30, SsdpPps: 384, UDPPps: 355, UDPBps: 327, QPS: 364, ReceiveCount: 368, IPType: 319, Monitor: &monitor5, Product: &product5, Host: &host5}
 	bulkDatas := [...]ent.SpectrumData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.IP
 	testCase := &CaseRule{Api: spectrumDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -101,10 +101,10 @@ func TestQuerySearchSpectrumDataIP(t *testing.T) {
 // UpdateByID SpectrumData test case
 // 根据 ID 修改
 func TestUpdateByIDSpectrumData(t *testing.T) {
-	monitor19 := "UknHrBeYIW"
-	product19 := "zvHhacfAqv"
-	host19 := "BJegUabALE"
-	updateData := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "bPZtdYdOnc", Time: time.Now().AddDate(0, 0, -1), DataType: 364, Bps: 369, Pps: 327, SynBps: 380, SynPps: 332, AckBps: 375, AckPps: 384, SynAckBps: 372, SynAckPps: 363, IcmpBps: 389, IcmpPps: 395, SmallPps: 390, NtpPps: 38, NtpBps: 343, DNSQueryPps: 370, DNSQueryBps: 323, DNSAnswerPps: 347, DNSAnswerBps: 323, SsdpBps: 339, SsdpPps: 394, UDPPps: 359, UDPBps: 383, QPS: 314, ReceiveCount: 310, IPType: 334, Monitor: &monitor19, Product: &product19, Host: &host19}
+	monitor30 := "xBauHytVpi"
+	product30 := "GcJqnBAoUr"
+	host30 := "VwIVgcgEpi"
+	updateData := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "ykUwxwSIzw", Time: time.Now().AddDate(0, 0, -1), DataType: 390, Bps: 367, Pps: 359, SynBps: 350, SynPps: 330, AckBps: 367, AckPps: 322, SynAckBps: 339, SynAckPps: 377, IcmpBps: 363, IcmpPps: 365, SmallPps: 319, NtpPps: 390, NtpBps: 32, DNSQueryPps: 375, DNSQueryBps: 394, DNSAnswerPps: 338, DNSAnswerBps: 398, SsdpBps: 346, SsdpPps: 330, UDPPps: 325, UDPBps: 376, QPS: 373, ReceiveCount: 377, IPType: 384, Monitor: &monitor30, Product: &product30, Host: &host30}
 	successExpectedResult.ResponseData = updateData.IP
 	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(spectrumDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/strategy_test.go b/api/test/strategy_test.go
index 4a693db..31e69ae 100644
--- a/api/test/strategy_test.go
+++ b/api/test/strategy_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/strategy"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/strategy"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var strategyApi = baseApi + "/strategy"
-var remark25 = "WYKVLeJzfa"
-var testStrategy = &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, Name: "fniZIVwdGl", Type: 337, Bps: 365, Pps: 372, BpsCount: 392, PpsCount: 368, IspCode: 380}
+var remark31 = "kNBCGjDiMq"
+var testStrategy = &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark31, Name: "waYXjYenYR", Type: 38, Bps: 315, Pps: 344, BpsCount: 355, PpsCount: 368, IspCode: 311}
 var strategyIDs = []int{}
 
 // Create Strategy test case
@@ -26,16 +26,16 @@ func TestCreateStrategy(t *testing.T) {
 // CreateBulk Strategy test case
 // 批量创建
 func TestCreateBulkStrategy(t *testing.T) {
-	remark1 := "xAYbydbSCI"
-	bulkData1 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "RngyegXVUS", Type: 335, Bps: 396, Pps: 350, BpsCount: 319, PpsCount: 348, IspCode: 347}
-	remark2 := "YthRnBYilL"
-	bulkData2 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "nKmyyuRUNY", Type: 326, Bps: 383, Pps: 335, BpsCount: 315, PpsCount: 370, IspCode: 318}
-	remark3 := "xEujpuexFx"
-	bulkData3 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "wFLgYymnby", Type: 342, Bps: 390, Pps: 358, BpsCount: 312, PpsCount: 375, IspCode: 363}
-	remark4 := "fupdBTaJLN"
-	bulkData4 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "ogKtXQaUCa", Type: 31, Bps: 366, Pps: 357, BpsCount: 369, PpsCount: 383, IspCode: 374}
-	remark5 := "JvujCzSrmU"
-	bulkData5 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "dSqVcPSdWb", Type: 342, Bps: 398, Pps: 369, BpsCount: 314, PpsCount: 35, IspCode: 374}
+	remark1 := "sYIaFlLNFF"
+	bulkData1 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "mgGrqUjcQC", Type: 324, Bps: 335, Pps: 379, BpsCount: 382, PpsCount: 379, IspCode: 386}
+	remark2 := "uwzmWAalew"
+	bulkData2 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "pGbqZJYeLb", Type: 354, Bps: 377, Pps: 310, BpsCount: 316, PpsCount: 319, IspCode: 357}
+	remark3 := "lJplRzDfhR"
+	bulkData3 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "wIJspOEPfb", Type: 397, Bps: 374, Pps: 319, BpsCount: 345, PpsCount: 333, IspCode: 370}
+	remark4 := "ppLDblZEdN"
+	bulkData4 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "qLFrhdqWXs", Type: 383, Bps: 350, Pps: 353, BpsCount: 325, PpsCount: 373, IspCode: 382}
+	remark5 := "bXGfpNAkXA"
+	bulkData5 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "sxiuYyIqOi", Type: 313, Bps: 373, Pps: 38, BpsCount: 380, PpsCount: 392, IspCode: 314}
 	bulkDatas := [...]ent.Strategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: strategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchStrategyName(t *testing.T) {
 // UpdateByID Strategy test case
 // 根据 ID 修改
 func TestUpdateByIDStrategy(t *testing.T) {
-	remark25 := "yGziOaFxGP"
-	updateData := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, Name: "EwQFATkbhD", Type: 35, Bps: 373, Pps: 38, BpsCount: 370, PpsCount: 376, IspCode: 335}
+	remark31 := "FUVnGRytVp"
+	updateData := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark31, Name: "sygCbieVBQ", Type: 332, Bps: 311, Pps: 320, BpsCount: 356, PpsCount: 33, IspCode: 392}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: strategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(strategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/system_api_test.go b/api/test/system_api_test.go
index a67452c..da78eb4 100644
--- a/api/test/system_api_test.go
+++ b/api/test/system_api_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/systemapi"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/systemapi"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var systemApiApi = baseApi + "/systemapi"
-var remark20 = "cErpbdsbCU"
-var testSystemApi = &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "eTLoaQhVCG", Path: "AwfLAWGgqe", HTTPMethod: "bTsUbVJqqC"}
+var remark32 = "PRxsuisxce"
+var testSystemApi = &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark32, Name: "nATNbeiORv", Path: "cRFQUrNPqg", HTTPMethod: "kIodGjGpoy"}
 var systemApiIDs = []int{}
 
 // Create SystemApi test case
@@ -26,16 +26,16 @@ func TestCreateSystemApi(t *testing.T) {
 // CreateBulk SystemApi test case
 // 批量创建
 func TestCreateBulkSystemApi(t *testing.T) {
-	remark1 := "RuUFrtXPVI"
-	bulkData1 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "QgRUEdfwsc", Path: "tqNTSsKLTz", HTTPMethod: "LRMQtwAmxd"}
-	remark2 := "PayhFmufSe"
-	bulkData2 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "VyVcCGQKMC", Path: "WyiDDcpCny", HTTPMethod: "DgIuqTMLKk"}
-	remark3 := "FemWBqwgJV"
-	bulkData3 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "iuAWaryaPM", Path: "jWQssKUGMA", HTTPMethod: "TSuFguPQHc"}
-	remark4 := "wSXmIeYSIF"
-	bulkData4 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "IeTbZBQKWe", Path: "FNQszlnMdy", HTTPMethod: "DiMjzbSRuL"}
-	remark5 := "kTBynNFDJB"
-	bulkData5 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "oyztdikNqs", Path: "ywBeAitGKH", HTTPMethod: "PbyyVCXyhq"}
+	remark1 := "VRDfvxgWJw"
+	bulkData1 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "NlVIhYxWyv", Path: "RsTNxVDLWG", HTTPMethod: "BJYJBOxjrz"}
+	remark2 := "gMSwFNvAEV"
+	bulkData2 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "TFqHxdtMcs", Path: "GrNXFLPVtN", HTTPMethod: "qBCsnaNfhB"}
+	remark3 := "vIXueNJYjD"
+	bulkData3 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "hRGGkXFTAm", Path: "XQGcoGoDJd", HTTPMethod: "fLXvTCGBMZ"}
+	remark4 := "mDOMMgxYFr"
+	bulkData4 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "ccHWtWvTfI", Path: "oHwqUtQLjM", HTTPMethod: "xGWCNyQteI"}
+	remark5 := "MRegxxSmOS"
+	bulkData5 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "ZLJqBBfqvk", Path: "PTcABNHYIq", HTTPMethod: "lNQDYonhCT"}
 	bulkDatas := [...]ent.SystemApi{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: systemApiApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchSystemApiName(t *testing.T) {
 // UpdateByID SystemApi test case
 // 根据 ID 修改
 func TestUpdateByIDSystemApi(t *testing.T) {
-	remark20 := "TqctFacAXr"
-	updateData := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "GhcTWKShNY", Path: "hdGYSCyUpY", HTTPMethod: "axuNoJLIzd"}
+	remark32 := "DIWpetvHFs"
+	updateData := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark32, Name: "PQGzoFSWlH", Path: "tUxnnGMfbW", HTTPMethod: "qlncSMrhOs"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "PUT", UrlData: strconv.Itoa(systemApiIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/system_config_test.go b/api/test/system_config_test.go
index 9355440..063340d 100644
--- a/api/test/system_config_test.go
+++ b/api/test/system_config_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/systemconfig"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/systemconfig"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var systemConfigApi = baseApi + "/systemconfig"
-var remark24 = "AShuqHdKbu"
-var testSystemConfig = &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark24, WofangTestIP: "vidWdsFhno"}
+var remark25 = "RRXteYkEyn"
+var testSystemConfig = &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, WofangTestIP: "zSoeMODIQK"}
 var systemConfigIDs = []int{}
 
 // Create SystemConfig test case
@@ -26,16 +26,16 @@ func TestCreateSystemConfig(t *testing.T) {
 // CreateBulk SystemConfig test case
 // 批量创建
 func TestCreateBulkSystemConfig(t *testing.T) {
-	remark1 := "cwGmFbyfyM"
-	bulkData1 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, WofangTestIP: "hDmVEIbeDi"}
-	remark2 := "TMBEltwDuK"
-	bulkData2 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, WofangTestIP: "twPhpofLYI"}
-	remark3 := "njlvhOHfyu"
-	bulkData3 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, WofangTestIP: "eeHNNSDrru"}
-	remark4 := "PCGCgxlHsg"
-	bulkData4 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, WofangTestIP: "HJenrqQMUP"}
-	remark5 := "ljvzxLdXSE"
-	bulkData5 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, WofangTestIP: "FaQZfNEPQt"}
+	remark1 := "AWPiVtJbKR"
+	bulkData1 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, WofangTestIP: "hWUAOZfYqO"}
+	remark2 := "uWuiQCoiks"
+	bulkData2 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, WofangTestIP: "zMYjpOkvKx"}
+	remark3 := "onsdqqwTid"
+	bulkData3 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, WofangTestIP: "HqqMuhgnxP"}
+	remark4 := "WSUDwwNWDw"
+	bulkData4 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, WofangTestIP: "GHjkvSRWcc"}
+	remark5 := "VmbPygjNUL"
+	bulkData5 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, WofangTestIP: "dFpsrVOoMC"}
 	bulkDatas := [...]ent.SystemConfig{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.WofangTestIP
 	testCase := &CaseRule{Api: systemConfigApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchSystemConfigWofangTestIP(t *testing.T) {
 // UpdateByID SystemConfig test case
 // 根据 ID 修改
 func TestUpdateByIDSystemConfig(t *testing.T) {
-	remark24 := "KoDpivCDwP"
-	updateData := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark24, WofangTestIP: "mNmLCsflOf"}
+	remark25 := "imbfNMRsEK"
+	updateData := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, WofangTestIP: "TFCXdkjKEp"}
 	successExpectedResult.ResponseData = updateData.WofangTestIP
 	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "PUT", UrlData: strconv.Itoa(systemConfigIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/tenant_test.go b/api/test/tenant_test.go
index b0941f5..748289b 100644
--- a/api/test/tenant_test.go
+++ b/api/test/tenant_test.go
@@ -1,15 +1,15 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/tenant"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/tenant"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 )
 
 var tenantApi = baseApi + "/tenant"
-var testTenant = &ent.Tenant{Name: "MNcbXxCNFF", Code: "xmrxYkNiaI"}
+var testTenant = &ent.Tenant{Name: "ZiByzpHtnX", Code: "vnfcaUKwTD"}
 var tenantIDs = []int{}
 
 // Create Tenant test case
@@ -24,11 +24,11 @@ func TestCreateTenant(t *testing.T) {
 // CreateBulk Tenant test case
 // 批量创建
 func TestCreateBulkTenant(t *testing.T) {
-	bulkData1 := &ent.Tenant{Name: "POBVEWQSdg", Code: "RRyEMbJfcy"}
-	bulkData2 := &ent.Tenant{Name: "mZQrRNmxZa", Code: "sqgJAsuenQ"}
-	bulkData3 := &ent.Tenant{Name: "viGKqIXkqQ", Code: "eOrdvQyzKF"}
-	bulkData4 := &ent.Tenant{Name: "AKtVHMDuJj", Code: "ORrqGlIoMM"}
-	bulkData5 := &ent.Tenant{Name: "iFfYKsfTLf", Code: "xvncXuVpqR"}
+	bulkData1 := &ent.Tenant{Name: "QwSAzjOUve", Code: "KyHsBaJjCH"}
+	bulkData2 := &ent.Tenant{Name: "jpWwZZPMGJ", Code: "eRYvrfeBZf"}
+	bulkData3 := &ent.Tenant{Name: "BWxUWoiPww", Code: "CUYZAmbXKc"}
+	bulkData4 := &ent.Tenant{Name: "TReYtFbeXf", Code: "GDcouuGmMw"}
+	bulkData5 := &ent.Tenant{Name: "zvviASKmGb", Code: "fHTVSUqYQX"}
 	bulkDatas := [...]ent.Tenant{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: tenantApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -82,7 +82,7 @@ func TestQuerySearchTenantName(t *testing.T) {
 // UpdateByID Tenant test case
 // 根据 ID 修改
 func TestUpdateByIDTenant(t *testing.T) {
-	updateData := &ent.Tenant{Name: "WaXZgcTXRk", Code: "ZuOinzHQtD"}
+	updateData := &ent.Tenant{Name: "muYVStNlqc", Code: "dTupxCZObS"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: tenantApi, HttpMethod: "PUT", UrlData: strconv.Itoa(tenantIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/user_operation_log_test.go b/api/test/user_operation_log_test.go
index db3345c..192ed22 100644
--- a/api/test/user_operation_log_test.go
+++ b/api/test/user_operation_log_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/useroperationlog"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/useroperationlog"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var userOperationLogApi = baseApi + "/useroperationlog"
-var remark18 = "FoXGMRUcgF"
-var testUserOperationLog = &ent.UserOperationLog{Remark: &remark18, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "YECnxhGoSV", Method: "cULTFhoJES", URI: "uhPRAxPqIR", RequestBody: "WPZecSSnTB", Project: "zrTuBOevwA"}
+var remark34 = "AzdSZFrkYB"
+var testUserOperationLog = &ent.UserOperationLog{Remark: &remark34, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "cpiAHrsYmX", Method: "RyKIwYRYXk", URI: "YwqSSrymMH", RequestBody: "uProFHjcfl", Project: "ZTDJDCAEYN"}
 var userOperationLogIDs = []int{}
 
 // Create UserOperationLog test case
@@ -26,16 +26,16 @@ func TestCreateUserOperationLog(t *testing.T) {
 // CreateBulk UserOperationLog test case
 // 批量创建
 func TestCreateBulkUserOperationLog(t *testing.T) {
-	remark1 := "sWyoduqvAJ"
-	bulkData1 := &ent.UserOperationLog{Remark: &remark1, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "zOAbfDKsFh", Method: "xMKYfStAlO", URI: "pXscDwahvg", RequestBody: "jbsWiQvHMN", Project: "VnnSEykGjI"}
-	remark2 := "QDxJvgsPMH"
-	bulkData2 := &ent.UserOperationLog{Remark: &remark2, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "XSkLnCTgxQ", Method: "fqDoLQkQFo", URI: "ctPcfjFIUc", RequestBody: "HbyJPPUpoM", Project: "BPTEWjigtT"}
-	remark3 := "SkXBWYNPMz"
-	bulkData3 := &ent.UserOperationLog{Remark: &remark3, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "FIVMYAihic", Method: "HNqdtrCzzN", URI: "WbPbuZOQrX", RequestBody: "sSXZvNRIay", Project: "jntKvkjbNj"}
-	remark4 := "nElDiPmFwI"
-	bulkData4 := &ent.UserOperationLog{Remark: &remark4, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "QjFapquOQe", Method: "VwCMfATuIq", URI: "FDZlQyTPeJ", RequestBody: "radGpLzVkD", Project: "IfdiTzjdWA"}
-	remark5 := "GZJVmjBukV"
-	bulkData5 := &ent.UserOperationLog{Remark: &remark5, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "oFtZJOKmgJ", Method: "lLufeKosIC", URI: "CelxPBqrwy", RequestBody: "FxlbYuWLNn", Project: "fJuLiPFkfv"}
+	remark1 := "CvOXXMfTyb"
+	bulkData1 := &ent.UserOperationLog{Remark: &remark1, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "jlRHPGLtdS", Method: "uOWalxMMxd", URI: "EpfjjydAkj", RequestBody: "HmjRSJFzNW", Project: "WgPjEIOTyR"}
+	remark2 := "udyavfhitC"
+	bulkData2 := &ent.UserOperationLog{Remark: &remark2, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "vzNuBHHanS", Method: "bERcYGxDwc", URI: "GNshJmCwlr", RequestBody: "XEsLWsHxmK", Project: "rjFylWmkfn"}
+	remark3 := "kPanAVefJu"
+	bulkData3 := &ent.UserOperationLog{Remark: &remark3, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "iFmqIxwWqm", Method: "ZkYanoKMhJ", URI: "OcLUdSAhFP", RequestBody: "PcuokvAqWI", Project: "LgJloyPpcq"}
+	remark4 := "LMJScGgPCi"
+	bulkData4 := &ent.UserOperationLog{Remark: &remark4, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "IpCotJbIaj", Method: "UEAskFJZNC", URI: "ACKhpswlxq", RequestBody: "qwuWsJMnnc", Project: "tGCFnbWRsy"}
+	remark5 := "YVxlfQYIWH"
+	bulkData5 := &ent.UserOperationLog{Remark: &remark5, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "icgCuxnHhI", Method: "RZdDijidEI", URI: "dMyUgiobot", RequestBody: "rEakoVHMJi", Project: "IrMbUiAONL"}
 	bulkDatas := [...]ent.UserOperationLog{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Username
 	testCase := &CaseRule{Api: userOperationLogApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchUserOperationLogUsername(t *testing.T) {
 // UpdateByID UserOperationLog test case
 // 根据 ID 修改
 func TestUpdateByIDUserOperationLog(t *testing.T) {
-	remark18 := "CrWyEkyNTX"
-	updateData := &ent.UserOperationLog{Remark: &remark18, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "RFYFGSpRBK", Method: "FASyWolauK", URI: "BPiuVTdiCu", RequestBody: "ikChhTUtjp", Project: "EtVTlKFcDn"}
+	remark34 := "uYotXKoaHP"
+	updateData := &ent.UserOperationLog{Remark: &remark34, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "PNtnkJwAwy", Method: "XJzcdZeEfN", URI: "YrGsxyaQFB", RequestBody: "VihNcUzKEC", Project: "jkDPYiUuIs"}
 	successExpectedResult.ResponseData = updateData.Username
 	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "PUT", UrlData: strconv.Itoa(userOperationLogIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/user_test.go b/api/test/user_test.go
index 23609ee..1175d6a 100644
--- a/api/test/user_test.go
+++ b/api/test/user_test.go
@@ -1,16 +1,16 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/user"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/user"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var userApi = baseApi + "/user"
-var testUser = &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "daJlmeNIMA", Password: "kJzglYLfny"}
+var testUser = &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "vfnywlgRcy", Password: "cPHUsvntyO"}
 var userIDs = []int{}
 
 // Create User test case
@@ -25,11 +25,11 @@ func TestCreateUser(t *testing.T) {
 // CreateBulk User test case
 // 批量创建
 func TestCreateBulkUser(t *testing.T) {
-	bulkData1 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "YpxhWaxSGl", Password: "vmqRNKQIGh"}
-	bulkData2 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "gSgFBLNulp", Password: "YBdFAvMpDB"}
-	bulkData3 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "FWLQcyFkDV", Password: "lBPOBmDGsw"}
-	bulkData4 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "namsncbTDv", Password: "yjlhGwAAGN"}
-	bulkData5 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "UPkFTOMYrg", Password: "PlyowqqSsT"}
+	bulkData1 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "BeMyExPmDu", Password: "EAEggQCjRU"}
+	bulkData2 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "YrqJRQbBfc", Password: "MEUjhIKEcP"}
+	bulkData3 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "WlegEdNrVc", Password: "JkknHlkNek"}
+	bulkData4 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "vgCzzOVilr", Password: "UnLsGhfzuH"}
+	bulkData5 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "XhCqymrGtW", Password: "eeBbipJebp"}
 	bulkDatas := [...]ent.User{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: userApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -83,7 +83,7 @@ func TestQuerySearchUserName(t *testing.T) {
 // UpdateByID User test case
 // 根据 ID 修改
 func TestUpdateByIDUser(t *testing.T) {
-	updateData := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "llrDImGcMa", Password: "nXURMdfhCl"}
+	updateData := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "HGqMLJloBW", Password: "YUwaEHiHWd"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: userApi, HttpMethod: "PUT", UrlData: strconv.Itoa(userIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/wofang_alert_test.go b/api/test/wofang_alert_test.go
index d1e958a..fd5e691 100644
--- a/api/test/wofang_alert_test.go
+++ b/api/test/wofang_alert_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/wofangalert"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/wofangalert"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var wofangAlertApi = baseApi + "/wofangalert"
-var remark33 = "oFikkFqZqx"
-var testWofangAlert = &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark33, AttackStatus: 315, DeviceIP: "UHAmWOSYUZ", ZoneIP: "UikHwxtBMf", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 355, MaxInBps: 374}
+var remark10 = "ClmkglGGKb"
+var testWofangAlert = &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, AttackStatus: 321, DeviceIP: "cSMDzlkYEV", ZoneIP: "FtPMzmSgcs", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 372, MaxInBps: 393}
 var wofangAlertIDs = []int{}
 
 // Create WofangAlert test case
@@ -26,16 +26,16 @@ func TestCreateWofangAlert(t *testing.T) {
 // CreateBulk WofangAlert test case
 // 批量创建
 func TestCreateBulkWofangAlert(t *testing.T) {
-	remark1 := "SxohKbXrcF"
-	bulkData1 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, AttackStatus: 318, DeviceIP: "hAVGgrmbDC", ZoneIP: "zRpuoLAQuq", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 383, MaxInBps: 323}
-	remark2 := "lfrrxaZxdS"
-	bulkData2 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, AttackStatus: 354, DeviceIP: "UtUwrPrCAz", ZoneIP: "benatwbjnQ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 338, MaxInBps: 37}
-	remark3 := "qASGOrLMhf"
-	bulkData3 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, AttackStatus: 375, DeviceIP: "YJCaJxxsaq", ZoneIP: "GvMBvjtKse", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 354, MaxInBps: 39}
-	remark4 := "CKliTvxVcv"
-	bulkData4 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, AttackStatus: 386, DeviceIP: "nblhMkUpbr", ZoneIP: "VXvHRlIBbz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 37, MaxInBps: 352}
-	remark5 := "bjwVqqIput"
-	bulkData5 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, AttackStatus: 358, DeviceIP: "VOVgRCurUG", ZoneIP: "cGpfDDbjYn", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 320, MaxInBps: 36}
+	remark1 := "AVWhceAmpC"
+	bulkData1 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, AttackStatus: 363, DeviceIP: "tcSsUulDyX", ZoneIP: "MvFVaCNDyO", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 384, MaxInBps: 357}
+	remark2 := "YMprDIPcHv"
+	bulkData2 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, AttackStatus: 37, DeviceIP: "Sgokwrefxr", ZoneIP: "RnTbVFONDn", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 344, MaxInBps: 323}
+	remark3 := "TzoxgvvZtC"
+	bulkData3 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, AttackStatus: 310, DeviceIP: "vDHTnBYbCz", ZoneIP: "vESWELzxZl", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 327, MaxInBps: 38}
+	remark4 := "uMtJWTlyhi"
+	bulkData4 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, AttackStatus: 363, DeviceIP: "WXqpxhobrh", ZoneIP: "PetboonMXC", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 330, MaxInBps: 331}
+	remark5 := "wFOQUwqFAg"
+	bulkData5 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, AttackStatus: 345, DeviceIP: "tBlDAdHRPl", ZoneIP: "FnoUBPntPK", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 338, MaxInBps: 389}
 	bulkDatas := [...]ent.WofangAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.DeviceIP
 	testCase := &CaseRule{Api: wofangAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchWofangAlertDeviceIP(t *testing.T) {
 // UpdateByID WofangAlert test case
 // 根据 ID 修改
 func TestUpdateByIDWofangAlert(t *testing.T) {
-	remark33 := "NVJXNvvSwE"
-	updateData := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark33, AttackStatus: 36, DeviceIP: "ebSMWdqhQZ", ZoneIP: "KdheEyIrtc", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 373, MaxInBps: 337}
+	remark10 := "sZOaUHbeoQ"
+	updateData := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, AttackStatus: 334, DeviceIP: "ICuedvHCSJ", ZoneIP: "nNwrpLMopX", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 329, MaxInBps: 365}
 	successExpectedResult.ResponseData = updateData.DeviceIP
 	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/test/wofang_test.go b/api/test/wofang_test.go
index a206a73..b3e6d61 100644
--- a/api/test/wofang_test.go
+++ b/api/test/wofang_test.go
@@ -1,17 +1,17 @@
 package test
 
 import (
-	"meta/app/ent"
-	"meta/app/ent/wofang"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/wofang"
+	"github.com/one-meta/meta/pkg/common"
 	"strconv"
 	"testing"
 	"time"
 )
 
 var wofangApi = baseApi + "/wofang"
-var remark21 = "IMHiXgMTWd"
-var testWofang = &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "qlYIqOpzdm", IP: "UHWlqwuDsu", Type: "gQMtDGcntR", UnDragSecond: 344, ErrorInfo: "IZachRXFOa", Status: "OzVDuDAXuQ"}
+var remark17 = "elsqlsHnev"
+var testWofang = &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark17, Name: "nPEPlXIuHf", IP: "WiUGLMfrOd", Type: "FqPLwbyrBg", UnDragSecond: 397, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "nBmynsgaUk", Status: "phBeKvbFaQ"}
 var wofangIDs = []int{}
 
 // Create Wofang test case
@@ -26,16 +26,16 @@ func TestCreateWofang(t *testing.T) {
 // CreateBulk Wofang test case
 // 批量创建
 func TestCreateBulkWofang(t *testing.T) {
-	remark1 := "WlAtbiZEss"
-	bulkData1 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "vYQrBonLLU", IP: "iBKhVOaJed", Type: "yPalgXmLTv", UnDragSecond: 386, ErrorInfo: "EpBzNnVQuW", Status: "RjObkMbktJ"}
-	remark2 := "NTjTHeADSn"
-	bulkData2 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "fVXtxgtkfM", IP: "foBPJFHBcd", Type: "YMInWZrwyb", UnDragSecond: 32, ErrorInfo: "qHIsslWtUu", Status: "UaZzbTfjIy"}
-	remark3 := "WwHpzLXVkt"
-	bulkData3 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "IhZnVcjBEj", IP: "vZORaEFhhk", Type: "ujUCUrJsnJ", UnDragSecond: 386, ErrorInfo: "KluztODoTW", Status: "frCyLoErGG"}
-	remark4 := "OovswKrIIs"
-	bulkData4 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "IrXXMfZcbv", IP: "fJTNoktSQj", Type: "vfwuTlZJKV", UnDragSecond: 390, ErrorInfo: "YIEeLwcavh", Status: "fCDqqsbYUQ"}
-	remark5 := "bekKJRsrxR"
-	bulkData5 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "IaNXGydeAg", IP: "tpCCgReNdU", Type: "rXqLgNGbas", UnDragSecond: 374, ErrorInfo: "vbwEmotxzG", Status: "claZNgxzAl"}
+	remark1 := "xVmOeMphRc"
+	bulkData1 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "FmDwMvBYZG", IP: "wNFJuVJSbe", Type: "Xdjlakkmvr", UnDragSecond: 396, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "wqqJEXwuqR", Status: "wxFViGxqRR"}
+	remark2 := "aLObdCKrvZ"
+	bulkData2 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "iOVMRBXoZA", IP: "LyLMoUqXbd", Type: "KzsEfRuhuh", UnDragSecond: 376, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "bOPgZtonzd", Status: "lGHFLsAYZW"}
+	remark3 := "tmbSJANJWF"
+	bulkData3 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "jKqEAHKBeU", IP: "ZaokiboWIe", Type: "TWnkYGCRGU", UnDragSecond: 33, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "EPKkbWNpwi", Status: "GFznLZbLEs"}
+	remark4 := "wavauaXXwE"
+	bulkData4 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "CrwZWkBTqu", IP: "jpHpftsEdt", Type: "qnRMsFXYEz", UnDragSecond: 364, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "lultxBEaHX", Status: "MjSjnUGBfX"}
+	remark5 := "AMfGHKkGpF"
+	bulkData5 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "yKhjnPcTzq", IP: "NqIMXNHuAk", Type: "kiZMoQvxTx", UnDragSecond: 386, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "TQrmylABKP", Status: "HpCfCndcxo"}
 	bulkDatas := [...]ent.Wofang{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
 	successExpectedResult.ResponseData = bulkData1.Name
 	testCase := &CaseRule{Api: wofangApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
@@ -89,8 +89,8 @@ func TestQuerySearchWofangName(t *testing.T) {
 // UpdateByID Wofang test case
 // 根据 ID 修改
 func TestUpdateByIDWofang(t *testing.T) {
-	remark21 := "pryPLvwcNF"
-	updateData := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "PJOiWwokFC", IP: "MYtjcXAyXK", Type: "OwAaNVWIdI", UnDragSecond: 326, ErrorInfo: "TyUfavOaIY", Status: "UeUNaBERbr"}
+	remark17 := "rARNMhEbeh"
+	updateData := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark17, Name: "rsIOHblMJD", IP: "zOYOCQxNLR", Type: "wVyNHrOVKP", UnDragSecond: 355, StartTime: time.Now().AddDate(0, 0, -1), ErrorInfo: "xhwWGzblxf", Status: "gTlZLOiJsu"}
 	successExpectedResult.ResponseData = updateData.Name
 	testCase := &CaseRule{Api: wofangApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
 	runTest(t, testCase)
diff --git a/api/v1/not_found.go b/api/v1/not_found.go
index 1b6574e..d614617 100644
--- a/api/v1/not_found.go
+++ b/api/v1/not_found.go
@@ -2,7 +2,7 @@ package v1
 
 import (
 	"github.com/gofiber/fiber/v2"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/pkg/common"
 )
 
 // NotFound 404路由
diff --git a/api/v1/private.go b/api/v1/private.go
index ab5c5c4..0f8c29f 100644
--- a/api/v1/private.go
+++ b/api/v1/private.go
@@ -1,9 +1,8 @@
 package v1
 
 import (
-	"meta/app/entity/config"
-
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/entity/config"
 )
 
 // Private 私有路由
@@ -11,85 +10,15 @@ func (r *Router) Private(fa *fiber.App) {
 	var router fiber.Router
 	// dev环境未启用认证授权
 	if !config.CFG.Auth.Enable && config.CFG.Stage.Status == "dev" {
-		router = fa.Group("/api", r.JWT.SaJWT(), r.Gen.SaCtx(), r.Casbinx.SaCasbin())
+		router = fa.Group("/api", r.Gen.SaJWT(), r.Gen.SaCtx(), r.Gen.SaCasbin())
 	} else {
-		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx(), r.OPLog.LogUserOperation())
-
-		// router = fa.Group("/api", r.JWT.AuthJWT(), r.OPLog.LogUserOperation(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx())
-
-		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx(), r.OPLog.LogUserOperation())
-		router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.OPLog.LogUserOperation(), r.Gen.AuthCtx())
-
-		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx())
+		router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx())
 	}
 	v1 := router.Group("/v1")
 
-	// ProtectGroup路由
-	protectGroupRouter := v1.Group("protectgroup")
-	protectGroupRouter.Get("", r.ProtectGroupController.Query)
-	protectGroupRouter.Get(":id", r.Gen.IntId(), r.ProtectGroupController.QueryByID)
-	protectGroupRouter.Post("", r.ProtectGroupController.Create)
-	protectGroupRouter.Post("bulk", r.ProtectGroupController.CreateBulk)
-	protectGroupRouter.Post("bulk/delete", r.ProtectGroupController.DeleteBulk)
-	protectGroupRouter.Put(":id", r.Gen.IntId(), r.ProtectGroupController.UpdateByID)
-	protectGroupRouter.Delete(":id", r.Gen.IntId(), r.ProtectGroupController.DeleteByID)
-
-	// SpectrumData路由
-	spectrumDataRouter := v1.Group("spectrumdata")
-	spectrumDataRouter.Get("", r.SpectrumDataController.Query)
-	spectrumDataRouter.Get(":id", r.Gen.IntId(), r.SpectrumDataController.QueryByID)
-	spectrumDataRouter.Post("", r.SpectrumDataController.Create)
-	spectrumDataRouter.Post("bulk", r.SpectrumDataController.CreateBulk)
-	spectrumDataRouter.Post("bulk/delete", r.SpectrumDataController.DeleteBulk)
-	spectrumDataRouter.Put(":id", r.Gen.IntId(), r.SpectrumDataController.UpdateByID)
-	spectrumDataRouter.Delete(":id", r.Gen.IntId(), r.SpectrumDataController.DeleteByID)
-
-	// Strategy路由
-	strategyRouter := v1.Group("strategy")
-	strategyRouter.Get("", r.StrategyController.Query)
-	strategyRouter.Get(":id", r.Gen.IntId(), r.StrategyController.QueryByID)
-	strategyRouter.Post("", r.StrategyController.Create)
-	strategyRouter.Post("bulk", r.StrategyController.CreateBulk)
-	strategyRouter.Post("bulk/delete", r.StrategyController.DeleteBulk)
-	strategyRouter.Put(":id", r.Gen.IntId(), r.StrategyController.UpdateByID)
-	strategyRouter.Delete(":id", r.Gen.IntId(), r.StrategyController.DeleteByID)
-
-	// User路由
-	userRouter := v1.Group("user")
-	userRouter.Get("info", r.UserController.UserInfo)
-
-	userRouter.Get("", r.UserController.Query)
-	userRouter.Get(":id", r.Gen.IntId(), r.UserController.QueryByID)
-	userRouter.Post("", r.UserController.Create)
-	userRouter.Post("bulk", r.UserController.CreateBulk)
-	userRouter.Post("bulk/delete", r.UserController.DeleteBulk)
-	userRouter.Put(":id", r.Gen.IntId(), r.UserController.UpdateByID)
-	userRouter.Delete(":id", r.Gen.IntId(), r.UserController.DeleteByID)
-
-	// CasbinRule路由
-	casbinRuleRouter := v1.Group("casbinrule")
-	casbinRuleRouter.Get("", r.CasbinRuleController.Query)
-	casbinRuleRouter.Get(":id", r.Gen.IntId(), r.CasbinRuleController.QueryByID)
-	casbinRuleRouter.Post("", r.CasbinRuleController.Create)
-	casbinRuleRouter.Post("bulk", r.CasbinRuleController.CreateBulk)
-	casbinRuleRouter.Post("bulk/delete", r.CasbinRuleController.DeleteBulk)
-	casbinRuleRouter.Put(":id", r.Gen.IntId(), r.CasbinRuleController.UpdateByID)
-	casbinRuleRouter.Delete(":id", r.Gen.IntId(), r.CasbinRuleController.DeleteByID)
-
-	// Notify路由
-	notifyRouter := v1.Group("notify")
-	notifyRouter.Get("", r.NotifyController.Query)
-	notifyRouter.Get(":id", r.Gen.IntId(), r.NotifyController.QueryByID)
-	notifyRouter.Post("", r.NotifyController.Create)
-	notifyRouter.Post("bulk", r.NotifyController.CreateBulk)
-	notifyRouter.Post("bulk/delete", r.NotifyController.DeleteBulk)
-	notifyRouter.Put(":id", r.Gen.IntId(), r.NotifyController.UpdateByID)
-	notifyRouter.Delete(":id", r.Gen.IntId(), r.NotifyController.DeleteByID)
-
 	// SpectrumAlert路由
 	spectrumAlertRouter := v1.Group("spectrumalert")
 	spectrumAlertRouter.Get("", r.SpectrumAlertController.Query)
-	spectrumAlertRouter.Get("attacking", r.SpectrumAlertController.GetAttackData)
 	spectrumAlertRouter.Get(":id", r.Gen.IntId(), r.SpectrumAlertController.QueryByID)
 	spectrumAlertRouter.Post("", r.SpectrumAlertController.Create)
 	spectrumAlertRouter.Post("bulk", r.SpectrumAlertController.CreateBulk)
@@ -97,15 +26,25 @@ func (r *Router) Private(fa *fiber.App) {
 	spectrumAlertRouter.Put(":id", r.Gen.IntId(), r.SpectrumAlertController.UpdateByID)
 	spectrumAlertRouter.Delete(":id", r.Gen.IntId(), r.SpectrumAlertController.DeleteByID)
 
-	// Tenant路由
-	tenantRouter := v1.Group("tenant")
-	tenantRouter.Get("", r.TenantController.Query)
-	tenantRouter.Get(":id", r.Gen.IntId(), r.TenantController.QueryByID)
-	tenantRouter.Post("", r.TenantController.Create)
-	tenantRouter.Post("bulk", r.TenantController.CreateBulk)
-	tenantRouter.Post("bulk/delete", r.TenantController.DeleteBulk)
-	tenantRouter.Put(":id", r.Gen.IntId(), r.TenantController.UpdateByID)
-	tenantRouter.Delete(":id", r.Gen.IntId(), r.TenantController.DeleteByID)
+	// SystemApi路由
+	systemApiRouter := v1.Group("systemapi")
+	systemApiRouter.Get("", r.SystemApiController.Query)
+	systemApiRouter.Get(":id", r.Gen.IntId(), r.SystemApiController.QueryByID)
+	systemApiRouter.Post("", r.SystemApiController.Create)
+	systemApiRouter.Post("bulk", r.SystemApiController.CreateBulk)
+	systemApiRouter.Post("bulk/delete", r.SystemApiController.DeleteBulk)
+	systemApiRouter.Put(":id", r.Gen.IntId(), r.SystemApiController.UpdateByID)
+	systemApiRouter.Delete(":id", r.Gen.IntId(), r.SystemApiController.DeleteByID)
+
+	// SystemConfig路由
+	systemConfigRouter := v1.Group("systemconfig")
+	systemConfigRouter.Get("", r.SystemConfigController.Query)
+	systemConfigRouter.Get(":id", r.Gen.IntId(), r.SystemConfigController.QueryByID)
+	systemConfigRouter.Post("", r.SystemConfigController.Create)
+	systemConfigRouter.Post("bulk", r.SystemConfigController.CreateBulk)
+	systemConfigRouter.Post("bulk/delete", r.SystemConfigController.DeleteBulk)
+	systemConfigRouter.Put(":id", r.Gen.IntId(), r.SystemConfigController.UpdateByID)
+	systemConfigRouter.Delete(":id", r.Gen.IntId(), r.SystemConfigController.DeleteByID)
 
 	// CleanData路由
 	cleanDataRouter := v1.Group("cleandata")
@@ -127,40 +66,25 @@ func (r *Router) Private(fa *fiber.App) {
 	groupRouter.Put(":id", r.Gen.IntId(), r.GroupController.UpdateByID)
 	groupRouter.Delete(":id", r.Gen.IntId(), r.GroupController.DeleteByID)
 
-	// 集团NDS路由
-	ndsRouter := v1.Group("nds")
-	ndsRouter.Post("ip", r.NdsController.AddIP2ProtectGroup)
-	ndsRouter.Post("push", r.NdsController.Push)
-	ndsRouter.Get("alert", r.NdsController.GetSpectrumAlert)
-	ndsRouter.Get("cleandata", r.NdsController.GetCleanData)
-	ndsRouter.Get("spectrumdata", r.NdsController.GetSpectrumData)
-
-	// 集团soc路由（工单）
-	socGroupRouter := v1.Group("socgroup")
-	socGroupRouter.Get("ticket", r.SocGroupController.Query)
-	socGroupRouter.Post("ticket", r.SocGroupController.Add)
-
-	// Wofang路由
-	wofangRouter := v1.Group("wofang")
-
-	// 沃防外部路由
-	woFangApiRouter := wofangRouter.Group("api")
-	woFangApiRouter.Get("", r.WoFangApi.Query)
-	woFangApiRouter.Post("", r.WoFangApi.Add)
-	woFangApiRouter.Delete(":dragType/:ip", r.WoFangApi.Delete)
+	// MatrixSpectrumAlert路由
+	matrixSpectrumAlertRouter := v1.Group("matrixspectrumalert")
+	matrixSpectrumAlertRouter.Get("", r.MatrixSpectrumAlertController.Query)
+	matrixSpectrumAlertRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.QueryByID)
+	matrixSpectrumAlertRouter.Post("", r.MatrixSpectrumAlertController.Create)
+	matrixSpectrumAlertRouter.Post("bulk", r.MatrixSpectrumAlertController.CreateBulk)
+	matrixSpectrumAlertRouter.Post("bulk/delete", r.MatrixSpectrumAlertController.DeleteBulk)
+	matrixSpectrumAlertRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.UpdateByID)
+	matrixSpectrumAlertRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.DeleteByID)
 
-	woFangApiRouter2 := wofangRouter.Group("api2")
-	woFangApiRouter2.Post("", r.WoFangApi2.Add)
-	woFangApiRouter2.Delete(":ip", r.WoFangApi2.Delete)
-
-	// Wofang路由
-	wofangRouter.Get("", r.WofangController.Query)
-	wofangRouter.Get(":id", r.Gen.IntId(), r.WofangController.QueryByID)
-	wofangRouter.Post("", r.WofangController.Create)
-	wofangRouter.Post("bulk", r.WofangController.CreateBulk)
-	wofangRouter.Post("bulk/delete", r.WofangController.DeleteBulk)
-	wofangRouter.Put(":id", r.Gen.IntId(), r.WofangController.UpdateByID)
-	wofangRouter.Delete(":id", r.Gen.IntId(), r.WofangController.DeleteByID)
+	// ProtectGroup路由
+	protectGroupRouter := v1.Group("protectgroup")
+	protectGroupRouter.Get("", r.ProtectGroupController.Query)
+	protectGroupRouter.Get(":id", r.Gen.IntId(), r.ProtectGroupController.QueryByID)
+	protectGroupRouter.Post("", r.ProtectGroupController.Create)
+	protectGroupRouter.Post("bulk", r.ProtectGroupController.CreateBulk)
+	protectGroupRouter.Post("bulk/delete", r.ProtectGroupController.DeleteBulk)
+	protectGroupRouter.Put(":id", r.Gen.IntId(), r.ProtectGroupController.UpdateByID)
+	protectGroupRouter.Delete(":id", r.Gen.IntId(), r.ProtectGroupController.DeleteByID)
 
 	// SocGroupTicket路由
 	socGroupTicketRouter := v1.Group("socgroupticket")
@@ -172,15 +96,76 @@ func (r *Router) Private(fa *fiber.App) {
 	socGroupTicketRouter.Put(":id", r.Gen.IntId(), r.SocGroupTicketController.UpdateByID)
 	socGroupTicketRouter.Delete(":id", r.Gen.IntId(), r.SocGroupTicketController.DeleteByID)
 
-	// SystemApi路由
-	systemApiRouter := v1.Group("systemapi")
-	systemApiRouter.Get("", r.SystemApiController.Query)
-	systemApiRouter.Get(":id", r.Gen.IntId(), r.SystemApiController.QueryByID)
-	systemApiRouter.Post("", r.SystemApiController.Create)
-	systemApiRouter.Post("bulk", r.SystemApiController.CreateBulk)
-	systemApiRouter.Post("bulk/delete", r.SystemApiController.DeleteBulk)
-	systemApiRouter.Put(":id", r.Gen.IntId(), r.SystemApiController.UpdateByID)
-	systemApiRouter.Delete(":id", r.Gen.IntId(), r.SystemApiController.DeleteByID)
+	// User路由
+	userRouter := v1.Group("user")
+	userRouter.Get("info", r.UserController.UserInfo)
+	userRouter.Get("", r.UserController.Query)
+	userRouter.Get(":id", r.Gen.IntId(), r.UserController.QueryByID)
+	userRouter.Post("", r.UserController.Create)
+	userRouter.Post("bulk", r.UserController.CreateBulk)
+	userRouter.Post("bulk/delete", r.UserController.DeleteBulk)
+	userRouter.Put(":id", r.Gen.IntId(), r.UserController.UpdateByID)
+	userRouter.Delete(":id", r.Gen.IntId(), r.UserController.DeleteByID)
+
+	// UserOperationLog路由
+	userOperationLogRouter := v1.Group("useroperationlog")
+	userOperationLogRouter.Get("", r.UserOperationLogController.Query)
+	userOperationLogRouter.Get(":id", r.Gen.IntId(), r.UserOperationLogController.QueryByID)
+	userOperationLogRouter.Post("", r.UserOperationLogController.Create)
+	userOperationLogRouter.Post("bulk", r.UserOperationLogController.CreateBulk)
+	userOperationLogRouter.Post("bulk/delete", r.UserOperationLogController.DeleteBulk)
+	userOperationLogRouter.Put(":id", r.Gen.IntId(), r.UserOperationLogController.UpdateByID)
+	userOperationLogRouter.Delete(":id", r.Gen.IntId(), r.UserOperationLogController.DeleteByID)
+
+	// WofangAlert路由
+	wofangAlertRouter := v1.Group("wofangalert")
+	wofangAlertRouter.Get("", r.WofangAlertController.Query)
+	wofangAlertRouter.Get(":id", r.Gen.IntId(), r.WofangAlertController.QueryByID)
+	wofangAlertRouter.Post("", r.WofangAlertController.Create)
+	wofangAlertRouter.Post("bulk", r.WofangAlertController.CreateBulk)
+	wofangAlertRouter.Post("bulk/delete", r.WofangAlertController.DeleteBulk)
+	wofangAlertRouter.Put(":id", r.Gen.IntId(), r.WofangAlertController.UpdateByID)
+	wofangAlertRouter.Delete(":id", r.Gen.IntId(), r.WofangAlertController.DeleteByID)
+
+	// CasbinRule路由
+	casbinRuleRouter := v1.Group("casbinrule")
+	casbinRuleRouter.Get("", r.CasbinRuleController.Query)
+	casbinRuleRouter.Get(":id", r.Gen.IntId(), r.CasbinRuleController.QueryByID)
+	casbinRuleRouter.Post("", r.CasbinRuleController.Create)
+	casbinRuleRouter.Post("bulk", r.CasbinRuleController.CreateBulk)
+	casbinRuleRouter.Post("bulk/delete", r.CasbinRuleController.DeleteBulk)
+	casbinRuleRouter.Put(":id", r.Gen.IntId(), r.CasbinRuleController.UpdateByID)
+	casbinRuleRouter.Delete(":id", r.Gen.IntId(), r.CasbinRuleController.DeleteByID)
+
+	// CloudFlowData路由
+	cloudFlowDataRouter := v1.Group("cloudflowdata")
+	cloudFlowDataRouter.Get("", r.CloudFlowDataController.Query)
+	cloudFlowDataRouter.Get(":id", r.Gen.IntId(), r.CloudFlowDataController.QueryByID)
+	cloudFlowDataRouter.Post("", r.CloudFlowDataController.Create)
+	cloudFlowDataRouter.Post("bulk", r.CloudFlowDataController.CreateBulk)
+	cloudFlowDataRouter.Post("bulk/delete", r.CloudFlowDataController.DeleteBulk)
+	cloudFlowDataRouter.Put(":id", r.Gen.IntId(), r.CloudFlowDataController.UpdateByID)
+	cloudFlowDataRouter.Delete(":id", r.Gen.IntId(), r.CloudFlowDataController.DeleteByID)
+
+	// MatrixStrategy路由
+	matrixStrategyRouter := v1.Group("matrixstrategy")
+	matrixStrategyRouter.Get("", r.MatrixStrategyController.Query)
+	matrixStrategyRouter.Get(":id", r.Gen.IntId(), r.MatrixStrategyController.QueryByID)
+	matrixStrategyRouter.Post("", r.MatrixStrategyController.Create)
+	matrixStrategyRouter.Post("bulk", r.MatrixStrategyController.CreateBulk)
+	matrixStrategyRouter.Post("bulk/delete", r.MatrixStrategyController.DeleteBulk)
+	matrixStrategyRouter.Put(":id", r.Gen.IntId(), r.MatrixStrategyController.UpdateByID)
+	matrixStrategyRouter.Delete(":id", r.Gen.IntId(), r.MatrixStrategyController.DeleteByID)
+
+	// Strategy路由
+	strategyRouter := v1.Group("strategy")
+	strategyRouter.Get("", r.StrategyController.Query)
+	strategyRouter.Get(":id", r.Gen.IntId(), r.StrategyController.QueryByID)
+	strategyRouter.Post("", r.StrategyController.Create)
+	strategyRouter.Post("bulk", r.StrategyController.CreateBulk)
+	strategyRouter.Post("bulk/delete", r.StrategyController.DeleteBulk)
+	strategyRouter.Put(":id", r.Gen.IntId(), r.StrategyController.UpdateByID)
+	strategyRouter.Delete(":id", r.Gen.IntId(), r.StrategyController.DeleteByID)
 
 	// CloudAlert路由
 	cloudAlertRouter := v1.Group("cloudalert")
@@ -192,110 +177,93 @@ func (r *Router) Private(fa *fiber.App) {
 	cloudAlertRouter.Put(":id", r.Gen.IntId(), r.CloudAlertController.UpdateByID)
 	cloudAlertRouter.Delete(":id", r.Gen.IntId(), r.CloudAlertController.DeleteByID)
 
-	// CloudFlow路由
-	cloudFlowRouter := v1.Group("cloudflowdata")
-	cloudFlowRouter.Get("", r.CloudFlowDataController.Query)
-	cloudFlowRouter.Get(":id", r.Gen.IntId(), r.CloudFlowDataController.QueryByID)
-	cloudFlowRouter.Post("", r.CloudFlowDataController.Create)
-	cloudFlowRouter.Post("bulk", r.CloudFlowDataController.CreateBulk)
-	cloudFlowRouter.Post("bulk/delete", r.CloudFlowDataController.DeleteBulk)
-	cloudFlowRouter.Put(":id", r.Gen.IntId(), r.CloudFlowDataController.UpdateByID)
-	cloudFlowRouter.Delete(":id", r.Gen.IntId(), r.CloudFlowDataController.DeleteByID)
+	// DataSync路由
+	dataSyncRouter := v1.Group("datasync")
+	dataSyncRouter.Get("", r.DataSyncController.Query)
+	dataSyncRouter.Get(":id", r.Gen.IntId(), r.DataSyncController.QueryByID)
+	dataSyncRouter.Post("", r.DataSyncController.Create)
+	dataSyncRouter.Post("bulk", r.DataSyncController.CreateBulk)
+	dataSyncRouter.Post("bulk/delete", r.DataSyncController.DeleteBulk)
+	dataSyncRouter.Put(":id", r.Gen.IntId(), r.DataSyncController.UpdateByID)
+	dataSyncRouter.Delete(":id", r.Gen.IntId(), r.DataSyncController.DeleteByID)
 
-	// CloudAttackData 路由
-	CloudAttackDataRouter := v1.Group("cloudattackdata")
-	CloudAttackDataRouter.Get("", r.CloudAttackDataController.Query)
-	CloudAttackDataRouter.Get(":id", r.Gen.IntId(), r.CloudAttackDataController.QueryByID)
-	CloudAttackDataRouter.Post("", r.CloudAttackDataController.Create)
-	CloudAttackDataRouter.Post("bulk", r.CloudAttackDataController.CreateBulk)
-	CloudAttackDataRouter.Post("bulk/delete", r.CloudAttackDataController.DeleteBulk)
-	CloudAttackDataRouter.Put(":id", r.Gen.IntId(), r.CloudAttackDataController.UpdateByID)
-	CloudAttackDataRouter.Delete(":id", r.Gen.IntId(), r.CloudAttackDataController.DeleteByID)
-
-	// WoFangAlert路由
-	woFangAlertRouter := v1.Group("wofangalert")
-	woFangAlertRouter.Get("", r.WoFangAlertController.Query)
-	woFangAlertRouter.Get(":id", r.Gen.IntId(), r.WoFangAlertController.QueryByID)
-	woFangAlertRouter.Post("", r.WoFangAlertController.Create)
-	woFangAlertRouter.Post("bulk", r.WoFangAlertController.CreateBulk)
-	woFangAlertRouter.Post("bulk/delete", r.WoFangAlertController.DeleteBulk)
-	woFangAlertRouter.Put(":id", r.Gen.IntId(), r.WoFangAlertController.UpdateByID)
-	woFangAlertRouter.Delete(":id", r.Gen.IntId(), r.WoFangAlertController.DeleteByID)
-
-	// SkylineDos 路由
-	SkylineDosRouter := v1.Group("skylinedos")
-	SkylineDosRouter.Get("", r.SkylineDosController.Query)
-	SkylineDosRouter.Get(":id", r.Gen.IntId(), r.SkylineDosController.QueryByID)
-	SkylineDosRouter.Post("", r.SkylineDosController.Create)
-	SkylineDosRouter.Post("bulk", r.SkylineDosController.CreateBulk)
-	SkylineDosRouter.Post("bulk/delete", r.SkylineDosController.DeleteBulk)
-	SkylineDosRouter.Put(":id", r.Gen.IntId(), r.SkylineDosController.UpdateByID)
-	SkylineDosRouter.Delete(":id", r.Gen.IntId(), r.SkylineDosController.DeleteByID)
-
-	// MatrixStategy 路由
-	MatrixStrategyRouter := v1.Group("matrixstrategy")
-	MatrixStrategyRouter.Get("", r.MatrixStrategyController.Query)
-	MatrixStrategyRouter.Get(":id", r.Gen.IntId(), r.MatrixStrategyController.QueryByID)
-	MatrixStrategyRouter.Post("", r.MatrixStrategyController.Create)
-	MatrixStrategyRouter.Post("bulk", r.MatrixStrategyController.CreateBulk)
-	MatrixStrategyRouter.Post("bulk/delete", r.MatrixStrategyController.DeleteBulk)
-	MatrixStrategyRouter.Put(":id", r.Gen.IntId(), r.MatrixStrategyController.UpdateByID)
-	MatrixStrategyRouter.Delete(":id", r.Gen.IntId(), r.MatrixStrategyController.DeleteByID)
-
-	// MatrixSpectrumData 路由
-	MatrixSpectrumDataRouter := v1.Group("matrixspectrumdata")
-	MatrixSpectrumDataRouter.Get("", r.MatrixSpectrumDataController.Query)
-	MatrixSpectrumDataRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.QueryByID)
-	MatrixSpectrumDataRouter.Post("", r.MatrixSpectrumDataController.Create)
-	MatrixSpectrumDataRouter.Post("bulk", r.MatrixSpectrumDataController.CreateBulk)
-	MatrixSpectrumDataRouter.Post("bulk/delete", r.MatrixSpectrumDataController.DeleteBulk)
-	MatrixSpectrumDataRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.UpdateByID)
-	MatrixSpectrumDataRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.DeleteByID)
-
-	// MatrixSpectrumAlert 路由
-	MatrixSpectrumAlertRouter := v1.Group("matrixspectrumalert")
-	MatrixSpectrumAlertRouter.Get("", r.MatrixSpectrumAlertController.Query)
-	MatrixSpectrumAlertRouter.Get("attacking", r.MatrixSpectrumAlertController.GetAttackData)
-	MatrixSpectrumAlertRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.QueryByID)
-	MatrixSpectrumAlertRouter.Post("", r.MatrixSpectrumAlertController.Create)
-	MatrixSpectrumAlertRouter.Post("bulk", r.MatrixSpectrumAlertController.CreateBulk)
-	MatrixSpectrumAlertRouter.Post("bulk/delete", r.MatrixSpectrumAlertController.DeleteBulk)
-	MatrixSpectrumAlertRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.UpdateByID)
-	MatrixSpectrumAlertRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.DeleteByID)
+	// Tenant路由
+	tenantRouter := v1.Group("tenant")
+	tenantRouter.Get("", r.TenantController.Query)
+	tenantRouter.Get(":id", r.Gen.IntId(), r.TenantController.QueryByID)
+	tenantRouter.Post("", r.TenantController.Create)
+	tenantRouter.Post("bulk", r.TenantController.CreateBulk)
+	tenantRouter.Post("bulk/delete", r.TenantController.DeleteBulk)
+	tenantRouter.Put(":id", r.Gen.IntId(), r.TenantController.UpdateByID)
+	tenantRouter.Delete(":id", r.Gen.IntId(), r.TenantController.DeleteByID)
 
-	// Chart 路由
-	ChartRouter := v1.Group("chart")
-	// 分光数据
-	ChartRouter.Get("spectrum", r.ChartApi.Query)
+	// Wofang路由
+	wofangRouter := v1.Group("wofang")
+	wofangRouter.Get("", r.WofangController.Query)
+	wofangRouter.Get(":id", r.Gen.IntId(), r.WofangController.QueryByID)
+	wofangRouter.Post("", r.WofangController.Create)
+	wofangRouter.Post("bulk", r.WofangController.CreateBulk)
+	wofangRouter.Post("bulk/delete", r.WofangController.DeleteBulk)
+	wofangRouter.Put(":id", r.Gen.IntId(), r.WofangController.UpdateByID)
+	wofangRouter.Delete(":id", r.Gen.IntId(), r.WofangController.DeleteByID)
 
-	// UserOperationLog 路由
-	UserOperationLogRouter := v1.Group("useroperationlog")
-	UserOperationLogRouter.Get("", r.UserOperationLogController.Query)
-	UserOperationLogRouter.Get(":id", r.Gen.IntId(), r.UserOperationLogController.QueryByID)
-	UserOperationLogRouter.Post("", r.UserOperationLogController.Create)
-	UserOperationLogRouter.Post("bulk", r.UserOperationLogController.CreateBulk)
-	UserOperationLogRouter.Post("bulk/delete", r.UserOperationLogController.DeleteBulk)
-	UserOperationLogRouter.Put(":id", r.Gen.IntId(), r.UserOperationLogController.UpdateByID)
-	UserOperationLogRouter.Delete(":id", r.Gen.IntId(), r.UserOperationLogController.DeleteByID)
+	// SpectrumData路由
+	spectrumDataRouter := v1.Group("spectrumdata")
+	spectrumDataRouter.Get("", r.SpectrumDataController.Query)
+	spectrumDataRouter.Get(":id", r.Gen.IntId(), r.SpectrumDataController.QueryByID)
+	spectrumDataRouter.Post("", r.SpectrumDataController.Create)
+	spectrumDataRouter.Post("bulk", r.SpectrumDataController.CreateBulk)
+	spectrumDataRouter.Post("bulk/delete", r.SpectrumDataController.DeleteBulk)
+	spectrumDataRouter.Put(":id", r.Gen.IntId(), r.SpectrumDataController.UpdateByID)
+	spectrumDataRouter.Delete(":id", r.Gen.IntId(), r.SpectrumDataController.DeleteByID)
 
-	// UserOperationLog 路由
-	SystemConfigRouter := v1.Group("systemconfig")
-	SystemConfigRouter.Get("", r.SystemConfigController.Query)
-	SystemConfigRouter.Get(":id", r.Gen.IntId(), r.SystemConfigController.QueryByID)
-	SystemConfigRouter.Post("", r.SystemConfigController.Create)
-	SystemConfigRouter.Post("bulk", r.SystemConfigController.CreateBulk)
-	SystemConfigRouter.Post("bulk/delete", r.SystemConfigController.DeleteBulk)
-	SystemConfigRouter.Put(":id", r.Gen.IntId(), r.SystemConfigController.UpdateByID)
-	SystemConfigRouter.Delete(":id", r.Gen.IntId(), r.SystemConfigController.DeleteByID)
+	// CloudAttackData路由
+	cloudAttackDataRouter := v1.Group("cloudattackdata")
+	cloudAttackDataRouter.Get("", r.CloudAttackDataController.Query)
+	cloudAttackDataRouter.Get(":id", r.Gen.IntId(), r.CloudAttackDataController.QueryByID)
+	cloudAttackDataRouter.Post("", r.CloudAttackDataController.Create)
+	cloudAttackDataRouter.Post("bulk", r.CloudAttackDataController.CreateBulk)
+	cloudAttackDataRouter.Post("bulk/delete", r.CloudAttackDataController.DeleteBulk)
+	cloudAttackDataRouter.Put(":id", r.Gen.IntId(), r.CloudAttackDataController.UpdateByID)
+	cloudAttackDataRouter.Delete(":id", r.Gen.IntId(), r.CloudAttackDataController.DeleteByID)
+
+	// HelloUser路由
+	helloUserRouter := v1.Group("hellouser")
+	helloUserRouter.Get("", r.HelloUserController.Query)
+	helloUserRouter.Get(":id", r.Gen.IntId(), r.HelloUserController.QueryByID)
+	helloUserRouter.Post("", r.HelloUserController.Create)
+	helloUserRouter.Post("bulk", r.HelloUserController.CreateBulk)
+	helloUserRouter.Post("bulk/delete", r.HelloUserController.DeleteBulk)
+	helloUserRouter.Put(":id", r.Gen.IntId(), r.HelloUserController.UpdateByID)
+	helloUserRouter.Delete(":id", r.Gen.IntId(), r.HelloUserController.DeleteByID)
+
+	// MatrixSpectrumData路由
+	matrixSpectrumDataRouter := v1.Group("matrixspectrumdata")
+	matrixSpectrumDataRouter.Get("", r.MatrixSpectrumDataController.Query)
+	matrixSpectrumDataRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.QueryByID)
+	matrixSpectrumDataRouter.Post("", r.MatrixSpectrumDataController.Create)
+	matrixSpectrumDataRouter.Post("bulk", r.MatrixSpectrumDataController.CreateBulk)
+	matrixSpectrumDataRouter.Post("bulk/delete", r.MatrixSpectrumDataController.DeleteBulk)
+	matrixSpectrumDataRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.UpdateByID)
+	matrixSpectrumDataRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.DeleteByID)
 
-	// DataSync 路由
-	DataSyncRouter := v1.Group("datasync")
-	DataSyncRouter.Get("", r.DataSyncController.Query)
-	DataSyncRouter.Get(":id", r.Gen.IntId(), r.DataSyncController.QueryByID)
-	DataSyncRouter.Post("", r.DataSyncController.Create)
-	DataSyncRouter.Post("bulk", r.DataSyncController.CreateBulk)
-	DataSyncRouter.Post("bulk/delete", r.DataSyncController.DeleteBulk)
-	DataSyncRouter.Put(":id", r.Gen.IntId(), r.DataSyncController.UpdateByID)
-	DataSyncRouter.Delete(":id", r.Gen.IntId(), r.DataSyncController.DeleteByID)
+	// Notify路由
+	notifyRouter := v1.Group("notify")
+	notifyRouter.Get("", r.NotifyController.Query)
+	notifyRouter.Get(":id", r.Gen.IntId(), r.NotifyController.QueryByID)
+	notifyRouter.Post("", r.NotifyController.Create)
+	notifyRouter.Post("bulk", r.NotifyController.CreateBulk)
+	notifyRouter.Post("bulk/delete", r.NotifyController.DeleteBulk)
+	notifyRouter.Put(":id", r.Gen.IntId(), r.NotifyController.UpdateByID)
+	notifyRouter.Delete(":id", r.Gen.IntId(), r.NotifyController.DeleteByID)
 
+	// SkylineDos路由
+	skylineDosRouter := v1.Group("skylinedos")
+	skylineDosRouter.Get("", r.SkylineDosController.Query)
+	skylineDosRouter.Get(":id", r.Gen.IntId(), r.SkylineDosController.QueryByID)
+	skylineDosRouter.Post("", r.SkylineDosController.Create)
+	skylineDosRouter.Post("bulk", r.SkylineDosController.CreateBulk)
+	skylineDosRouter.Post("bulk/delete", r.SkylineDosController.DeleteBulk)
+	skylineDosRouter.Put(":id", r.Gen.IntId(), r.SkylineDosController.UpdateByID)
+	skylineDosRouter.Delete(":id", r.Gen.IntId(), r.SkylineDosController.DeleteByID)
 }
diff --git a/api/v1/public.go b/api/v1/public.go
index 9e3a3cc..ed0096f 100644
--- a/api/v1/public.go
+++ b/api/v1/public.go
@@ -10,4 +10,5 @@ func (r *Router) Public(fa *fiber.App) {
 	userRouter := v1.Group("user")
 	userRouter.Get("logout", r.UserController.Logout)
 	userRouter.Post("login", r.UserController.Login)
+
 }
diff --git a/api/v1/wire_set.go b/api/v1/wire_set.go
index 824817c..e05e175 100644
--- a/api/v1/wire_set.go
+++ b/api/v1/wire_set.go
@@ -1,12 +1,11 @@
 package v1
 
 import (
-	"meta/app/controller"
-	"meta/pkg/middleware"
-	"meta/pkg/middleware/generator"
-
 	"github.com/casbin/casbin/v2"
 	"github.com/google/wire"
+	"github.com/one-meta/meta/app/controller"
+	"github.com/one-meta/meta/pkg/middleware"
+	"github.com/one-meta/meta/pkg/middleware/generator"
 )
 
 var Set = wire.NewSet(wire.Struct(new(Router), "*"))
@@ -15,39 +14,30 @@ type Router struct {
 	Enf                           *casbin.Enforcer
 	Casbinx                       *middleware.Casbinx
 	JWT                           *middleware.JWT
-	AuthApiKey                    *middleware.Apikey
-	OPLog                         *middleware.OPLog
 	Gen                           *generator.Gen
-	CasbinRuleController          *controller.CasbinRuleController
-	CleanDataController           *controller.CleanDataController
-	GroupController               *controller.GroupController
+	MatrixSpectrumAlertController *controller.MatrixSpectrumAlertController
 	ProtectGroupController        *controller.ProtectGroupController
-	NdsController                 *controller.NdsController
+	SocGroupTicketController      *controller.SocGroupTicketController
 	SpectrumAlertController       *controller.SpectrumAlertController
-	SpectrumDataController        *controller.SpectrumDataController
-	TenantController              *controller.TenantController
+	SystemApiController           *controller.SystemApiController
+	SystemConfigController        *controller.SystemConfigController
+	CleanDataController           *controller.CleanDataController
+	GroupController               *controller.GroupController
+	WofangAlertController         *controller.WofangAlertController
 	UserController                *controller.UserController
+	UserOperationLogController    *controller.UserOperationLogController
+	MatrixStrategyController      *controller.MatrixStrategyController
 	StrategyController            *controller.StrategyController
-	NotifyController              *controller.NotifyController
-	SocGroupController            *controller.SocGroupController
+	CasbinRuleController          *controller.CasbinRuleController
+	CloudFlowDataController       *controller.CloudFlowDataController
+	TenantController              *controller.TenantController
 	WofangController              *controller.WofangController
-	SocGroupTicketController      *controller.SocGroupTicketController
-	SystemApiController           *controller.SystemApiController
 	CloudAlertController          *controller.CloudAlertController
-	CloudFlowDataController       *controller.CloudFlowDataController
-	CloudAttackDataController     *controller.CloudAttackDataController
-	WoFangAlertController         *controller.WofangAlertController
-	SkylineDosController          *controller.SkylineDosController
-	MatrixStrategyController      *controller.MatrixStrategyController
-	MatrixSpectrumDataController  *controller.MatrixSpectrumDataController
-	MatrixSpectrumAlertController *controller.MatrixSpectrumAlertController
-	UserOperationLogController    *controller.UserOperationLogController
-	SystemConfigController        *controller.SystemConfigController
 	DataSyncController            *controller.DataSyncController
-
-	// Api为直接外部接口的数据
-	// Controller是内部接口
-	WoFangApi  *controller.WoFangApi
-	WoFangApi2 *controller.WoFangApi2
-	ChartApi   *controller.ChartController
+	MatrixSpectrumDataController  *controller.MatrixSpectrumDataController
+	NotifyController              *controller.NotifyController
+	SkylineDosController          *controller.SkylineDosController
+	SpectrumDataController        *controller.SpectrumDataController
+	CloudAttackDataController     *controller.CloudAttackDataController
+	HelloUserController           *controller.HelloUserController
 }
diff --git a/app/controller/casbin_rule.go b/app/controller/casbin_rule.go
index 3602f3d..926a516 100644
--- a/app/controller/casbin_rule.go
+++ b/app/controller/casbin_rule.go
@@ -2,11 +2,10 @@ package controller
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
-
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
 )
 
@@ -22,14 +21,14 @@ type CasbinRuleController struct {
 //	@Tags CasbinRule
 //	@Accept json
 //	@Produce json
-//	@Param NetType query string false "NetType"
+//	@Param type query string false "type"
 //
-// @Param Sub query string false "Sub"
-// @Param Dom query string false "Dom"
-// @Param Obj query string false "Obj"
-// @Param Act query string false "Act"
-// @Param V4 query string false "V4"
-// @Param V5 query string false "V5"
+// @Param sub query string false "sub"
+// @Param dom query string false "dom"
+// @Param obj query string false "obj"
+// @Param act query string false "act"
+// @Param v4 query string false "v4"
+// @Param v5 query string false "v5"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/clean_data.go b/app/controller/clean_data.go
index 43ed720..fda7136 100644
--- a/app/controller/clean_data.go
+++ b/app/controller/clean_data.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type CleanDataController struct {
@@ -21,37 +21,37 @@ type CleanDataController struct {
 //	@Tags CleanData
 //	@Accept json
 //	@Produce json
-//	@Param IP query string false "ip" Format(ipv4)
 //	@Param created_at query string false "created_at" Format(date-time)
 //
-// @Param Time query string false "Time" Format(date-time)
-// @Param InBps query integer false "InBps"
-// @Param OutBps query integer false "OutBps"
-// @Param InPps query integer false "InPps"
-// @Param OutPps query integer false "OutPps"
-// @Param InAckPps query integer false "InAckPps"
-// @Param OutAckPps query integer false "OutAckPps"
-// @Param InAckBps query integer false "InAckBps"
-// @Param OutAckBps query integer false "OutAckBps"
-// @Param InSynPps query integer false "InSynPps"
-// @Param OutSynPps query integer false "OutSynPps"
-// @Param InUdpPps query integer false "InUdpPps"
-// @Param OutUdpPps query integer false "OutUdpPps"
-// @Param InUdpBps query integer false "InUdpBps"
-// @Param OutUdpBps query integer false "OutUdpBps"
-// @Param InIcmpPps query integer false "InIcmpPps"
-// @Param InIcmpBps query integer false "InIcmpBps"
-// @Param OutIcmpBps query integer false "OutIcmpBps"
-// @Param OutIcmpPps query integer false "OutIcmpPps"
-// @Param InDnsPps query integer false "InDnsPps"
-// @Param OutDnsPps query integer false "OutDnsPps"
-// @Param InDnsBps query integer false "InDnsBps"
-// @Param OutDnsBps query integer false "OutDnsBps"
-// @Param AttackFlags query integer false "AttackFlags"
-// @Param Count query integer false "Count"
-// @Param IpType query integer false "IpType"
-// @Param FFilter query string false "FFilter"
-// @Param Host query string false "Host"
+// @Param ip query string false "ip" Format(ipv4)
+// @Param time query string false "time" Format(date-time)
+// @Param in_bps query integer false "in_bps"
+// @Param out_bps query integer false "out_bps"
+// @Param in_pps query integer false "in_pps"
+// @Param out_pps query integer false "out_pps"
+// @Param in_ack_pps query integer false "in_ack_pps"
+// @Param out_ack_pps query integer false "out_ack_pps"
+// @Param in_ack_bps query integer false "in_ack_bps"
+// @Param out_ack_bps query integer false "out_ack_bps"
+// @Param in_syn_pps query integer false "in_syn_pps"
+// @Param out_syn_pps query integer false "out_syn_pps"
+// @Param in_udp_pps query integer false "in_udp_pps"
+// @Param out_udp_pps query integer false "out_udp_pps"
+// @Param in_udp_bps query integer false "in_udp_bps"
+// @Param out_udp_bps query integer false "out_udp_bps"
+// @Param in_icmp_pps query integer false "in_icmp_pps"
+// @Param in_icmp_bps query integer false "in_icmp_bps"
+// @Param out_icmp_bps query integer false "out_icmp_bps"
+// @Param out_icmp_pps query integer false "out_icmp_pps"
+// @Param in_dns_pps query integer false "in_dns_pps"
+// @Param out_dns_pps query integer false "out_dns_pps"
+// @Param in_dns_bps query integer false "in_dns_bps"
+// @Param out_dns_bps query integer false "out_dns_bps"
+// @Param attack_flags query integer false "attack_flags"
+// @Param count query integer false "count"
+// @Param ip_type query integer false "ip_type"
+// @Param cfilter query string false "cfilter"
+// @Param host query string false "host"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/cloud_alert.go b/app/controller/cloud_alert.go
index 0198310..3a2f386 100644
--- a/app/controller/cloud_alert.go
+++ b/app/controller/cloud_alert.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type CloudAlertController struct {
@@ -34,7 +34,10 @@ type CloudAlertController struct {
 // @Param tcp_ack_num query string false "tcp_ack_num"
 // @Param tcp_seq_num query string false "tcp_seq_num"
 // @Param protocol query integer false "protocol"
-// @Param defence_level query string false "defence_level"
+// @Param defence_level query integer false "defence_level"
+// @Param max_pps query integer false "max_pps"
+// @Param max_attack_pps query integer false "max_attack_pps"
+// @Param overlimit_pkt_count query integer false "overlimit_pkt_count"
 // @Param start_time query string false "start_time" Format(date-time)
 // @Param end_time query string false "end_time" Format(date-time)
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
diff --git a/app/controller/cloud_attack_data.go b/app/controller/cloud_attack_data.go
index 416256d..313bdf5 100644
--- a/app/controller/cloud_attack_data.go
+++ b/app/controller/cloud_attack_data.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type CloudAttackDataController struct {
diff --git a/app/controller/cloud_flow_data.go b/app/controller/cloud_flow_data.go
index ffe8cab..e95d0b4 100644
--- a/app/controller/cloud_flow_data.go
+++ b/app/controller/cloud_flow_data.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type CloudFlowDataController struct {
@@ -30,7 +30,8 @@ type CloudFlowDataController struct {
 // @Param dst_ip query string false "dst_ip"
 // @Param dst_port query integer false "dst_port"
 // @Param protocol query integer false "protocol"
-// @Param count query integer false "count"
+// @Param max_attack_pps query integer false "max_attack_pps"
+// @Param flow_over_max_pps_count query integer false "flow_over_max_pps_count"
 // @Param start_time query string false "start_time" Format(date-time)
 // @Param end_time query string false "end_time" Format(date-time)
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
diff --git a/app/controller/data_sync.go b/app/controller/data_sync.go
index 6186fd2..a54dcf2 100644
--- a/app/controller/data_sync.go
+++ b/app/controller/data_sync.go
@@ -3,11 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
-	"sort"
 )
 
 type DataSyncController struct {
@@ -25,10 +24,9 @@ type DataSyncController struct {
 //	@Param created_at query string false "created_at" Format(date-time)
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
-// @Param net_type query string false "net_type"
+// @Param remark query string false "remark"
+// @Param data_type query string false "data_type"
 // @Param type query string false "type"
-// @Param region query string false "region"
-// @Param source query string false "source"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -130,8 +128,6 @@ func (dsc *DataSyncController) UpdateByID(c *fiber.Ctx) error {
 	if err != nil {
 		return common.NewResult(c, err)
 	}
-	sort.Strings(*ds.DataList)
-	sort.Strings(*ds.PreDataList)
 	data, err := dsc.DataSyncService.UpdateByID(ctx, ds, id)
 	return common.NewResult(c, err, data)
 }
diff --git a/app/controller/group.go b/app/controller/group.go
index d0ace4c..c1c7e3f 100644
--- a/app/controller/group.go
+++ b/app/controller/group.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type GroupController struct {
@@ -21,14 +21,14 @@ type GroupController struct {
 //	@Tags Group
 //	@Accept json
 //	@Produce json
-//	@Param Name query string false "Name"
+//	@Param name query string false "name"
 //
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
 // @Param order query string false "排序，默认id逆序(-id)"
 //
-//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
+//	@Success 200 {object} common.Result{data=[]ent.Group}
 //	@Router /api/v1/group [get]
 func (gc *GroupController) Query(c *fiber.Ctx) error {
 	g := &ent.Group{}
@@ -49,7 +49,7 @@ func (gc *GroupController) Query(c *fiber.Ctx) error {
 //	@Accept json
 //	@Produce json
 //	@Param id path int true "Group ID"
-//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
+//	@Success 200 {object} common.Result{data=ent.Group}
 //	@Router /api/v1/group/{id} [get]
 func (gc *GroupController) QueryByID(c *fiber.Ctx) error {
 	id := c.Locals("id").(int)
@@ -65,8 +65,8 @@ func (gc *GroupController) QueryByID(c *fiber.Ctx) error {
 //	@Tags Group
 //	@Accept json
 //	@Produce json
-//	@Param group body ent.ProtectGroup true "Group"
-//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
+//	@Param group body ent.Group true "Group"
+//	@Success 200 {object} common.Result{data=ent.Group}
 //	@Router /api/v1/group [post]
 func (gc *GroupController) Create(c *fiber.Ctx) error {
 	g := &ent.Group{}
@@ -86,8 +86,8 @@ func (gc *GroupController) Create(c *fiber.Ctx) error {
 //	@Tags Group
 //	@Accept json
 //	@Produce json
-//	@Param group body []ent.ProtectGroup true "Group"
-//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
+//	@Param group body []ent.Group true "Group"
+//	@Success 200 {object} common.Result{data=[]ent.Group}
 //	@Router /api/v1/group/bulk [post]
 func (gc *GroupController) CreateBulk(c *fiber.Ctx) error {
 	g := make([]*ent.Group, 10)
@@ -109,9 +109,9 @@ func (gc *GroupController) CreateBulk(c *fiber.Ctx) error {
 //	@Produce json
 //	@Param id path int true "Group ID"
 //
-// @Param group body ent.ProtectGroup true "Group"
+// @Param group body ent.Group true "Group"
 //
-//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
+//	@Success 200 {object} common.Result{data=ent.Group}
 //	@Router /api/v1/group/{id} [put]
 func (gc *GroupController) UpdateByID(c *fiber.Ctx) error {
 	id := c.Locals("id").(int)
diff --git a/app/controller/matrix_spectrum_alert.go b/app/controller/matrix_spectrum_alert.go
index 3f99635..d152fc7 100644
--- a/app/controller/matrix_spectrum_alert.go
+++ b/app/controller/matrix_spectrum_alert.go
@@ -3,19 +3,15 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/entity"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type MatrixSpectrumAlertController struct {
 	MatrixSpectrumAlertService *service.MatrixSpectrumAlertService
 	Logger                     *zap.Logger
-	Rdb                        *redis.Client
-	NdsController              *NdsController
 }
 
 // Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
@@ -29,10 +25,9 @@ type MatrixSpectrumAlertController struct {
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
 // @Param remark query string false "remark"
-// @Param ip query string false "ip" Format(ipv4)
-// @Param device_name query string false "device_name"
-// @Param interface query string false "interface"
-// @Param protect_type query string false "protect_type"
+// @Param region query string false "region"
+// @Param net_type query string false "net_type"
+// @Param isp query string false "isp"
 // @Param start_time query string false "start_time" Format(date-time)
 // @Param end_time query string false "end_time" Format(date-time)
 // @Param attack_type query string false "attack_type"
@@ -180,35 +175,3 @@ func (msac *MatrixSpectrumAlertController) DeleteBulk(c *fiber.Ctx) error {
 	_, err = msac.MatrixSpectrumAlertService.DeleteBulk(ctx, deleteItem.Ids)
 	return common.NewResult(c, err)
 }
-
-// GetAttackData 获取攻击数据 MatrixSpectrumAlert
-//
-//	@Description Query 获取攻击数据 MatrixSpectrumAlert
-//	@Summary Query 获取攻击数据 MatrixSpectrumAlert
-//	@Tags MatrixSpectrumAlert
-//	@Accept json
-//	@Produce json
-//
-// @Param current query integer false "当前页"
-// @Param pageSize query integer false "分页大小"
-// @Param order query string false "排序，默认id逆序(-id)"
-//
-//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumAlert}
-//	@Router /api/v1/matrixspectrumalert/attacking [get]
-func (msac *MatrixSpectrumAlertController) GetAttackData(c *fiber.Ctx) error {
-	msa := &ent.MatrixSpectrumAlert{}
-	attack := &entity.AttackQuery{}
-	qp, err := common.QueryParser(c, attack)
-	if err != nil {
-		return common.NewResult(c, err)
-	}
-	ctx := c.Locals("ctx").(context.Context)
-	var count int
-	var result []*ent.MatrixSpectrumAlert
-	if !attack.Attacking {
-		count, result, err = msac.MatrixSpectrumAlertService.GetEndTimeNil(ctx, msa, qp)
-	} else {
-		count, result, err = msac.MatrixSpectrumAlertService.Query(ctx, msa, qp)
-	}
-	return common.NewPageResult(c, err, count, result)
-}
diff --git a/app/controller/matrix_spectrum_data.go b/app/controller/matrix_spectrum_data.go
index c27ea02..f77c1a3 100644
--- a/app/controller/matrix_spectrum_data.go
+++ b/app/controller/matrix_spectrum_data.go
@@ -2,11 +2,10 @@ package controller
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
-
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
 )
 
@@ -22,10 +21,12 @@ type MatrixSpectrumDataController struct {
 //	@Tags MatrixSpectrumData
 //	@Accept json
 //	@Produce json
-//	@Param device_ip query string false "device_ip"
+//	@Param created_at query string false "created_at" Format(date-time)
 //
-// @Param device_name query string false "device_name"
-// @Param interface query string false "interface"
+// @Param updated_at query string false "updated_at" Format(date-time)
+// @Param region query string false "region"
+// @Param net_type query string false "net_type"
+// @Param isp query string false "isp"
 // @Param bps query integer false "bps"
 // @Param time query string false "time" Format(date-time)
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
diff --git a/app/controller/matrix_strategy.go b/app/controller/matrix_strategy.go
index ee92e2c..562c9ba 100644
--- a/app/controller/matrix_strategy.go
+++ b/app/controller/matrix_strategy.go
@@ -3,17 +3,15 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type MatrixStrategyController struct {
 	MatrixStrategyService *service.MatrixStrategyService
 	Logger                *zap.Logger
-	Rdb                   *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
@@ -28,10 +26,12 @@ type MatrixStrategyController struct {
 // @Param updated_at query string false "updated_at" Format(date-time)
 // @Param remark query string false "remark"
 // @Param name query string false "name"
-// @Param device query string false "device"
+// @Param region query string false "region"
+// @Param net_type query string false "net_type"
+// @Param isp query string false "isp"
 // @Param monitor_bps query integer false "monitor_bps"
 // @Param drag_bps query integer false "drag_bps"
-// @Param drag_type query string false "drag_type"
+// @Param drag_type query integer false "drag_type"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -85,24 +85,9 @@ func (msc *MatrixStrategyController) Create(c *fiber.Ctx) error {
 	}
 	ctx := c.Locals("ctx").(context.Context)
 	create, err := msc.MatrixStrategyService.Create(ctx, ms)
-	freshCache(create, msc.Rdb, ctx)
 	return common.NewResult(c, err, create)
 }
 
-func freshCache(data *ent.MatrixStrategy, rdb *redis.Client, ctx context.Context) {
-	configKey := common.GenRedisKey("matrix", data.Region, data.NetType, data.Isp)
-	rdb.HSet(ctx, configKey, "id", data.ID)
-	rdb.HSet(ctx, configKey, "name", data.Name)
-	rdb.HSet(ctx, configKey, "monitorBps", data.MonitorBps)
-	rdb.HSet(ctx, configKey, "dragBps", data.DragBps)
-	rdb.HSet(ctx, configKey, "dragType", data.DragType)
-}
-
-func cleanCache(data *ent.MatrixStrategy, rdb *redis.Client, ctx context.Context) {
-	configKey := common.GenRedisKey("matrix", data.Region, data.NetType, data.Isp)
-	rdb.Del(ctx, configKey)
-}
-
 // CreateBulk 批量创建 MatrixStrategy
 //
 //	@Description CreateBulk 批量创建 MatrixStrategy
@@ -149,7 +134,6 @@ func (msc *MatrixStrategyController) UpdateByID(c *fiber.Ctx) error {
 		return common.NewResult(c, err)
 	}
 	data, err := msc.MatrixStrategyService.UpdateByID(ctx, ms, id)
-	freshCache(data, msc.Rdb, ctx)
 	return common.NewResult(c, err, data)
 }
 
@@ -167,8 +151,6 @@ func (msc *MatrixStrategyController) UpdateByID(c *fiber.Ctx) error {
 func (msc *MatrixStrategyController) DeleteByID(c *fiber.Ctx) error {
 	id := c.Locals("id").(int)
 	ctx := c.Locals("ctx").(context.Context)
-	ms, _ := msc.MatrixStrategyService.QueryByID(ctx, id)
-	cleanCache(ms, msc.Rdb, ctx)
 	err := msc.MatrixStrategyService.DeleteByID(ctx, id)
 	return common.NewResult(c, err)
 }
diff --git a/app/controller/notify.go b/app/controller/notify.go
index 722bfd5..342785e 100644
--- a/app/controller/notify.go
+++ b/app/controller/notify.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type NotifyController struct {
diff --git a/app/controller/protect_group.go b/app/controller/protect_group.go
index 3660c21..6f5d54b 100644
--- a/app/controller/protect_group.go
+++ b/app/controller/protect_group.go
@@ -2,19 +2,10 @@ package controller
 
 import (
 	"context"
-	"errors"
-	"fmt"
-	"meta/app/ent"
-	"meta/app/entity"
-	"meta/app/entity/config"
-	"meta/app/entity/netease"
-	"meta/app/service"
-	"meta/pkg/common"
-	"meta/pkg/http"
-	"strings"
-
-	mapset "github.com/deckarep/golang-set"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
 )
 
@@ -30,12 +21,13 @@ type ProtectGroupController struct {
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param Remark query string false "Remark"
 //	@Param created_at query string false "created_at" Format(date-time)
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
-// @Param GroupName query string false "GroupName"
-// @Param NetType query integer false "NetType"
+// @Param remark query string false "remark"
+// @Param group_name query string false "group_name"
+// @Param type query integer false "type"
+// @Param expand_ip query string false "expand_ip"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -61,7 +53,7 @@ func (pgc *ProtectGroupController) Query(c *fiber.Ctx) error {
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param id path int true "Group ID"
+//	@Param id path int true "ProtectGroup ID"
 //	@Success 200 {object} common.Result{data=ent.ProtectGroup}
 //	@Router /api/v1/protectgroup/{id} [get]
 func (pgc *ProtectGroupController) QueryByID(c *fiber.Ctx) error {
@@ -71,14 +63,14 @@ func (pgc *ProtectGroupController) QueryByID(c *fiber.Ctx) error {
 	return common.NewResult(c, err, result)
 }
 
-// Create 创建 Group
+// Create 创建 ProtectGroup
 //
 //	@Description Create 创建 ProtectGroup
 //	@Summary Create 创建 ProtectGroup
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param protectgroup body ent.ProtectGroup true "Group"
+//	@Param protectgroup body ent.ProtectGroup true "ProtectGroup"
 //	@Success 200 {object} common.Result{data=ent.ProtectGroup}
 //	@Router /api/v1/protectgroup [post]
 func (pgc *ProtectGroupController) Create(c *fiber.Ctx) error {
@@ -99,7 +91,7 @@ func (pgc *ProtectGroupController) Create(c *fiber.Ctx) error {
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param protectgroup body []ent.ProtectGroup true "Group"
+//	@Param protectgroup body []ent.ProtectGroup true "ProtectGroup"
 //	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
 //	@Router /api/v1/protectgroup/bulk [post]
 func (pgc *ProtectGroupController) CreateBulk(c *fiber.Ctx) error {
@@ -120,9 +112,9 @@ func (pgc *ProtectGroupController) CreateBulk(c *fiber.Ctx) error {
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param id path int true "Group ID"
+//	@Param id path int true "ProtectGroup ID"
 //
-// @Param protectgroup body ent.ProtectGroup true "Group"
+// @Param protectgroup body ent.ProtectGroup true "ProtectGroup"
 //
 //	@Success 200 {object} common.Result{data=ent.ProtectGroup}
 //	@Router /api/v1/protectgroup/{id} [put]
@@ -137,62 +129,6 @@ func (pgc *ProtectGroupController) UpdateByID(c *fiber.Ctx) error {
 	if err != nil {
 		return common.NewResult(c, err)
 	}
-	if pg.IPList == nil {
-		return common.NewResult(c, errors.New("ip列表为空"))
-	}
-
-	var ips []string
-	var expendIpList []string
-	ipSegmentSet := mapset.NewSet()
-	ipSet := mapset.NewSet()
-
-	for _, ipOrCidr := range *pg.IPList {
-		err := common.CheckIp(ipOrCidr)
-		if err != nil {
-			return common.NewResult(c, err)
-		}
-		if ipSegmentSet.Contains(ipOrCidr) {
-			return common.NewResult(c, fmt.Errorf("数据重复:%s", ipOrCidr))
-		}
-		ipSegmentSet.Add(ipOrCidr)
-
-		ips = append(ips, ipOrCidr)
-		tempList, err := common.IpData2SingleIp(ipOrCidr)
-		if err != nil {
-			pgc.Logger.Error(err.Error())
-			continue
-		}
-
-		for _, ip := range tempList {
-			if ipSet.Contains(ip) {
-				return common.NewResult(c, fmt.Errorf("IPv4展开数据重复:%s", ip))
-			}
-			ipSet.Add(ip)
-		}
-		expendIpList = append(expendIpList, tempList...)
-	}
-	if len(expendIpList) != 0 {
-		pg.ExpandIP = strings.Join(expendIpList, "\n")
-		pg.IPList = &ips
-	}
-	groupUpdate := &netease.GroupUpdate{
-		GroupId: pg.GroupID,
-		Type:    pg.Type,
-		Ips:     ips,
-	}
-	timestamp, sign := http.LoadNdsSign()
-	reqData := &entity.ReqData{Data: groupUpdate, Sign: sign, Timestamp: timestamp}
-	// 正式环境才修改NDS防护群组ip列表
-	if config.CFG.Stage.Status == "prod" {
-		_, err = http.DoNdsRequest(config.CFG.External.Nds.Api, "/group/updateIpList.json", reqData)
-		if err != nil {
-			pgc.Logger.Sugar().Error(err.Error())
-			return common.NewResult(c, err)
-		}
-		fmt.Printf("\n%+v\n", groupUpdate)
-		fmt.Printf("%+v\n", reqData.Data)
-	}
-
 	data, err := pgc.ProtectGroupService.UpdateByID(ctx, pg, id)
 	return common.NewResult(c, err, data)
 }
@@ -204,7 +140,7 @@ func (pgc *ProtectGroupController) UpdateByID(c *fiber.Ctx) error {
 //	@Tags ProtectGroup
 //	@Accept json
 //	@Produce json
-//	@Param id path int true "Group ID"
+//	@Param id path int true "ProtectGroup ID"
 //
 //	@Success 200 {object} common.Message
 //	@Router /api/v1/protectgroup/{id} [delete]
diff --git a/app/controller/skyline_dos.go b/app/controller/skyline_dos.go
index 04bcdac..183fc4f 100644
--- a/app/controller/skyline_dos.go
+++ b/app/controller/skyline_dos.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type SkylineDosController struct {
@@ -46,9 +46,6 @@ func (sdc *SkylineDosController) Query(c *fiber.Ctx) error {
 	if err != nil {
 		return common.NewResult(c, err)
 	}
-	if qp.Order == "-id" {
-		qp.Order = "-start_time"
-	}
 	ctx := c.Locals("ctx").(context.Context)
 	count, result, err := sdc.SkylineDosService.Query(ctx, sd, qp)
 	return common.NewPageResult(c, err, count, result)
diff --git a/app/controller/soc_group_ticket.go b/app/controller/soc_group_ticket.go
index 3d2a557..4d53473 100644
--- a/app/controller/soc_group_ticket.go
+++ b/app/controller/soc_group_ticket.go
@@ -3,21 +3,15 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/entity/config"
-	"meta/app/entity/netease/socgroup"
-	"meta/app/service"
-	"meta/pkg/common"
-	"meta/pkg/http"
-	"strings"
 )
 
 type SocGroupTicketController struct {
 	SocGroupTicketService *service.SocGroupTicketService
 	Logger                *zap.Logger
-	Rdb                   *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
@@ -32,6 +26,16 @@ type SocGroupTicketController struct {
 // @Param updated_at query string false "updated_at" Format(date-time)
 // @Param remark query string false "remark"
 // @Param name query string false "name"
+// @Param type query string false "type"
+// @Param description query string false "description"
+// @Param divert_type query integer false "divert_type"
+// @Param op_type query integer false "op_type"
+// @Param op_time query string false "op_time" Format(date-time)
+// @Param config_type query integer false "config_type"
+// @Param config_args query string false "config_args"
+// @Param product_name query string false "product_name"
+// @Param product_code query string false "product_code"
+// @Param error_info query string false "error_info"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -84,46 +88,7 @@ func (sgtc *SocGroupTicketController) Create(c *fiber.Ctx) error {
 		return common.NewResult(c, err)
 	}
 	ctx := c.Locals("ctx").(context.Context)
-	var followIdList []int
-	if sgt.FollowList == nil || len(*sgt.FollowList) == 0 {
-		followIdList = []int{5314}
-	} else {
-		followIdList = *sgt.FollowList
-	}
-	var configType int
-	if sgt.ConfigType != 0 {
-		configType = sgt.ConfigType - 1
-	}
-	requestData := &socgroup.AddRequestData{
-		WorkOrderType:          "DOSDIVERT",
-		Name:                   sgt.Name,
-		Description:            sgt.Description,
-		FollowUserIdList:       followIdList,
-		DepartmentId:           sgt.DepartmentID,
-		IpList:                 strings.Join(*sgt.IPList, "\n"),
-		Bandwidth:              sgt.MinBandwidth,
-		DivertType:             sgt.DivertType - 1,
-		OpType:                 sgt.OpType - 1,
-		OpTime:                 sgt.OpTime.UnixMilli(),
-		ConfigType:             configType,
-		ConfigArgs:             sgt.ConfigArgs,
-		ProductName:            sgt.ProductName,
-		ProductAlias:           sgt.ProductCode,
-		EmergencyContacterList: *sgt.ContactList,
-	}
-	ticket, err := http.AddTicket(config.CFG.External.SocGroup.Api, requestData)
-	if ticket == nil || err != nil {
-		if ticket != nil {
-			sgt.ErrorInfo = ticket.Msg
-		} else {
-			sgt.ErrorInfo = err.Error()
-		}
-		sgtc.Logger.Sugar().Error(err)
-	} else {
-		sgt.GroupTicketID = ticket.DataItems.Id
-	}
 	create, err := sgtc.SocGroupTicketService.Create(ctx, sgt)
-
 	return common.NewResult(c, err, create)
 }
 
diff --git a/app/controller/spectrum_alert.go b/app/controller/spectrum_alert.go
index a8d5981..98f3891 100644
--- a/app/controller/spectrum_alert.go
+++ b/app/controller/spectrum_alert.go
@@ -3,11 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/entity"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type SpectrumAlertController struct {
@@ -22,17 +21,17 @@ type SpectrumAlertController struct {
 //	@Tags SpectrumAlert
 //	@Accept json
 //	@Produce json
-//	@Param Remark query string false "Remark"
 //	@Param created_at query string false "created_at" Format(date-time)
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
-// @Param IP query string false "IP" Format(ipv4)
-// @Param StartTime query string false "StartTime" Format(date-time)
-// @Param EndTime query string false "EndTime" Format(date-time)
-// @Param AttackType query string false "AttackType"
-// @Param Source query string false "Source"
-// @Param MaxPps query string false "MaxPps"
-// @Param MaxBps query string false "MaxBps"
+// @Param remark query string false "remark"
+// @Param ip query string false "ip" Format(ipv4)
+// @Param start_time query string false "start_time" Format(date-time)
+// @Param end_time query string false "end_time" Format(date-time)
+// @Param attack_type query string false "attack_type"
+// @Param max_pps query integer false "max_pps"
+// @Param max_bps query integer false "max_bps"
+// @Param isp_code query integer false "isp_code"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -176,35 +175,3 @@ func (sac *SpectrumAlertController) DeleteBulk(c *fiber.Ctx) error {
 	_, err = sac.SpectrumAlertService.DeleteBulk(ctx, deleteItem.Ids)
 	return common.NewResult(c, err)
 }
-
-// GetAttackData 获取攻击数据  SpectrumAlert
-//
-//	@Description Query 获取攻击数据 SpectrumAlert
-//	@Summary Query 获取攻击数据 SpectrumAlert
-//	@Tags SpectrumAlert
-//	@Accept json
-//	@Produce json
-//
-// @Param current query integer false "当前页"
-// @Param pageSize query integer false "分页大小"
-// @Param order query string false "排序，默认id逆序(-id)"
-//
-//	@Success 200 {object} common.Result{data=[]ent.SpectrumAlert}
-//	@Router /api/v1/spectrumalert/attacking [get]
-func (sac *SpectrumAlertController) GetAttackData(c *fiber.Ctx) error {
-	sa := &ent.SpectrumAlert{}
-	attack := &entity.AttackQuery{}
-	qp, err := common.QueryParser(c, attack)
-	if err != nil {
-		return common.NewResult(c, err)
-	}
-	ctx := c.Locals("ctx").(context.Context)
-	var count int
-	var result []*ent.SpectrumAlert
-	if !attack.Attacking {
-		count, result, err = sac.SpectrumAlertService.GetEndTimeNil(ctx, sa, qp)
-	} else {
-		count, result, err = sac.SpectrumAlertService.GetEndTimeNotNil(ctx, sa, qp)
-	}
-	return common.NewPageResult(c, err, count, result)
-}
diff --git a/app/controller/spectrum_data.go b/app/controller/spectrum_data.go
index dbb61fb..42ad013 100644
--- a/app/controller/spectrum_data.go
+++ b/app/controller/spectrum_data.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type SpectrumDataController struct {
@@ -21,38 +21,38 @@ type SpectrumDataController struct {
 //	@Tags SpectrumData
 //	@Accept json
 //	@Produce json
-//	@Param IP query string false "IP" Format(ipv4)
 //	@Param created_at query string false "created_at" Format(date-time)
 //
-// @Param Time query string false "Time" Format(date-time)
-// @Param DataType query integer false "DataType"
-// @Param MatrixBps query integer false "MatrixBps"
-// @Param MatrixPps query integer false "MatrixPps"
-// @Param SynBps query integer false "SynBps"
-// @Param SynPps query integer false "SynPps"
-// @Param AckBps query integer false "AckBps"
-// @Param AckPps query integer false "AckPps"
-// @Param SynAckBps query integer false "SynAckBps"
-// @Param SynAckPps query integer false "SynAckPps"
-// @Param IcmpBps query integer false "IcmpBps"
-// @Param IcmpPps query integer false "IcmpPps"
-// @Param SmallPps query integer false "SmallPps"
-// @Param NtpPps query integer false "NtpPps"
-// @Param NtpBps query integer false "NtpBps"
-// @Param DnsQueryPps query integer false "DnsQueryPps"
-// @Param DnsQueryBps query integer false "DnsQueryBps"
-// @Param DnsAnswerPps query integer false "DnsAnswerPps"
-// @Param DnsAnswerBps query integer false "DnsAnswerBps"
-// @Param SsdpBps query integer false "SsdpBps"
-// @Param SsdpPps query integer false "SsdpPps"
-// @Param UdpPps query integer false "UdpPps"
-// @Param UdpBps query integer false "UdpBps"
-// @Param QPS query integer false "QPS"
-// @Param ReceiveCount query integer false "ReceiveCount"
-// @Param IpType query integer false "IpType"
-// @Param Monitor query string false "Monitor"
-// @Param Product query string false "Product"
-// @Param Host query string false "Host"
+// @Param ip query string false "ip" Format(ipv4)
+// @Param time query string false "time" Format(date-time)
+// @Param data_type query integer false "data_type"
+// @Param bps query integer false "bps"
+// @Param pps query integer false "pps"
+// @Param syn_bps query integer false "syn_bps"
+// @Param syn_pps query integer false "syn_pps"
+// @Param ack_bps query integer false "ack_bps"
+// @Param ack_pps query integer false "ack_pps"
+// @Param syn_ack_bps query integer false "syn_ack_bps"
+// @Param syn_ack_pps query integer false "syn_ack_pps"
+// @Param icmp_bps query integer false "icmp_bps"
+// @Param icmp_pps query integer false "icmp_pps"
+// @Param small_pps query integer false "small_pps"
+// @Param ntp_pps query integer false "ntp_pps"
+// @Param ntp_bps query integer false "ntp_bps"
+// @Param dns_query_pps query integer false "dns_query_pps"
+// @Param dns_query_bps query integer false "dns_query_bps"
+// @Param dns_answer_pps query integer false "dns_answer_pps"
+// @Param dns_answer_bps query integer false "dns_answer_bps"
+// @Param ssdp_bps query integer false "ssdp_bps"
+// @Param ssdp_pps query integer false "ssdp_pps"
+// @Param udp_pps query integer false "udp_pps"
+// @Param udp_bps query integer false "udp_bps"
+// @Param qps query integer false "qps"
+// @Param receive_count query integer false "receive_count"
+// @Param ip_type query integer false "ip_type"
+// @Param monitor query string false "monitor"
+// @Param product query string false "product"
+// @Param host query string false "host"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/strategy.go b/app/controller/strategy.go
index 40d974a..28e75f0 100644
--- a/app/controller/strategy.go
+++ b/app/controller/strategy.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type StrategyController struct {
@@ -21,17 +21,19 @@ type StrategyController struct {
 //	@Tags Strategy
 //	@Accept json
 //	@Produce json
-//	@Param Remark query string false "Remark"
 //	@Param created_at query string false "created_at" Format(date-time)
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
-// @Param Name query string false "Name"
-// @Param NetType query string false "NetType"
-// @Param Base query bool false "Base"
-// @Param MatrixBps query integer false "MatrixBps"
-// @Param MatrixPps query integer false "MatrixPps"
-// @Param BpsCount query integer false "BpsCount"
-// @Param PpsCount query integer false "PpsCount"
+// @Param remark query string false "remark"
+// @Param name query string false "name"
+// @Param type query integer false "type"
+// @Param enabled query bool false "enabled"
+// @Param system query bool false "system"
+// @Param bps query integer false "bps"
+// @Param pps query integer false "pps"
+// @Param bps_count query integer false "bps_count"
+// @Param pps_count query integer false "pps_count"
+// @Param isp_code query integer false "isp_code"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/system_api.go b/app/controller/system_api.go
index be67bb2..c43deae 100644
--- a/app/controller/system_api.go
+++ b/app/controller/system_api.go
@@ -3,17 +3,15 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type SystemApiController struct {
 	SystemApiService *service.SystemApiService
 	Logger           *zap.Logger
-	Rdb              *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 SystemApi
@@ -31,6 +29,7 @@ type SystemApiController struct {
 // @Param path query string false "path"
 // @Param http_method query string false "http_method"
 // @Param public query bool false "public"
+// @Param sa query bool false "sa"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/system_config.go b/app/controller/system_config.go
index d049d32..ffb80e4 100644
--- a/app/controller/system_config.go
+++ b/app/controller/system_config.go
@@ -3,18 +3,15 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
-	"strings"
 )
 
 type SystemConfigController struct {
 	SystemConfigService *service.SystemConfigService
 	Logger              *zap.Logger
-	Rdb                 *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 SystemConfig
@@ -131,16 +128,6 @@ func (scc *SystemConfigController) UpdateByID(c *fiber.Ctx) error {
 		return common.NewResult(c, err)
 	}
 	data, err := scc.SystemConfigService.UpdateByID(ctx, sc, id)
-	_, err = scc.Rdb.HSet(ctx, common.NotifyPrefix, map[string]string{
-		"wofangTestIP": data.WofangTestIP,
-		"notifyScenes": strings.Join(*data.NotifyScenes, ","),
-		"notifyPhones": strings.Join(*data.NotifyPhones, ","),
-		"notifyEmails": strings.Join(*data.NotifyEmails, ","),
-		"ipWhitelists": strings.Join(*data.IPWhitelists, ","),
-	}).Result()
-	if err != nil {
-		return common.NewResult(c, err)
-	}
 	return common.NewResult(c, err, data)
 }
 
diff --git a/app/controller/tenant.go b/app/controller/tenant.go
index 50c67b0..81bce3c 100644
--- a/app/controller/tenant.go
+++ b/app/controller/tenant.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type TenantController struct {
@@ -21,8 +21,11 @@ type TenantController struct {
 //	@Tags Tenant
 //	@Accept json
 //	@Produce json
-//	@Param Name query string false "Name"
+//	@Param name query string false "name"
 //
+// @Param code query string false "code"
+// @Param offline query bool false "offline"
+// @Param isdefend query bool false "isdefend"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/user.go b/app/controller/user.go
index 6bf3090..db5e118 100644
--- a/app/controller/user.go
+++ b/app/controller/user.go
@@ -3,18 +3,17 @@ package controller
 import (
 	"context"
 	"errors"
-	"fmt"
-	"meta/app/ent"
-	"meta/app/ent/tenant"
-	"meta/app/entity"
-	"meta/app/entity/config"
-	"meta/app/service"
-	"meta/pkg/auth"
-	"meta/pkg/common"
-	"meta/pkg/jwt"
-
 	"github.com/casbin/casbin/v2"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/tenant"
+	"github.com/one-meta/meta/app/entity"
+	"github.com/one-meta/meta/app/entity/config"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/auth"
+	"github.com/one-meta/meta/pkg/common"
+	"github.com/one-meta/meta/pkg/jwt"
+	"github.com/redis/go-redis/v9"
 	"go.uber.org/zap"
 )
 
@@ -23,6 +22,7 @@ type UserController struct {
 	Logger      *zap.Logger
 	Enf         *casbin.Enforcer
 	AuthEnt     *auth.Entx
+	Rdb         *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 User
@@ -32,12 +32,13 @@ type UserController struct {
 //	@Tags User
 //	@Accept json
 //	@Produce json
-//	@Param Name query string false "Name"
 //	@Param created_at query string false "created_at" Format(date-time)
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
-// @Param Password query string false "Password" Format(password)
-// @Param SuperAdmin query bool false "SuperAdmin"
+// @Param name query string false "name"
+// @Param password query string false "password" Format(password)
+// @Param super_admin query bool false "super_admin"
+// @Param update_auth query bool false "update_auth"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
@@ -84,7 +85,7 @@ func (uc *UserController) QueryByID(c *fiber.Ctx) error {
 //	@Success 200 {object} common.Result{data=ent.User}
 //	@Router /api/v1/user [post]
 func (uc *UserController) Create(c *fiber.Ctx) error {
-	u := &ent.User{Valid: true, SuperAdmin: false}
+	u := &ent.User{Valid: true, SuperAdmin: false, UpdateAuth: false}
 	err := common.BodyParser(c, u)
 	if err != nil {
 		return common.NewResult(c, err)
@@ -190,7 +191,7 @@ func (uc *UserController) DeleteBulk(c *fiber.Ctx) error {
 //	@Accept json
 //	@Produce json
 //	@Param user body ent.User true "User"
-//	@Success 200 {object} common.Result{data=string}
+//	@Success 200 {object} common.Result{data=entity.UserInfo}
 //	@Router /api/v1/user/login [post]
 func (uc *UserController) Login(c *fiber.Ctx) error {
 	u := &ent.User{}
@@ -223,7 +224,6 @@ func (uc *UserController) Login(c *fiber.Ctx) error {
 		}
 		projects = append(projects, project)
 	}
-
 	if len(projects) > 0 {
 		loginInfo.Projects = projects
 	}
@@ -237,8 +237,8 @@ func (uc *UserController) Login(c *fiber.Ctx) error {
 //	@Tags User
 //	@Accept json
 //	@Produce json
-//
-//	@Success 200 {object} common.Message
+//	@Param user body ent.User true "User"
+//	@Success 200 {object} common.Result{data=entity.UserInfo}
 //	@Router /api/v1/user/logout [get]
 func (uc *UserController) Logout(c *fiber.Ctx) error {
 	authorization := c.Get("Authorization")
@@ -249,14 +249,15 @@ func (uc *UserController) Logout(c *fiber.Ctx) error {
 	return common.NewResult(c, err)
 }
 
-// UserInfo 获取当前用户信息 User
+// UserInfo 获取用户信息 User
 //
-//	@Description UserInfo 获取当前用户信息 User
-//	@Summary UserInfo 获取当前用户信息 User
+//	@Description UserInfo 获取用户信息 User
+//	@Summary UserInfo 获取用户信息 User
 //	@Tags User
 //	@Accept json
 //	@Produce json
-//	@Success 200 {object} common.Result{data=string}
+//
+//	@Success 200 {object} common.Message
 //	@Router /api/v1/user/info [get]
 func (uc *UserController) UserInfo(c *fiber.Ctx) error {
 	if c.Locals("casbinUser") == nil {
@@ -278,14 +279,6 @@ func (uc *UserController) UserInfo(c *fiber.Ctx) error {
 	if err != nil {
 		return common.NewResult(c, err)
 	}
-	if !queryUser.Valid {
-		return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
-	}
-	//提示权限未处理完毕
-	//if queryUser.UpdateAuth{
-	//	return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
-	//}
-
 	userInfo := &entity.UserInfo{}
 	if queryUser.Valid {
 		admin := queryUser.SuperAdmin
@@ -309,24 +302,6 @@ func (uc *UserController) UserInfo(c *fiber.Ctx) error {
 			}
 			userAccess := &entity.Access{}
 			for _, v := range forUser {
-				//switch {
-				////xx权限开头，赋予对应权限，适用于
-				//不行
-				//case strings.HasPrefix(v, "query"):
-				//	userAccess.Query = true
-				//case strings.HasPrefix(v, "new"):
-				//	userAccess.New = true
-				//case strings.HasPrefix(v, "edit"):
-				//	userAccess.Edit = true
-				//case strings.HasPrefix(v, "viewDetail"):
-				//	userAccess.ViewDetail = true
-				//case strings.HasPrefix(v, "view"):
-				//	userAccess.View = true
-				//case strings.HasPrefix(v, "delete"):
-				//	userAccess.Delete = true
-				//case strings.HasPrefix(v, "bulkDelete"):
-				//	userAccess.BulkDelete = true
-				//}
 				switch v {
 				case "query":
 					userAccess.Query = true
@@ -354,18 +329,12 @@ func (uc *UserController) UserInfo(c *fiber.Ctx) error {
 		}
 		var projects []entity.Project
 		userDomains, _ := uc.Enf.GetDomainsForUser(queryUser.Name)
-		fmt.Printf("userDomains:%+v", userDomains)
 		saContext := uc.AuthEnt.GetSAContext()
 		for _, v := range userDomains {
 			queryProject, err := uc.UserService.Dao.Tenant.Query().Where(tenant.Code(v)).Only(saContext)
-			fmt.Printf("v:%s, queryProject:%+v", v, queryProject)
-
 			if err != nil {
 				uc.Logger.Sugar().Info(err)
 			}
-			if queryProject == nil {
-				continue
-			}
 			project := entity.Project{
 				Name: queryProject.Name,
 				Code: v,
@@ -374,9 +343,8 @@ func (uc *UserController) UserInfo(c *fiber.Ctx) error {
 		}
 		if len(projects) > 0 {
 			loginInfo.Projects = projects
+			userInfo.LoginInfo = *loginInfo
 		}
-		userInfo.LoginInfo = *loginInfo
-
 	}
 	return common.NewResult(c, err, userInfo)
 }
diff --git a/app/controller/user_operation_log.go b/app/controller/user_operation_log.go
index 03e261d..f7fc44c 100644
--- a/app/controller/user_operation_log.go
+++ b/app/controller/user_operation_log.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type UserOperationLogController struct {
@@ -21,15 +21,15 @@ type UserOperationLogController struct {
 //	@Tags UserOperationLog
 //	@Accept json
 //	@Produce json
-//	@Param created_at query string false "created_at" Format(date-time)
+//	@Param remark query string false "remark"
 //
+// @Param created_at query string false "created_at" Format(date-time)
 // @Param updated_at query string false "updated_at" Format(date-time)
 // @Param username query string false "username"
 // @Param method query string false "method"
 // @Param uri query string false "uri"
 // @Param request_body query string false "request_body"
 // @Param project query string false "project"
-// @Param time query string false "time" Format(date-time)
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/controller/wire_set.go b/app/controller/wire_set.go
index adae49b..5109d15 100644
--- a/app/controller/wire_set.go
+++ b/app/controller/wire_set.go
@@ -1,46 +1,33 @@
 package controller
 
-import (
-	"github.com/google/wire"
-)
+import "github.com/google/wire"
 
 // Controller wire set
 var (
-	Set = wire.NewSet(GroupSet, NotifySet, NdsSet, CasbinRuleSet, CleanDataSet,
-		ProtectGroupSet, SpectrumAlertSet, SpectrumDataSet, StrategySet, TenantSet, UserSet,
-		WoFangApiSet, SocGroupSet, WoFangSet, SocGroupTicketSet, SystemApiSet, CloudAlertSet,
-		WoFangApi2Set, WoFangAlertSet, CloudFlowDataSet, ProjectSet, SkylineDosSet, CloudAttackDataSet,
-		MatrixStrategySet, MatrixSpectrumDataSet, MatrixSpectrumAlertSet, ChartSet, UserOperationLogSet,
-		SystemConfigSet, DataSyncSet,
-	)
-	GroupSet               = wire.NewSet(wire.Struct(new(GroupController), "*"))
-	NotifySet              = wire.NewSet(wire.Struct(new(NotifyController), "*"))
-	NdsSet                 = wire.NewSet(wire.Struct(new(NdsController), "*"))
-	CasbinRuleSet          = wire.NewSet(wire.Struct(new(CasbinRuleController), "*"))
-	CleanDataSet           = wire.NewSet(wire.Struct(new(CleanDataController), "*"))
+	Set                    = wire.NewSet(MatrixSpectrumAlertSet, ProtectGroupSet, SocGroupTicketSet, SpectrumAlertSet, SystemApiSet, SystemConfigSet, CleanDataSet, GroupSet, WofangAlertSet, UserSet, UserOperationLogSet, MatrixStrategySet, StrategySet, CasbinRuleSet, CloudFlowDataSet, TenantSet, WofangSet, CloudAlertSet, DataSyncSet, MatrixSpectrumDataSet, NotifySet, SkylineDosSet, SpectrumDataSet, CloudAttackDataSet, HelloUserSet)
+	MatrixSpectrumAlertSet = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertController), "*"))
 	ProtectGroupSet        = wire.NewSet(wire.Struct(new(ProtectGroupController), "*"))
+	SocGroupTicketSet      = wire.NewSet(wire.Struct(new(SocGroupTicketController), "*"))
 	SpectrumAlertSet       = wire.NewSet(wire.Struct(new(SpectrumAlertController), "*"))
-	SpectrumDataSet        = wire.NewSet(wire.Struct(new(SpectrumDataController), "*"))
+	SystemApiSet           = wire.NewSet(wire.Struct(new(SystemApiController), "*"))
+	SystemConfigSet        = wire.NewSet(wire.Struct(new(SystemConfigController), "*"))
+	CleanDataSet           = wire.NewSet(wire.Struct(new(CleanDataController), "*"))
+	GroupSet               = wire.NewSet(wire.Struct(new(GroupController), "*"))
+	WofangAlertSet         = wire.NewSet(wire.Struct(new(WofangAlertController), "*"))
+	UserSet                = wire.NewSet(wire.Struct(new(UserController), "*"))
+	UserOperationLogSet    = wire.NewSet(wire.Struct(new(UserOperationLogController), "*"))
+	MatrixStrategySet      = wire.NewSet(wire.Struct(new(MatrixStrategyController), "*"))
 	StrategySet            = wire.NewSet(wire.Struct(new(StrategyController), "*"))
+	CasbinRuleSet          = wire.NewSet(wire.Struct(new(CasbinRuleController), "*"))
+	CloudFlowDataSet       = wire.NewSet(wire.Struct(new(CloudFlowDataController), "*"))
 	TenantSet              = wire.NewSet(wire.Struct(new(TenantController), "*"))
-	UserSet                = wire.NewSet(wire.Struct(new(UserController), "*"))
-	WoFangApiSet           = wire.NewSet(wire.Struct(new(WoFangApi), "*"))
-	WoFangApi2Set          = wire.NewSet(wire.Struct(new(WoFangApi2), "*"))
-	SocGroupSet            = wire.NewSet(wire.Struct(new(SocGroupController), "*"))
-	WoFangSet              = wire.NewSet(wire.Struct(new(WofangController), "*"))
-	SocGroupTicketSet      = wire.NewSet(wire.Struct(new(SocGroupTicketController), "*"))
-	SystemApiSet           = wire.NewSet(wire.Struct(new(SystemApiController), "*"))
+	WofangSet              = wire.NewSet(wire.Struct(new(WofangController), "*"))
 	CloudAlertSet          = wire.NewSet(wire.Struct(new(CloudAlertController), "*"))
-	CloudFlowDataSet       = wire.NewSet(wire.Struct(new(CloudFlowDataController), "*"))
-	CloudAttackDataSet     = wire.NewSet(wire.Struct(new(CloudAttackDataController), "*"))
-	WoFangAlertSet         = wire.NewSet(wire.Struct(new(WofangAlertController), "*"))
-	ProjectSet             = wire.NewSet(wire.Struct(new(ProjectController), "*"))
-	SkylineDosSet          = wire.NewSet(wire.Struct(new(SkylineDosController), "*"))
-	MatrixStrategySet      = wire.NewSet(wire.Struct(new(MatrixStrategyController), "*"))
-	MatrixSpectrumDataSet  = wire.NewSet(wire.Struct(new(MatrixSpectrumDataController), "*"))
-	MatrixSpectrumAlertSet = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertController), "*"))
-	ChartSet               = wire.NewSet(wire.Struct(new(ChartController), "*"))
-	UserOperationLogSet    = wire.NewSet(wire.Struct(new(UserOperationLogController), "*"))
-	SystemConfigSet        = wire.NewSet(wire.Struct(new(SystemConfigController), "*"))
 	DataSyncSet            = wire.NewSet(wire.Struct(new(DataSyncController), "*"))
+	MatrixSpectrumDataSet  = wire.NewSet(wire.Struct(new(MatrixSpectrumDataController), "*"))
+	NotifySet              = wire.NewSet(wire.Struct(new(NotifyController), "*"))
+	SkylineDosSet          = wire.NewSet(wire.Struct(new(SkylineDosController), "*"))
+	SpectrumDataSet        = wire.NewSet(wire.Struct(new(SpectrumDataController), "*"))
+	CloudAttackDataSet     = wire.NewSet(wire.Struct(new(CloudAttackDataController), "*"))
+	HelloUserSet           = wire.NewSet(wire.Struct(new(HelloUserController), "*"))
 )
diff --git a/app/controller/wofang.go b/app/controller/wofang.go
index 673572d..7738717 100644
--- a/app/controller/wofang.go
+++ b/app/controller/wofang.go
@@ -2,24 +2,16 @@ package controller
 
 import (
 	"context"
-	"errors"
 	"github.com/gofiber/fiber/v2"
-	"github.com/redis/go-redis/v9"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/entity/wofang"
-	"meta/app/service"
-	"meta/pkg/common"
-	"meta/pkg/http"
-	"net"
-	"strings"
-	"time"
 )
 
 type WofangController struct {
 	WofangService *service.WofangService
 	Logger        *zap.Logger
-	Rdb           *redis.Client
 }
 
 // Query 根据指定字段、时间范围查询或搜索 Wofang
@@ -37,7 +29,8 @@ type WofangController struct {
 // @Param ip query string false "ip" Format(ipv4)
 // @Param type query string false "type"
 // @Param un_drag_second query integer false "un_drag_second"
-// @Param api_response query string false "api_response"
+// @Param start_time query string false "start_time" Format(date-time)
+// @Param error_info query string false "error_info"
 // @Param status query string false "status"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
@@ -90,29 +83,8 @@ func (wc *WofangController) Create(c *fiber.Ctx) error {
 	if err != nil {
 		return common.NewResult(c, err)
 	}
-	parseIP := net.ParseIP(strings.TrimSpace(w.IP))
-	if parseIP == nil {
-		return common.NewResult(c, errors.New("ip错误"))
-	}
-	dataMap := wofang.NewDrag2("Drain", w.IP, w.UnDragSecond, w.StartTime)
-	drag, err := http.WoFangDrag2(dataMap)
-	if drag == nil || err != nil {
-		if drag != nil {
-			w.ErrorInfo = drag.Message
-		} else {
-			w.ErrorInfo = err.Error()
-		}
-		w.Status = "failed"
-		wc.Logger.Sugar().Error(err)
-	}
-
-	if drag != nil && drag.Code == 0 {
-		w.Status = "success"
-	}
 	ctx := c.Locals("ctx").(context.Context)
 	create, err := wc.WofangService.Create(ctx, w)
-	wc.Rdb.Set(ctx, common.Key10010CleanPrefix+w.IP, "1", time.Second*time.Duration(w.UnDragSecond))
-
 	return common.NewResult(c, err, create)
 }
 
diff --git a/app/controller/wofang_alert.go b/app/controller/wofang_alert.go
index ce3cb56..2967743 100644
--- a/app/controller/wofang_alert.go
+++ b/app/controller/wofang_alert.go
@@ -3,10 +3,10 @@ package controller
 import (
 	"context"
 	"github.com/gofiber/fiber/v2"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/service"
+	"github.com/one-meta/meta/pkg/common"
 	"go.uber.org/zap"
-	"meta/app/ent"
-	"meta/app/service"
-	"meta/pkg/common"
 )
 
 type WofangAlertController struct {
@@ -25,14 +25,13 @@ type WofangAlertController struct {
 //
 // @Param updated_at query string false "updated_at" Format(date-time)
 // @Param remark query string false "remark"
-// @Param attack_status query string false "attack_status"
-// @Param attack_type query string false "attack_type"
+// @Param attack_status query integer false "attack_status"
 // @Param device_ip query string false "device_ip"
 // @Param zone_ip query string false "zone_ip"
 // @Param start_time query string false "start_time" Format(date-time)
 // @Param end_time query string false "end_time" Format(date-time)
-// @Param max_drop_kbps query integer false "max_drop_kbps"
-// @Param max_in_kbps query integer false "max_in_kbps"
+// @Param max_drop_bps query integer false "max_drop_bps"
+// @Param max_in_bps query integer false "max_in_bps"
 // @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
 // @Param current query integer false "当前页"
 // @Param pageSize query integer false "分页大小"
diff --git a/app/ent/extend/query_param.go b/app/ent/extend/query_param.go
index 48719a7..079329d 100644
--- a/app/ent/extend/query_param.go
+++ b/app/ent/extend/query_param.go
@@ -5,15 +5,6 @@ import "time"
 
 // 时间参数结构体
 type TimeParam struct {
-	StartTimeGte time.Time `query:"start_time_gte"`
-	StartTimeLte time.Time `query:"start_time_lte"`
-
-	EndTimeGte time.Time `query:"end_time_gte"`
-	EndTimeLte time.Time `query:"end_time_lte"`
-
-	OpTimeGte time.Time `query:"op_time_gte"`
-	OpTimeLte time.Time `query:"op_time_lte"`
-
 	CreatedAtGte time.Time `query:"created_at_gte"`
 	CreatedAtLte time.Time `query:"created_at_lte"`
 
@@ -22,4 +13,13 @@ type TimeParam struct {
 
 	UpdatedAtGte time.Time `query:"updated_at_gte"`
 	UpdatedAtLte time.Time `query:"updated_at_lte"`
+
+	StartTimeGte time.Time `query:"start_time_gte"`
+	StartTimeLte time.Time `query:"start_time_lte"`
+
+	EndTimeGte time.Time `query:"end_time_gte"`
+	EndTimeLte time.Time `query:"end_time_lte"`
+
+	OpTimeGte time.Time `query:"op_time_gte"`
+	OpTimeLte time.Time `query:"op_time_lte"`
 }
diff --git a/app/service/casbin_rule.go b/app/service/casbin_rule.go
index 685f0ca..e4d0fa2 100644
--- a/app/service/casbin_rule.go
+++ b/app/service/casbin_rule.go
@@ -4,10 +4,10 @@ import (
 	"context"
 	"errors"
 	"github.com/casbin/casbin/v2"
-	"meta/app/ent"
-	"meta/app/ent/casbinrule"
-	"meta/app/entity"
-	"meta/pkg/common"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/casbinrule"
+	"github.com/one-meta/meta/app/entity"
+	"github.com/one-meta/meta/pkg/common"
 )
 
 type CasbinRuleService struct {
diff --git a/app/service/clean_data.go b/app/service/clean_data.go
index 50762aa..89f13b4 100644
--- a/app/service/clean_data.go
+++ b/app/service/clean_data.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/cleandata"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cleandata"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type CleanDataService struct {
@@ -22,7 +22,7 @@ func (cds *CleanDataService) Query(ctx context.Context, cd *ent.CleanData, qp *e
 
 // QueryByID 根据 ID 查询 CleanData
 func (cds *CleanDataService) QueryByID(ctx context.Context, id int) (*ent.CleanData, error) {
-	return cds.Dao.CleanData.Query().Where(cleandata.ID(id)).WithTenant().Only(ctx)
+	return cds.Dao.CleanData.Query().Where(cleandata.ID(id)).Only(ctx)
 }
 
 // Create 创建 CleanData
@@ -39,15 +39,6 @@ func (cds *CleanDataService) CreateBulk(ctx context.Context, cd []*ent.CleanData
 	return cds.Dao.CleanData.CreateBulk(bulks...).Save(ctx)
 }
 
-// CreateBulk2 批量创建 CleanData
-func (cds *CleanDataService) CreateBulk2(ctx context.Context, cd []*ent.CleanData, id int) ([]*ent.CleanData, error) {
-	bulks := make([]*ent.CleanDataCreate, len(cd))
-	for i, v := range cd {
-		bulks[i] = cds.Dao.CleanData.Create().SetItemCleanData(v).SetSpectrumAlertID(id)
-	}
-	return cds.Dao.CleanData.CreateBulk(bulks...).Save(ctx)
-}
-
 // UpdateByID 根据 ID 修改 CleanData
 func (cds *CleanDataService) UpdateByID(ctx context.Context, cd *ent.CleanData, id int) (*ent.CleanData, error) {
 	return cds.Dao.CleanData.UpdateOneID(id).SetItemCleanData(cd).Save(ctx)
@@ -70,7 +61,7 @@ func (cds *CleanDataService) QueryPage(ctx context.Context, cd *ent.CleanData, q
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cds.Dao.CleanData.Query().QueryItemCleanData(cd, qp, false).WithTenant().All(ctx)
+	results, err := cds.Dao.CleanData.Query().QueryItemCleanData(cd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -86,7 +77,7 @@ func (cds *CleanDataService) QuerySearch(ctx context.Context, cd *ent.CleanData,
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cds.Dao.CleanData.Query().SearchCleanData(cd, qp, false).WithTenant().All(ctx)
+	results, err := cds.Dao.CleanData.Query().SearchCleanData(cd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/cloud_alert.go b/app/service/cloud_alert.go
index 92710e8..0f2f08b 100644
--- a/app/service/cloud_alert.go
+++ b/app/service/cloud_alert.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/cloudalert"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudalert"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type CloudAlertService struct {
@@ -22,7 +22,7 @@ func (cas *CloudAlertService) Query(ctx context.Context, ca *ent.CloudAlert, qp
 
 // QueryByID 根据 ID 查询 CloudAlert
 func (cas *CloudAlertService) QueryByID(ctx context.Context, id int) (*ent.CloudAlert, error) {
-	return cas.Dao.CloudAlert.Query().Where(cloudalert.ID(id)).WithTenant().Only(ctx)
+	return cas.Dao.CloudAlert.Query().Where(cloudalert.ID(id)).Only(ctx)
 }
 
 // Create 创建 CloudAlert
@@ -61,7 +61,7 @@ func (cas *CloudAlertService) QueryPage(ctx context.Context, ca *ent.CloudAlert,
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cas.Dao.CloudAlert.Query().QueryItemCloudAlert(ca, qp, false).WithTenant().All(ctx)
+	results, err := cas.Dao.CloudAlert.Query().QueryItemCloudAlert(ca, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (cas *CloudAlertService) QuerySearch(ctx context.Context, ca *ent.CloudAler
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cas.Dao.CloudAlert.Query().SearchCloudAlert(ca, qp, false).WithTenant().All(ctx)
+	results, err := cas.Dao.CloudAlert.Query().SearchCloudAlert(ca, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/cloud_attack_data.go b/app/service/cloud_attack_data.go
index 0953d76..6744e9c 100644
--- a/app/service/cloud_attack_data.go
+++ b/app/service/cloud_attack_data.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/cloudattackdata"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudattackdata"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type CloudAttackDataService struct {
@@ -22,7 +22,7 @@ func (cads *CloudAttackDataService) Query(ctx context.Context, cad *ent.CloudAtt
 
 // QueryByID 根据 ID 查询 CloudAttackData
 func (cads *CloudAttackDataService) QueryByID(ctx context.Context, id int) (*ent.CloudAttackData, error) {
-	return cads.Dao.CloudAttackData.Query().Where(cloudattackdata.ID(id)).WithTenant().Only(ctx)
+	return cads.Dao.CloudAttackData.Query().Where(cloudattackdata.ID(id)).Only(ctx)
 }
 
 // Create 创建 CloudAttackData
@@ -61,7 +61,7 @@ func (cads *CloudAttackDataService) QueryPage(ctx context.Context, cad *ent.Clou
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cads.Dao.CloudAttackData.Query().QueryItemCloudAttackData(cad, qp, false).WithTenant().All(ctx)
+	results, err := cads.Dao.CloudAttackData.Query().QueryItemCloudAttackData(cad, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (cads *CloudAttackDataService) QuerySearch(ctx context.Context, cad *ent.Cl
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cads.Dao.CloudAttackData.Query().SearchCloudAttackData(cad, qp, false).WithTenant().All(ctx)
+	results, err := cads.Dao.CloudAttackData.Query().SearchCloudAttackData(cad, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/cloud_flow_data.go b/app/service/cloud_flow_data.go
index 7984a25..1c60b71 100644
--- a/app/service/cloud_flow_data.go
+++ b/app/service/cloud_flow_data.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/cloudflowdata"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/cloudflowdata"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type CloudFlowDataService struct {
@@ -22,7 +22,7 @@ func (cfds *CloudFlowDataService) Query(ctx context.Context, cfd *ent.CloudFlowD
 
 // QueryByID 根据 ID 查询 CloudFlowData
 func (cfds *CloudFlowDataService) QueryByID(ctx context.Context, id int) (*ent.CloudFlowData, error) {
-	return cfds.Dao.CloudFlowData.Query().Where(cloudflowdata.ID(id)).WithTenant().Only(ctx)
+	return cfds.Dao.CloudFlowData.Query().Where(cloudflowdata.ID(id)).Only(ctx)
 }
 
 // Create 创建 CloudFlowData
@@ -61,7 +61,7 @@ func (cfds *CloudFlowDataService) QueryPage(ctx context.Context, cfd *ent.CloudF
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cfds.Dao.CloudFlowData.Query().QueryItemCloudFlowData(cfd, qp, false).WithTenant().All(ctx)
+	results, err := cfds.Dao.CloudFlowData.Query().QueryItemCloudFlowData(cfd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (cfds *CloudFlowDataService) QuerySearch(ctx context.Context, cfd *ent.Clou
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := cfds.Dao.CloudFlowData.Query().SearchCloudFlowData(cfd, qp, false).WithTenant().All(ctx)
+	results, err := cfds.Dao.CloudFlowData.Query().SearchCloudFlowData(cfd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/data_sync.go b/app/service/data_sync.go
index 95cd9e2..aed93dd 100644
--- a/app/service/data_sync.go
+++ b/app/service/data_sync.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/datasync"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/datasync"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type DataSyncService struct {
diff --git a/app/service/group.go b/app/service/group.go
index 7568e61..34a3506 100644
--- a/app/service/group.go
+++ b/app/service/group.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/group"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/group"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type GroupService struct {
@@ -22,7 +22,7 @@ func (gs *GroupService) Query(ctx context.Context, g *ent.Group, qp *entity.Quer
 
 // QueryByID 根据 ID 查询 Group
 func (gs *GroupService) QueryByID(ctx context.Context, id int) (*ent.Group, error) {
-	return gs.Dao.Group.Get(ctx, id)
+	return gs.Dao.Group.Query().Where(group.ID(id)).Only(ctx)
 }
 
 // Create 创建 Group
diff --git a/app/service/matrix_spectrum_alert.go b/app/service/matrix_spectrum_alert.go
index 2d68a39..a9089c0 100644
--- a/app/service/matrix_spectrum_alert.go
+++ b/app/service/matrix_spectrum_alert.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/matrixspectrumalert"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixspectrumalert"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type MatrixSpectrumAlertService struct {
@@ -22,7 +22,7 @@ func (msas *MatrixSpectrumAlertService) Query(ctx context.Context, msa *ent.Matr
 
 // QueryByID 根据 ID 查询 MatrixSpectrumAlert
 func (msas *MatrixSpectrumAlertService) QueryByID(ctx context.Context, id int) (*ent.MatrixSpectrumAlert, error) {
-	return msas.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.ID(id)).WithWofangTicket().WithTenant().WithMatrixStrategy().Only(ctx)
+	return msas.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.ID(id)).Only(ctx)
 }
 
 // Create 创建 MatrixSpectrumAlert
@@ -61,7 +61,7 @@ func (msas *MatrixSpectrumAlertService) QueryPage(ctx context.Context, msa *ent.
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := msas.Dao.MatrixSpectrumAlert.Query().QueryItemMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
+	results, err := msas.Dao.MatrixSpectrumAlert.Query().QueryItemMatrixSpectrumAlert(msa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,24 +77,7 @@ func (msas *MatrixSpectrumAlertService) QuerySearch(ctx context.Context, msa *en
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := msas.Dao.MatrixSpectrumAlert.Query().SearchMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	if len(results) == 0 {
-		count = 0
-	}
-	return count, results, nil
-}
-
-func (msas *MatrixSpectrumAlertService) GetEndTimeNil(ctx context.Context, msa *ent.MatrixSpectrumAlert, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumAlert, error) {
-	count, err := msas.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.EndTimeIsNil()).QueryItemMatrixSpectrumAlert(msa, qp, true).Count(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	results, err := msas.Dao.MatrixSpectrumAlert.Query().
-		Where(matrixspectrumalert.EndTimeIsNil()).
-		QueryItemMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
+	results, err := msas.Dao.MatrixSpectrumAlert.Query().SearchMatrixSpectrumAlert(msa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/matrix_spectrum_data.go b/app/service/matrix_spectrum_data.go
index cd7ea8b..9c5bef3 100644
--- a/app/service/matrix_spectrum_data.go
+++ b/app/service/matrix_spectrum_data.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/matrixspectrumdata"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixspectrumdata"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type MatrixSpectrumDataService struct {
@@ -22,7 +22,7 @@ func (msds *MatrixSpectrumDataService) Query(ctx context.Context, msd *ent.Matri
 
 // QueryByID 根据 ID 查询 MatrixSpectrumData
 func (msds *MatrixSpectrumDataService) QueryByID(ctx context.Context, id int) (*ent.MatrixSpectrumData, error) {
-	return msds.Dao.MatrixSpectrumData.Query().Where(matrixspectrumdata.ID(id)).WithTenant().Only(ctx)
+	return msds.Dao.MatrixSpectrumData.Query().Where(matrixspectrumdata.ID(id)).Only(ctx)
 }
 
 // Create 创建 MatrixSpectrumData
@@ -61,7 +61,7 @@ func (msds *MatrixSpectrumDataService) QueryPage(ctx context.Context, msd *ent.M
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := msds.Dao.MatrixSpectrumData.Query().QueryItemMatrixSpectrumData(msd, qp, false).WithTenant().All(ctx)
+	results, err := msds.Dao.MatrixSpectrumData.Query().QueryItemMatrixSpectrumData(msd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (msds *MatrixSpectrumDataService) QuerySearch(ctx context.Context, msd *ent
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := msds.Dao.MatrixSpectrumData.Query().SearchMatrixSpectrumData(msd, qp, false).WithTenant().All(ctx)
+	results, err := msds.Dao.MatrixSpectrumData.Query().SearchMatrixSpectrumData(msd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/matrix_strategy.go b/app/service/matrix_strategy.go
index 2aeb356..5cf1673 100644
--- a/app/service/matrix_strategy.go
+++ b/app/service/matrix_strategy.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/matrixstrategy"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/matrixstrategy"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type MatrixStrategyService struct {
diff --git a/app/service/notify.go b/app/service/notify.go
index e3a6691..5023234 100644
--- a/app/service/notify.go
+++ b/app/service/notify.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/notify"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/notify"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type NotifyService struct {
@@ -22,7 +22,7 @@ func (ns *NotifyService) Query(ctx context.Context, n *ent.Notify, qp *entity.Qu
 
 // QueryByID 根据 ID 查询 Notify
 func (ns *NotifyService) QueryByID(ctx context.Context, id int) (*ent.Notify, error) {
-	return ns.Dao.Notify.Query().Where(notify.ID(id)).WithTenant().Only(ctx)
+	return ns.Dao.Notify.Query().Where(notify.ID(id)).Only(ctx)
 }
 
 // Create 创建 Notify
@@ -61,7 +61,7 @@ func (ns *NotifyService) QueryPage(ctx context.Context, n *ent.Notify, qp *entit
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ns.Dao.Notify.Query().QueryItemNotify(n, qp, false).WithTenant().All(ctx)
+	results, err := ns.Dao.Notify.Query().QueryItemNotify(n, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (ns *NotifyService) QuerySearch(ctx context.Context, n *ent.Notify, qp *ent
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ns.Dao.Notify.Query().SearchNotify(n, qp, false).WithTenant().All(ctx)
+	results, err := ns.Dao.Notify.Query().SearchNotify(n, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/protect_group.go b/app/service/protect_group.go
index 0edd7a2..d6ab471 100644
--- a/app/service/protect_group.go
+++ b/app/service/protect_group.go
@@ -2,16 +2,16 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/protectgroup"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/protectgroup"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type ProtectGroupService struct {
 	Dao *Dao
 }
 
-// Query 根据指定字段、时间范围查询或搜索 Group
+// Query 根据指定字段、时间范围查询或搜索 ProtectGroup
 func (pgs *ProtectGroupService) Query(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
 	if len(qp.Search) == 0 {
 		return pgs.QueryPage(ctx, pg, qp)
@@ -20,17 +20,17 @@ func (pgs *ProtectGroupService) Query(ctx context.Context, pg *ent.ProtectGroup,
 	}
 }
 
-// QueryByID 根据 ID 查询 Group
+// QueryByID 根据 ID 查询 ProtectGroup
 func (pgs *ProtectGroupService) QueryByID(ctx context.Context, id int) (*ent.ProtectGroup, error) {
-	return pgs.Dao.ProtectGroup.Query().Where(protectgroup.ID(id)).WithTenant().Only(ctx)
+	return pgs.Dao.ProtectGroup.Query().Where(protectgroup.ID(id)).Only(ctx)
 }
 
-// Create 创建 Group
+// Create 创建 ProtectGroup
 func (pgs *ProtectGroupService) Create(ctx context.Context, pg *ent.ProtectGroup) (*ent.ProtectGroup, error) {
 	return pgs.Dao.ProtectGroup.Create().SetItemProtectGroup(pg).Save(ctx)
 }
 
-// CreateBulk 批量创建 Group
+// CreateBulk 批量创建 ProtectGroup
 func (pgs *ProtectGroupService) CreateBulk(ctx context.Context, pg []*ent.ProtectGroup) ([]*ent.ProtectGroup, error) {
 	bulks := make([]*ent.ProtectGroupCreate, len(pg))
 	for i, v := range pg {
@@ -39,29 +39,29 @@ func (pgs *ProtectGroupService) CreateBulk(ctx context.Context, pg []*ent.Protec
 	return pgs.Dao.ProtectGroup.CreateBulk(bulks...).Save(ctx)
 }
 
-// UpdateByID 根据 ID 修改 Group
+// UpdateByID 根据 ID 修改 ProtectGroup
 func (pgs *ProtectGroupService) UpdateByID(ctx context.Context, pg *ent.ProtectGroup, id int) (*ent.ProtectGroup, error) {
 	return pgs.Dao.ProtectGroup.UpdateOneID(id).SetItemProtectGroup(pg).Save(ctx)
 }
 
-// DeleteByID 根据 ID 删除 Group
+// DeleteByID 根据 ID 删除 ProtectGroup
 func (pgs *ProtectGroupService) DeleteByID(ctx context.Context, id int) error {
 	return pgs.Dao.ProtectGroup.DeleteOneID(id).Exec(ctx)
 }
 
-// DeleteBulk 根据 IDs 批量删除 Group
+// DeleteBulk 根据 IDs 批量删除 ProtectGroup
 func (pgs *ProtectGroupService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
 	count, err := pgs.Dao.ProtectGroup.Delete().Where(protectgroup.IDIn(ids...)).Exec(ctx)
 	return count, err
 }
 
-// QueryPage 分页查询 Group
+// QueryPage 分页查询 ProtectGroup
 func (pgs *ProtectGroupService) QueryPage(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
 	count, err := pgs.Dao.ProtectGroup.Query().QueryItemProtectGroup(pg, qp, true).Count(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := pgs.Dao.ProtectGroup.Query().QueryItemProtectGroup(pg, qp, false).WithTenant().All(ctx)
+	results, err := pgs.Dao.ProtectGroup.Query().QueryItemProtectGroup(pg, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -71,13 +71,13 @@ func (pgs *ProtectGroupService) QueryPage(ctx context.Context, pg *ent.ProtectGr
 	return count, results, nil
 }
 
-// QuerySearch 分页搜索 Group
+// QuerySearch 分页搜索 ProtectGroup
 func (pgs *ProtectGroupService) QuerySearch(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
 	count, err := pgs.Dao.ProtectGroup.Query().SearchProtectGroup(pg, qp, true).Count(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := pgs.Dao.ProtectGroup.Query().SearchProtectGroup(pg, qp, false).WithTenant().All(ctx)
+	results, err := pgs.Dao.ProtectGroup.Query().SearchProtectGroup(pg, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/skyline_dos.go b/app/service/skyline_dos.go
index ff63e69..fdce3cd 100644
--- a/app/service/skyline_dos.go
+++ b/app/service/skyline_dos.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/skylinedos"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/skylinedos"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SkylineDosService struct {
@@ -22,7 +22,7 @@ func (sds *SkylineDosService) Query(ctx context.Context, sd *ent.SkylineDos, qp
 
 // QueryByID 根据 ID 查询 SkylineDos
 func (sds *SkylineDosService) QueryByID(ctx context.Context, id int) (*ent.SkylineDos, error) {
-	return sds.Dao.SkylineDos.Query().Where(skylinedos.ID(id)).WithTenant().Only(ctx)
+	return sds.Dao.SkylineDos.Query().Where(skylinedos.ID(id)).Only(ctx)
 }
 
 // Create 创建 SkylineDos
@@ -61,7 +61,7 @@ func (sds *SkylineDosService) QueryPage(ctx context.Context, sd *ent.SkylineDos,
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sds.Dao.SkylineDos.Query().QueryItemSkylineDos(sd, qp, false).WithTenant().All(ctx)
+	results, err := sds.Dao.SkylineDos.Query().QueryItemSkylineDos(sd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (sds *SkylineDosService) QuerySearch(ctx context.Context, sd *ent.SkylineDo
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sds.Dao.SkylineDos.Query().SearchSkylineDos(sd, qp, false).WithTenant().All(ctx)
+	results, err := sds.Dao.SkylineDos.Query().SearchSkylineDos(sd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/soc_group_ticket.go b/app/service/soc_group_ticket.go
index 8005a39..fd0b10f 100644
--- a/app/service/soc_group_ticket.go
+++ b/app/service/soc_group_ticket.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/socgroupticket"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/socgroupticket"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SocGroupTicketService struct {
@@ -22,7 +22,7 @@ func (sgts *SocGroupTicketService) Query(ctx context.Context, sgt *ent.SocGroupT
 
 // QueryByID 根据 ID 查询 SocGroupTicket
 func (sgts *SocGroupTicketService) QueryByID(ctx context.Context, id int) (*ent.SocGroupTicket, error) {
-	return sgts.Dao.SocGroupTicket.Query().Where(socgroupticket.ID(id)).WithTenant().Only(ctx)
+	return sgts.Dao.SocGroupTicket.Query().Where(socgroupticket.ID(id)).Only(ctx)
 }
 
 // Create 创建 SocGroupTicket
@@ -61,7 +61,7 @@ func (sgts *SocGroupTicketService) QueryPage(ctx context.Context, sgt *ent.SocGr
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sgts.Dao.SocGroupTicket.Query().QueryItemSocGroupTicket(sgt, qp, false).WithTenant().All(ctx)
+	results, err := sgts.Dao.SocGroupTicket.Query().QueryItemSocGroupTicket(sgt, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (sgts *SocGroupTicketService) QuerySearch(ctx context.Context, sgt *ent.Soc
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sgts.Dao.SocGroupTicket.Query().SearchSocGroupTicket(sgt, qp, false).WithTenant().All(ctx)
+	results, err := sgts.Dao.SocGroupTicket.Query().SearchSocGroupTicket(sgt, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/spectrum_alert.go b/app/service/spectrum_alert.go
index c788865..1e92078 100644
--- a/app/service/spectrum_alert.go
+++ b/app/service/spectrum_alert.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/spectrumalert"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/spectrumalert"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SpectrumAlertService struct {
@@ -22,8 +22,7 @@ func (sas *SpectrumAlertService) Query(ctx context.Context, sa *ent.SpectrumAler
 
 // QueryByID 根据 ID 查询 SpectrumAlert
 func (sas *SpectrumAlertService) QueryByID(ctx context.Context, id int) (*ent.SpectrumAlert, error) {
-	return sas.Dao.SpectrumAlert.Query().Where(spectrumalert.ID(id)).WithTenant().WithWofangTicket().WithProtectGroup().Only(ctx)
-
+	return sas.Dao.SpectrumAlert.Query().Where(spectrumalert.ID(id)).Only(ctx)
 }
 
 // Create 创建 SpectrumAlert
@@ -62,7 +61,7 @@ func (sas *SpectrumAlertService) QueryPage(ctx context.Context, sa *ent.Spectrum
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, false).WithTenant().WithWofangTicket(selectWofangFields()).WithProtectGroup(selectProtecGroupFields()).All(ctx)
+	results, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -78,49 +77,7 @@ func (sas *SpectrumAlertService) QuerySearch(ctx context.Context, sa *ent.Spectr
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sas.Dao.SpectrumAlert.Query().SearchSpectrumAlert(sa, qp, false).WithTenant().WithWofangTicket(selectWofangFields()).WithProtectGroup(selectProtecGroupFields()).All(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	if len(results) == 0 {
-		count = 0
-	}
-	return count, results, nil
-}
-
-func (sas *SpectrumAlertService) GetEndTimeNotNil(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
-	count, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, true).Count(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	results, err := sas.Dao.SpectrumAlert.Query().
-		QueryItemSpectrumAlert(sa, qp, false).
-		WithTenant().
-		WithWofangTicket(selectWofangFields()).
-		WithProtectGroup(selectProtecGroupFields()).
-		WithStrategy(selectStrategyFields()).
-		All(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	if len(results) == 0 {
-		count = 0
-	}
-	return count, results, nil
-}
-
-func (sas *SpectrumAlertService) GetEndTimeNil(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
-	count, err := sas.Dao.SpectrumAlert.Query().Where(spectrumalert.EndTimeIsNil()).QueryItemSpectrumAlert(sa, qp, true).Count(ctx)
-	if err != nil {
-		return 0, nil, err
-	}
-	results, err := sas.Dao.SpectrumAlert.Query().
-		Where(spectrumalert.EndTimeIsNil()).QueryItemSpectrumAlert(sa, qp, false).
-		WithTenant().
-		WithWofangTicket(selectWofangFields()).
-		WithProtectGroup(selectProtecGroupFields()).
-		WithStrategy(selectStrategyFields()).
-		All(ctx)
+	results, err := sas.Dao.SpectrumAlert.Query().SearchSpectrumAlert(sa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/spectrum_data.go b/app/service/spectrum_data.go
index 25c4873..5f5cac7 100644
--- a/app/service/spectrum_data.go
+++ b/app/service/spectrum_data.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/spectrumdata"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/spectrumdata"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SpectrumDataService struct {
@@ -22,7 +22,7 @@ func (sds *SpectrumDataService) Query(ctx context.Context, sd *ent.SpectrumData,
 
 // QueryByID 根据 ID 查询 SpectrumData
 func (sds *SpectrumDataService) QueryByID(ctx context.Context, id int) (*ent.SpectrumData, error) {
-	return sds.Dao.SpectrumData.Query().Where(spectrumdata.ID(id)).WithTenant().Only(ctx)
+	return sds.Dao.SpectrumData.Query().Where(spectrumdata.ID(id)).Only(ctx)
 }
 
 // Create 创建 SpectrumData
@@ -39,15 +39,6 @@ func (sds *SpectrumDataService) CreateBulk(ctx context.Context, sd []*ent.Spectr
 	return sds.Dao.SpectrumData.CreateBulk(bulks...).Save(ctx)
 }
 
-// CreateBulk2 批量创建 包含分光告警的SpectrumData
-func (sds *SpectrumDataService) CreateBulk2(ctx context.Context, sd []*ent.SpectrumData, id int) ([]*ent.SpectrumData, error) {
-	bulks := make([]*ent.SpectrumDataCreate, len(sd))
-	for i, v := range sd {
-		bulks[i] = sds.Dao.SpectrumData.Create().SetItemSpectrumData(v).SetSpectrumAlertID(id)
-	}
-	return sds.Dao.SpectrumData.CreateBulk(bulks...).Save(ctx)
-}
-
 // UpdateByID 根据 ID 修改 SpectrumData
 func (sds *SpectrumDataService) UpdateByID(ctx context.Context, sd *ent.SpectrumData, id int) (*ent.SpectrumData, error) {
 	return sds.Dao.SpectrumData.UpdateOneID(id).SetItemSpectrumData(sd).Save(ctx)
@@ -70,7 +61,7 @@ func (sds *SpectrumDataService) QueryPage(ctx context.Context, sd *ent.SpectrumD
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sds.Dao.SpectrumData.Query().QueryItemSpectrumData(sd, qp, false).WithTenant().All(ctx)
+	results, err := sds.Dao.SpectrumData.Query().QueryItemSpectrumData(sd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -86,7 +77,7 @@ func (sds *SpectrumDataService) QuerySearch(ctx context.Context, sd *ent.Spectru
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := sds.Dao.SpectrumData.Query().SearchSpectrumData(sd, qp, false).WithTenant().All(ctx)
+	results, err := sds.Dao.SpectrumData.Query().SearchSpectrumData(sd, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/strategy.go b/app/service/strategy.go
index 1c9c64f..db99380 100644
--- a/app/service/strategy.go
+++ b/app/service/strategy.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/strategy"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/strategy"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type StrategyService struct {
@@ -22,7 +22,7 @@ func (ss *StrategyService) Query(ctx context.Context, s *ent.Strategy, qp *entit
 
 // QueryByID 根据 ID 查询 Strategy
 func (ss *StrategyService) QueryByID(ctx context.Context, id int) (*ent.Strategy, error) {
-	return ss.Dao.Strategy.Query().Where(strategy.ID(id)).WithTenant().Only(ctx)
+	return ss.Dao.Strategy.Query().Where(strategy.ID(id)).Only(ctx)
 }
 
 // Create 创建 Strategy
@@ -61,7 +61,7 @@ func (ss *StrategyService) QueryPage(ctx context.Context, s *ent.Strategy, qp *e
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ss.Dao.Strategy.Query().QueryItemStrategy(s, qp, false).WithTenant().All(ctx)
+	results, err := ss.Dao.Strategy.Query().QueryItemStrategy(s, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (ss *StrategyService) QuerySearch(ctx context.Context, s *ent.Strategy, qp
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ss.Dao.Strategy.Query().SearchStrategy(s, qp, false).WithTenant().All(ctx)
+	results, err := ss.Dao.Strategy.Query().SearchStrategy(s, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/system_api.go b/app/service/system_api.go
index 85aa70b..c592798 100644
--- a/app/service/system_api.go
+++ b/app/service/system_api.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/systemapi"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/systemapi"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SystemApiService struct {
diff --git a/app/service/system_config.go b/app/service/system_config.go
index f2a1f41..74a2a6f 100644
--- a/app/service/system_config.go
+++ b/app/service/system_config.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/systemconfig"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/systemconfig"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type SystemConfigService struct {
diff --git a/app/service/tenant.go b/app/service/tenant.go
index a544363..47a4a10 100644
--- a/app/service/tenant.go
+++ b/app/service/tenant.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/tenant"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/tenant"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type TenantService struct {
@@ -22,7 +22,7 @@ func (ts *TenantService) Query(ctx context.Context, t *ent.Tenant, qp *entity.Qu
 
 // QueryByID 根据 ID 查询 Tenant
 func (ts *TenantService) QueryByID(ctx context.Context, id int) (*ent.Tenant, error) {
-	return ts.Dao.Tenant.Get(ctx, id)
+	return ts.Dao.Tenant.Query().Where(tenant.ID(id)).Only(ctx)
 }
 
 // Create 创建 Tenant
diff --git a/app/service/user.go b/app/service/user.go
index 56e8fe9..a7ed381 100644
--- a/app/service/user.go
+++ b/app/service/user.go
@@ -3,13 +3,12 @@ package service
 import (
 	"context"
 	"errors"
-	"meta/app/ent"
-	"meta/app/ent/user"
-	"meta/app/entity"
-	"meta/app/entity/config"
-	"meta/pkg/bcrypt"
-	"meta/pkg/jwt"
-	"strings"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/user"
+	"github.com/one-meta/meta/app/entity"
+	"github.com/one-meta/meta/app/entity/config"
+	"github.com/one-meta/meta/pkg/bcrypt"
+	"github.com/one-meta/meta/pkg/jwt"
 )
 
 type UserService struct {
@@ -27,12 +26,7 @@ func (us *UserService) Query(ctx context.Context, u *ent.User, qp *entity.QueryP
 
 // QueryByID 根据 ID 查询 User
 func (us *UserService) QueryByID(ctx context.Context, id int) (*ent.User, error) {
-	return us.Dao.User.Get(ctx, id)
-}
-
-// QueryByUserName 根据 Name 查询 User
-func (us *UserService) QueryByUserName(ctx context.Context, name string) (*ent.User, error) {
-	return us.Dao.User.Query().Where(user.Name(name)).Only(ctx)
+	return us.Dao.User.Query().Where(user.ID(id)).Only(ctx)
 }
 
 // Create 创建 User
@@ -54,6 +48,7 @@ func (us *UserService) CreateBulk(ctx context.Context, u []*ent.User) ([]*ent.Us
 		}
 		v.Valid = true
 		v.SuperAdmin = false
+		v.UpdateAuth = false
 		v.Password = encodePassword
 	}
 	bulks := make([]*ent.UserCreate, len(u))
@@ -65,13 +60,11 @@ func (us *UserService) CreateBulk(ctx context.Context, u []*ent.User) ([]*ent.Us
 
 // UpdateByID 根据 ID 修改 User
 func (us *UserService) UpdateByID(ctx context.Context, u *ent.User, id int) (*ent.User, error) {
-	if !strings.HasPrefix(u.Password, "$2a$10$") {
-		encodePassword, err := bcrypt.Encode(u.Password)
-		if err != nil {
-			return nil, err
-		}
-		u.Password = encodePassword
+	encodePassword, err := bcrypt.Encode(u.Password)
+	if err != nil {
+		return nil, err
 	}
+	u.Password = encodePassword
 	return us.Dao.User.UpdateOneID(id).SetItemUser(u).Save(ctx)
 }
 
@@ -135,3 +128,8 @@ func (us *UserService) Login(ctx context.Context, u *ent.User) (*ent.User, strin
 	}
 	return nil, "", errors.New("login error")
 }
+
+// QueryByUserName 根据 Name 查询 User
+func (us *UserService) QueryByUserName(ctx context.Context, name string) (*ent.User, error) {
+	return us.Dao.User.Query().Where(user.Name(name)).Only(ctx)
+}
diff --git a/app/service/user_operation_log.go b/app/service/user_operation_log.go
index 9101e66..aca7143 100644
--- a/app/service/user_operation_log.go
+++ b/app/service/user_operation_log.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/useroperationlog"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/useroperationlog"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type UserOperationLogService struct {
diff --git a/app/service/wire_set.go b/app/service/wire_set.go
index 18d53f3..c53e2ba 100644
--- a/app/service/wire_set.go
+++ b/app/service/wire_set.go
@@ -1,43 +1,38 @@
 package service
 
 import (
-	"meta/app/ent"
-
 	"github.com/google/wire"
+	"github.com/one-meta/meta/app/ent"
 )
 
 // Dao 所有的Dao都是ent Client
 type Dao = ent.Client
 
 var (
-	Set = wire.NewSet(
-		ProtectGroupSet, SpectrumAlertSet, SpectrumDataSet, StrategySet, TenantSet, UserSet,
-		CasbinRuleSet, CleanDataSet, GroupSet, NotifySet, WoFangSet, SocGroupTicketSet, SystemApiSet,
-		CloudAlertSet, WoFangAlertSet, CloudFlowDataSet, SkylineDosSet, CloudAttackDataSet, MatrixStrategySet,
-		MatrixSpectrumDataSet, MatrixSpectrumAlertSet, UserOperationLogSet, SystemConfigSet, DataSyncSet,
-	)
-	ProtectGroupSet        = wire.NewSet(wire.Struct(new(ProtectGroupService), "*"))
-	SpectrumAlertSet       = wire.NewSet(wire.Struct(new(SpectrumAlertService), "*"))
-	SpectrumDataSet        = wire.NewSet(wire.Struct(new(SpectrumDataService), "*"))
-	StrategySet            = wire.NewSet(wire.Struct(new(StrategyService), "*"))
-	TenantSet              = wire.NewSet(wire.Struct(new(TenantService), "*"))
-	UserSet                = wire.NewSet(wire.Struct(new(UserService), "*"))
-	CasbinRuleSet          = wire.NewSet(wire.Struct(new(CasbinRuleService), "*"))
-	CleanDataSet           = wire.NewSet(wire.Struct(new(CleanDataService), "*"))
+	Set                    = wire.NewSet(GroupSet, MatrixSpectrumAlertSet, ProtectGroupSet, SocGroupTicketSet, SpectrumAlertSet, SystemApiSet, SystemConfigSet, CleanDataSet, UserOperationLogSet, WofangAlertSet, UserSet, CloudFlowDataSet, MatrixStrategySet, StrategySet, CasbinRuleSet, DataSyncSet, TenantSet, WofangSet, CloudAlertSet, HelloUserSet, MatrixSpectrumDataSet, NotifySet, SkylineDosSet, SpectrumDataSet, CloudAttackDataSet)
 	GroupSet               = wire.NewSet(wire.Struct(new(GroupService), "*"))
-	NotifySet              = wire.NewSet(wire.Struct(new(NotifyService), "*"))
-	WoFangSet              = wire.NewSet(wire.Struct(new(WofangService), "*"))
+	MatrixSpectrumAlertSet = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertService), "*"))
+	ProtectGroupSet        = wire.NewSet(wire.Struct(new(ProtectGroupService), "*"))
 	SocGroupTicketSet      = wire.NewSet(wire.Struct(new(SocGroupTicketService), "*"))
+	SpectrumAlertSet       = wire.NewSet(wire.Struct(new(SpectrumAlertService), "*"))
 	SystemApiSet           = wire.NewSet(wire.Struct(new(SystemApiService), "*"))
-	CloudAlertSet          = wire.NewSet(wire.Struct(new(CloudAlertService), "*"))
+	SystemConfigSet        = wire.NewSet(wire.Struct(new(SystemConfigService), "*"))
+	CleanDataSet           = wire.NewSet(wire.Struct(new(CleanDataService), "*"))
+	UserOperationLogSet    = wire.NewSet(wire.Struct(new(UserOperationLogService), "*"))
+	WofangAlertSet         = wire.NewSet(wire.Struct(new(WofangAlertService), "*"))
+	UserSet                = wire.NewSet(wire.Struct(new(UserService), "*"))
 	CloudFlowDataSet       = wire.NewSet(wire.Struct(new(CloudFlowDataService), "*"))
-	CloudAttackDataSet     = wire.NewSet(wire.Struct(new(CloudAttackDataService), "*"))
-	WoFangAlertSet         = wire.NewSet(wire.Struct(new(WofangAlertService), "*"))
-	SkylineDosSet          = wire.NewSet(wire.Struct(new(SkylineDosService), "*"))
 	MatrixStrategySet      = wire.NewSet(wire.Struct(new(MatrixStrategyService), "*"))
-	MatrixSpectrumDataSet  = wire.NewSet(wire.Struct(new(MatrixSpectrumDataService), "*"))
-	MatrixSpectrumAlertSet = wire.NewSet(wire.Struct(new(MatrixSpectrumAlertService), "*"))
-	UserOperationLogSet    = wire.NewSet(wire.Struct(new(UserOperationLogService), "*"))
-	SystemConfigSet        = wire.NewSet(wire.Struct(new(SystemConfigService), "*"))
+	StrategySet            = wire.NewSet(wire.Struct(new(StrategyService), "*"))
+	CasbinRuleSet          = wire.NewSet(wire.Struct(new(CasbinRuleService), "*"))
 	DataSyncSet            = wire.NewSet(wire.Struct(new(DataSyncService), "*"))
+	TenantSet              = wire.NewSet(wire.Struct(new(TenantService), "*"))
+	WofangSet              = wire.NewSet(wire.Struct(new(WofangService), "*"))
+	CloudAlertSet          = wire.NewSet(wire.Struct(new(CloudAlertService), "*"))
+	HelloUserSet           = wire.NewSet(wire.Struct(new(HelloUserService), "*"))
+	MatrixSpectrumDataSet  = wire.NewSet(wire.Struct(new(MatrixSpectrumDataService), "*"))
+	NotifySet              = wire.NewSet(wire.Struct(new(NotifyService), "*"))
+	SkylineDosSet          = wire.NewSet(wire.Struct(new(SkylineDosService), "*"))
+	SpectrumDataSet        = wire.NewSet(wire.Struct(new(SpectrumDataService), "*"))
+	CloudAttackDataSet     = wire.NewSet(wire.Struct(new(CloudAttackDataService), "*"))
 )
diff --git a/app/service/wofang.go b/app/service/wofang.go
index 070381f..7e3f318 100644
--- a/app/service/wofang.go
+++ b/app/service/wofang.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/wofang"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/wofang"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type WofangService struct {
@@ -22,7 +22,7 @@ func (ws *WofangService) Query(ctx context.Context, w *ent.Wofang, qp *entity.Qu
 
 // QueryByID 根据 ID 查询 Wofang
 func (ws *WofangService) QueryByID(ctx context.Context, id int) (*ent.Wofang, error) {
-	return ws.Dao.Wofang.Query().Where(wofang.ID(id)).WithTenant().Only(ctx)
+	return ws.Dao.Wofang.Query().Where(wofang.ID(id)).Only(ctx)
 }
 
 // Create 创建 Wofang
@@ -61,7 +61,7 @@ func (ws *WofangService) QueryPage(ctx context.Context, w *ent.Wofang, qp *entit
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ws.Dao.Wofang.Query().QueryItemWofang(w, qp, false).WithTenant().All(ctx)
+	results, err := ws.Dao.Wofang.Query().QueryItemWofang(w, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (ws *WofangService) QuerySearch(ctx context.Context, w *ent.Wofang, qp *ent
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := ws.Dao.Wofang.Query().SearchWofang(w, qp, false).WithTenant().All(ctx)
+	results, err := ws.Dao.Wofang.Query().SearchWofang(w, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
diff --git a/app/service/wofang_alert.go b/app/service/wofang_alert.go
index 9072857..01c1484 100644
--- a/app/service/wofang_alert.go
+++ b/app/service/wofang_alert.go
@@ -2,9 +2,9 @@ package service
 
 import (
 	"context"
-	"meta/app/ent"
-	"meta/app/ent/wofangalert"
-	"meta/app/entity"
+	"github.com/one-meta/meta/app/ent"
+	"github.com/one-meta/meta/app/ent/wofangalert"
+	"github.com/one-meta/meta/app/entity"
 )
 
 type WofangAlertService struct {
@@ -22,7 +22,7 @@ func (was *WofangAlertService) Query(ctx context.Context, wa *ent.WofangAlert, q
 
 // QueryByID 根据 ID 查询 WofangAlert
 func (was *WofangAlertService) QueryByID(ctx context.Context, id int) (*ent.WofangAlert, error) {
-	return was.Dao.WofangAlert.Query().Where(wofangalert.ID(id)).WithTenant().Only(ctx)
+	return was.Dao.WofangAlert.Query().Where(wofangalert.ID(id)).Only(ctx)
 }
 
 // Create 创建 WofangAlert
@@ -61,7 +61,7 @@ func (was *WofangAlertService) QueryPage(ctx context.Context, wa *ent.WofangAler
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := was.Dao.WofangAlert.Query().QueryItemWofangAlert(wa, qp, false).WithTenant().All(ctx)
+	results, err := was.Dao.WofangAlert.Query().QueryItemWofangAlert(wa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
@@ -77,7 +77,7 @@ func (was *WofangAlertService) QuerySearch(ctx context.Context, wa *ent.WofangAl
 	if err != nil {
 		return 0, nil, err
 	}
-	results, err := was.Dao.WofangAlert.Query().SearchWofangAlert(wa, qp, false).WithTenant().All(ctx)
+	results, err := was.Dao.WofangAlert.Query().SearchWofangAlert(wa, qp, false).All(ctx)
 	if err != nil {
 		return 0, nil, err
 	}
