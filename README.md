## DDoS平台后端

使用[Task](https://taskfile.dev/zh-Hans/installation/)进行CI/CD

基于[Meta](https://github.com/one-meta/meta)的后端脚手架

基于[air](https://github.com/cosmtrek/air)go 程序热重启，可在开发中热重启

依赖：

1. [Task](https://taskfile.dev/zh-Hans/installation/)
2. go >= v1.21.6
3. docker 或者 podman

参考：[Meta框架联合使用说明](https://github.com/one-meta/meta/wiki/%E6%A1%86%E6%9E%B6%E8%81%94%E5%90%88%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E)

## 开发
项目根目录执行：`air`

### 运行

`task run`

### 编译linux exec

`task build` 或者 `task linux`

### 本地开发

#### 本地编译和运行

```bash
# 本地编译（生成本地可执行文件）
task build-local

# 本地运行（使用本地配置）
task run-local

# 或者使用 make（如果你更喜欢 make）
make build-local
make run-local
```

#### 本地 Docker 部署

```bash
# 使用 docker-compose 进行本地部署
task deploy-local

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 或者使用 make
make deploy-local
make logs
```

#### 本地开发特性

- 使用 `config_local.toml` 配置文件
- Docker 容器包含调试工具（bash, curl, wget, htop）
- 日志目录挂载到本地 `./data/logs`
- 配置文件挂载，可以实时修改配置
- 健康检查支持
- 自动重启策略

### 部署（远程CI/CD）

> 如果不是使用podman，需要修改Taskfile中的 `env.DOCKER_NAME`

测试环境：`task deploy-test` 或者 `task test`

生产环境：`task deploy-prod` 或者 `task prod`
