## DDoS平台前端

使用[Task](https://taskfile.dev/zh-Hans/installation/)进行CI/CD

基于[Meta-Front](https://github.com/one-meta/meta-front)魔改的[Antd Pro 6](https://pro.ant.design/zh-CN)的前端脚手架

依赖：

1. [Task](https://taskfile.dev/zh-Hans/installation/)
2. Nodejs >= v20.10.0，建议使用[nvm](https://github.com/nvm-sh/nvm)管理node多版本
3. docker 或者 podman
4. yarn

### 运行

`task run`

### 编译dist

`task build`

### 部署（本地CI/CD）

>  注意调整 `.nginx`目录下的 `proxy_pass`地址
>
> 如果不是使用podman，需要修改Taskfile中的 `env.DOCKER_NAME`

本地开发环境运行：`task deploy-dev` 或者 `task dev`

测试环境：`task deploy-test` 或者 `task test`

生产环境：`task deploy-prod` 或者 `task prod`
