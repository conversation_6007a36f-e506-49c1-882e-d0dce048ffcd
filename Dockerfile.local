FROM golang:1.21-alpine AS builder

EN<PERSON> CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct

# Install necessary packages for development
RUN apk add --no-cache tzdata git

WORKDIR /build

# Copy go mod files first for better caching
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Generate wire and build the application
RUN go install github.com/google/wire/cmd/wire@latest
RUN wire ./app
ENV CGO_ENABLED=0 GOOS=linux GOARCH=amd64
RUN go build -ldflags="-s -w" -o /build/meta .

# Final stage - use alpine for better debugging capabilities in local development
FROM alpine:latest

# Install useful tools for local development and debugging
RUN apk update --no-cache && \
    apk add --no-cache ca-certificates bash curl wget htop procps && \
    rm -rf /var/cache/apk/*

# Set timezone
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app

# Copy the binary
COPY --from=builder /build/meta /app/meta

# Copy configuration files
COPY --from=builder /build/resource/model.conf /app/resource/model.conf
COPY --from=builder /build/resource/config_local.toml /app/resource/config.toml

# Create logs directory
RUN mkdir -p /app/data/logs/web /app/data/logs/app

# Expose port
EXPOSE 9001

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9001/health || exit 1

ENTRYPOINT ["./meta"]
