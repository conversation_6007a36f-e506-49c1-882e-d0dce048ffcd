version: '3'
silent: true

env:
  DOCKER_NAME: "podman"

  DEV_IMG_URL: "ddos_ui"
  DEV_DOCKER_FILE: "Dockerfile_local"

  TEST_IMG_URL: "ncr.nie.netease.com/sesa/ddos_ui"
  TEST_DOCKER_FILE: "Dockerfile_local"

  PROD_IMG_URL: "ncr.nie.netease.com/sesa/ddos_ui:production"
  PROD_DOCKER_FILE: "Dockerfile_local_prod"
tasks:
  run:
    desc: Run project.
    cmds:
      - yarn start:dev
  build:
    desc: Build dist.
    cmds:
      - yarn build
  deploy-dev:
    deps: [ build ]
    desc: Build and run dev(local) docker image.
    cmds:
      - ${DOCKER_NAME} build -t ${DEV_IMG_URL} . -f ${DEV_DOCKER_FILE}
      - ${DOCKER_NAME} run --rm -it -p 8088:80 ${DEV_IMG_URL}
    aliases: [ dev ]  
  deploy-test:
    deps: [ build ]
    desc: Build and deploy test docker image.
    cmds:
      - ${DOCKER_NAME} build --platform linux/amd64 -t ${TEST_IMG_URL} . -f ${TEST_DOCKER_FILE}
      - ${DOCKER_NAME} push ${TEST_IMG_URL}
    aliases: [ test ]

  deploy-prod:
    deps: [ build ]
    desc: Build and deploy prod docker image.
    cmds:
      - ${DOCKER_NAME} build --platform linux/amd64 -t ${PROD_IMG_URL} . -f ${PROD_DOCKER_FILE}
      - ${DOCKER_NAME} push ${PROD_IMG_URL}
    aliases: [ prod ]