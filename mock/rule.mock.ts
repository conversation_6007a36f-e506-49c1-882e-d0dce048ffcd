// @ts-ignore
import { Request, Response } from 'express';

export default {
  'GET /api/rule': (req: Request, res: Response) => {
    res.status(200).send({
      data: [
        {
          key: 95,
          disabled: true,
          href: 'https://ant.design',
          avatar: '',
          name: '宋杰',
          owner: '<PERSON>',
          desc: '建取质好什明不指与农复参准。',
          callNo: 86,
          status: 91,
          updatedAt: '1jGG',
          createdAt: '(WRSb$',
          progress: 75,
        },
        {
          key: 75,
          disabled: true,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
          name: '周芳',
          owner: '<PERSON>',
          desc: '现在算每各热号斗者有天见完自值二。',
          callNo: 98,
          status: 70,
          updatedAt: 'f57o',
          createdAt: 'mtnR^',
          progress: 61,
        },
        {
          key: 61,
          disabled: false,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
          name: '方芳',
          owner: 'Jones',
          desc: '完相整置劳斗等去其成作共真应压发处把。',
          callNo: 77,
          status: 89,
          updatedAt: 'mpIhyk',
          createdAt: 'dDSGD7',
          progress: 66,
        },
        {
          key: 75,
          disabled: false,
          href: 'https://ant.design',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
          name: '龚超',
          owner: 'Walker',
          desc: '看结现因克战要界土清改太成子。',
          callNo: 79,
          status: 66,
          updatedAt: ']E%2UV',
          createdAt: 'eT0r',
          progress: 79,
        },
        {
          key: 84,
          disabled: false,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://avatars0.githubusercontent.com/u/507615?s=40&v=4',
          name: '金娟',
          owner: 'Taylor',
          desc: '本布到表制而建干按候但工按半三家。',
          callNo: 62,
          status: 85,
          updatedAt: 'TT5M[V8',
          createdAt: 'tUV*',
          progress: 67,
        },
        {
          key: 62,
          disabled: false,
          href: 'https://ant.design',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
          name: '董娜',
          owner: 'Lee',
          desc: '争问是识调酸联也半手她每及江资群二。',
          callNo: 90,
          status: 93,
          updatedAt: 'cnw0BVK',
          createdAt: '0fqt',
          progress: 92,
        },
        {
          key: 82,
          disabled: false,
          href: 'https://preview.pro.ant.design/dashboard/analysis',
          avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
          name: '顾军',
          owner: 'Gonzalez',
          desc: '确本子专收更识商叫研农因资。',
          callNo: 71,
          status: 87,
          updatedAt: '5!sqW',
          createdAt: 'H3#b',
          progress: 94,
        },
        {
          key: 98,
          disabled: false,
          href: 'https://umijs.org/',
          avatar: 'https://avatars0.githubusercontent.com/u/507615?s=40&v=4',
          name: '锺强',
          owner: 'Martin',
          desc: '立度段间问太住米进资众从存量儿。',
          callNo: 95,
          status: 89,
          updatedAt: 'Ci)Ig9d',
          createdAt: 'Kda',
          progress: 66,
        },
        {
          key: 68,
          disabled: false,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://avatars1.githubusercontent.com/u/8186664?s=40&v=4',
          name: '方丽',
          owner: 'Williams',
          desc: '起厂之问合要日府决技层形复传众样打。',
          callNo: 64,
          status: 89,
          updatedAt: 'RbF4u',
          createdAt: '#FD8DR[',
          progress: 75,
        },
        {
          key: 86,
          disabled: true,
          href: '',
          avatar: '',
          name: '马丽',
          owner: 'Williams',
          desc: '头主细有本别拉须行二二外。',
          callNo: 83,
          status: 86,
          updatedAt: '8bC7^',
          createdAt: 'z&#pGo',
          progress: 93,
        },
        {
          key: 73,
          disabled: false,
          href: 'https://preview.pro.ant.design/dashboard/analysis',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
          name: '石敏',
          owner: 'Hernandez',
          desc: '往力进所什为结现运去角于术解厂音大。',
          callNo: 93,
          status: 75,
          updatedAt: 'X%l4^L',
          createdAt: '&75kZm',
          progress: 88,
        },
        {
          key: 90,
          disabled: true,
          href: 'https://preview.pro.ant.design/dashboard/analysis',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
          name: '曹娟',
          owner: 'Anderson',
          desc: '对光并省思也间装布个质复农铁。',
          callNo: 69,
          status: 89,
          updatedAt: 'EpeB',
          createdAt: '#lr@W',
          progress: 69,
        },
        {
          key: 91,
          disabled: true,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://avatars0.githubusercontent.com/u/507615?s=40&v=4',
          name: '秦平',
          owner: 'Perez',
          desc: '究共住义济进总眼使些论改音西新而处。',
          callNo: 85,
          status: 81,
          updatedAt: '9OKvBC',
          createdAt: 'V9NH',
          progress: 71,
        },
        {
          key: 98,
          disabled: true,
          href: 'https://ant.design',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
          name: '侯伟',
          owner: 'Hall',
          desc: '类表长话报物取完育命白表期所。',
          callNo: 89,
          status: 98,
          updatedAt: 'X]UiF3M',
          createdAt: 'nirEm',
          progress: 75,
        },
        {
          key: 96,
          disabled: true,
          href: 'https://procomponents.ant.design/',
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
          name: '邓军',
          owner: 'Robinson',
          desc: '活山山千养片书广于经格接空存程叫。',
          callNo: 80,
          status: 100,
          updatedAt: 'ZrTRl',
          createdAt: '#ySjIl',
          progress: 61,
        },
      ],
      total: 80,
      success: true,
    });
  },
};
