// @ts-ignore
import { Request, Response } from 'express';

export default {
  'POST /api/rule': (req: Request, res: Response) => {
    res.status(200).send({
      key: 65,
      disabled: false,
      href: 'https://github.com/umijs/dumi',
      avatar: 'https://avatars1.githubusercontent.com/u/8186664?s=40&v=4',
      name: '唐艳',
      owner: '<PERSON>',
      desc: '个相识构习十电国时克位带时们十种系。',
      callNo: 87,
      status: 96,
      updatedAt: 'ke9A[h',
      createdAt: '0s4',
      progress: 79,
    });
  },
};
