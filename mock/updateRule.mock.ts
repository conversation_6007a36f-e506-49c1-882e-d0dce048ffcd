// @ts-ignore
import { Request, Response } from 'express';

export default {
  'PUT /api/rule': (req: Request, res: Response) => {
    res.status(200).send({
      key: 97,
      disabled: true,
      href: 'https://github.com/umijs/dumi',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
      name: '廖明',
      owner: '<PERSON>',
      desc: '战节万照正低记目间际率六铁更习权相委。',
      callNo: 88,
      status: 76,
      updatedAt: 'grqXfr',
      createdAt: ')(F',
      progress: 82,
    });
  },
};
