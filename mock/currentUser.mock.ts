// @ts-ignore
import { Request, Response } from 'express';

export default {
  'GET /api/currentUser': (req: Request, res: Response) => {
    res.status(200).send({
      name: '郑静',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
      userid: '18DeE95b-9fEC-71Ad-4eC6-83fCD7bADeB5',
      email: '<EMAIL>',
      signature: '市土院前边定好为半样积合图边党。',
      title: '划将住内正前起名响实看立。',
      group: '区块链平台部',
      tags: [
        { key: 1, label: '海纳百川' },
        { key: 2, label: '傻白甜' },
        { key: 3, label: '健身达人' },
        { key: 4, label: '健身达人' },
        { key: 5, label: '大长腿' },
        { key: 6, label: 'IT 互联网' },
        { key: 7, label: '很有想法的' },
        { key: 8, label: '程序员' },
        { key: 9, label: '程序员' },
        { key: 10, label: '健身达人' },
        { key: 11, label: 'IT 互联网' },
        { key: 12, label: '专注设计' },
      ],
      notifyCount: 80,
      unreadCount: 80,
      country: '印度尼西亚',
      access: '克去里现其而用广里每成因他改非。',
      geographic: {
        province: { label: '澳门特别行政区', key: 13 },
        city: { label: '宜兰县', key: 14 },
      },
      address: '山西省 晋中市 祁县',
      phone: '11246661465',
    });
  },
};
