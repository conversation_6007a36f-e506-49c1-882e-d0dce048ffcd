.PHONY: help build-local run-local deploy-local clean logs

# Default target
help:
	@echo "Available targets:"
	@echo "  build-local   - Build the application for local development"
	@echo "  run-local     - Run the application locally with local config"
	@echo "  deploy-local  - Deploy using docker-compose for local development"
	@echo "  logs          - Show docker-compose logs"
	@echo "  clean         - Clean up local build artifacts and containers"
	@echo "  help          - Show this help message"

# Build for local development
build-local:
	@echo "Building application for local development..."
	meta-g && wire ./app
	go build -ldflags="-s -w" -o meta main.go
	@echo "Build completed: ./meta"

# Run locally with local configuration
run-local:
	@echo "Running application with local configuration..."
	meta-g && wire ./app
	CONFIG_PATH=resource/config_local.toml go run main.go

# Deploy using docker-compose
deploy-local:
	@echo "Deploying application using docker-compose..."
	docker-compose down
	docker-compose build
	docker-compose up -d
	@echo "Application deployed. Check status with: docker-compose ps"
	@echo "View logs with: make logs"

# Show docker-compose logs
logs:
	docker-compose logs -f

# Clean up
clean:
	@echo "Cleaning up..."
	docker-compose down -v
	docker system prune -f
	rm -f meta
	@echo "Cleanup completed"

# Quick status check
status:
	@echo "Docker containers status:"
	docker-compose ps
	@echo ""
	@echo "Application health:"
	curl -s http://localhost:9001/health || echo "Application not responding"
