#!/bin/bash

# Development setup script for DDoS platform backend
# This script helps set up the local development environment

set -e

echo "🚀 Setting up DDoS platform backend for local development..."

# Check if required tools are installed
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    else
        echo "✅ $1 is installed"
    fi
}

echo "📋 Checking required tools..."
check_tool "go"
check_tool "docker"
check_tool "docker-compose"

# Check if task is installed
if command -v task &> /dev/null; then
    echo "✅ task is installed"
    USE_TASK=true
else
    echo "⚠️  task is not installed, will use make instead"
    USE_TASK=false
fi

# Check Go version
GO_VERSION=$(go version | grep -o 'go[0-9]\+\.[0-9]\+' | sed 's/go//')
REQUIRED_VERSION="1.21"
if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
    echo "✅ Go version $GO_VERSION is compatible (>= $REQUIRED_VERSION)"
else
    echo "❌ Go version $GO_VERSION is too old. Please upgrade to >= $REQUIRED_VERSION"
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data/logs/web
mkdir -p data/logs/app
mkdir -p tmp
echo "✅ Directories created"

# Install wire if not present
if ! command -v wire &> /dev/null; then
    echo "📦 Installing wire..."
    go install github.com/google/wire/cmd/wire@latest
    echo "✅ wire installed"
else
    echo "✅ wire is already installed"
fi

# Install meta-g if not present
if ! command -v meta-g &> /dev/null; then
    echo "📦 Installing meta-g..."
    go install github.com/one-meta/meta/cmd/meta-g@latest
    echo "✅ meta-g installed"
else
    echo "✅ meta-g is already installed"
fi

# Download dependencies
echo "📦 Downloading Go dependencies..."
go mod download
echo "✅ Dependencies downloaded"

# Generate wire files
echo "🔧 Generating wire files..."
meta-g && wire ./app
echo "✅ Wire files generated"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📖 Available commands:"
if [ "$USE_TASK" = true ]; then
    echo "  task build-local   - Build for local development"
    echo "  task run-local     - Run with local configuration"
    echo "  task deploy-local  - Deploy using docker-compose"
else
    echo "  make build-local   - Build for local development"
    echo "  make run-local     - Run with local configuration"
    echo "  make deploy-local  - Deploy using docker-compose"
fi
echo ""
echo "🔗 Quick start:"
if [ "$USE_TASK" = true ]; then
    echo "  task run-local     # Run locally"
else
    echo "  make run-local     # Run locally"
fi
echo "  # or"
if [ "$USE_TASK" = true ]; then
    echo "  task deploy-local  # Run in Docker"
else
    echo "  make deploy-local  # Run in Docker"
fi
echo ""
echo "📝 The application will be available at: http://localhost:9001"
echo "📊 Swagger documentation: http://localhost:9001/swagger"
