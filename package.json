{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.8.0", "@ant-design/pro-card": "^2.8.8", "@ant-design/pro-components": "^2.3.52", "@ant-design/use-emotion-css": "1.0.4", "@types/http-proxy-middleware": "^1.0.0", "@umijs/route-utils": "^4.0.1", "antd": "^5.1.4", "classnames": "^2.3.2", "http-proxy-middleware": "^3.0.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "omit.js": "^2.0.2", "rc-menu": "^9.8.1", "rc-util": "^5.27.1", "react": "^18.2.0", "react-dev-inspector": "^2.0.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.3", "react-json-view": "^1.21.3"}, "devDependencies": {"@ant-design/pro-cli": "^3.1.0", "@testing-library/react": "^14.1.2", "@types/classnames": "^2.3.1", "@types/express": "^4.17.15", "@types/history": "^5.0.0", "@types/jest": "^29.2.5", "@types/lodash": "^4.14.191", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@types/react-helmet": "^6.1.6", "@umijs/lint": "^4.0.44", "@umijs/max": "^4.0.44", "cross-env": "^7.0.3", "eslint": "^8.31.0", "express": "^4.18.2", "gh-pages": "^6.1.0", "husky": "^8.0.3", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "lint-staged": "^15.2.0", "mockjs": "^1.1.0", "prettier": "^3.1.1", "swagger-ui-dist": "^5.10.5", "ts-node": "^10.9.1", "typescript": "^5.3.3", "umi-presets-pro": "^2.0.2"}, "engines": {"node": ">=12.0.0"}}